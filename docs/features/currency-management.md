# Currency Management Feature

This document describes the Currency Management feature implemented for User Stories US-PS-006 and US-PS-007.

## Overview

The Currency Management feature allows property owners to:
1. Create and manage custom currencies (US-PS-006)
2. Assign currencies to properties for proper financial tracking (US-PS-007)

## Features Implemented

### US-PS-006: Custom Currency Management

#### Backend Implementation

**Currency Model** (`packages/backend/models/Currency.ts`)
- Fields: `name`, `symbol`, `code` (optional), `createdBy`
- Unique constraints per user for name, symbol, and code
- Validation for currency code format (3 uppercase letters)
- Static method to create default currencies for new users
- Instance method to check if currency is being used by properties

**Currency Routes** (`packages/backend/routes/currencies.ts`)
- `GET /api/currencies` - List all user currencies
- `GET /api/currencies/:id` - Get specific currency
- `POST /api/currencies` - Create new currency
- `PUT /api/currencies/:id` - Update currency
- `DELETE /api/currencies/:id` - Delete currency (only if not in use)

**Default Currencies**
- Automatically created for new users during registration
- Includes: US Dollar ($, USD), Euro (€, EUR), British Pound (£, GBP)

#### Frontend Implementation

**Currency Hook** (`packages/frontend/hooks/useCurrencies.ts`)
- Manages currency state and API interactions
- Functions: `createCurrency`, `updateCurrency`, `deleteCurrency`, `refetch`
- Error handling and loading states

**Currency Management Page** (`packages/frontend/components/currencies/CurrenciesPage.tsx`)
- Full CRUD interface for currencies
- Form validation and error handling
- Confirmation dialogs for deletion
- Responsive design with modal forms

### US-PS-007: Property Currency Assignment

#### Backend Implementation

**Property Model Updates**
- Added `currencyId` field as optional reference to Currency model
- Updated property routes to populate currency information
- Support for currency assignment in create/update operations

**Property Routes Updates**
- All property endpoints now populate currency data
- Currency information included in property responses

#### Frontend Implementation

**Property Form Updates**
- Added currency selection dropdown in property creation/editing
- Displays currency symbol, name, and code for easy selection
- Optional field with fallback to no currency

**Property Display Updates**
- Properties now show associated currency information
- Currency data available for financial calculations

## API Endpoints

### Currency Management

```
GET    /api/currencies           # List user currencies
GET    /api/currencies/:id       # Get specific currency
POST   /api/currencies           # Create currency
PUT    /api/currencies/:id       # Update currency
DELETE /api/currencies/:id       # Delete currency
```

### Request/Response Examples

**Create Currency**
```json
POST /api/currencies
{
  "name": "US Dollar",
  "symbol": "$",
  "code": "USD"
}

Response:
{
  "_id": "currency_id",
  "name": "US Dollar",
  "symbol": "$",
  "code": "USD",
  "createdBy": "user_id",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

**Property with Currency**
```json
GET /api/properties/:id
Response:
{
  "_id": "property_id",
  "name": "Downtown Apartment",
  "address": "123 Main St",
  "currencyId": "currency_id",
  "currency": {
    "_id": "currency_id",
    "name": "US Dollar",
    "symbol": "$",
    "code": "USD"
  },
  // ... other property fields
}
```

## Validation Rules

### Currency Validation
- **Name**: Required, 1-50 characters, unique per user
- **Symbol**: Required, 1-3 characters, unique per user
- **Code**: Optional, exactly 3 uppercase letters, unique per user

### Business Rules
- Users can only manage their own currencies
- Currencies cannot be deleted if used by properties
- Default currencies are created automatically for new users
- Currency assignment to properties is optional

## Error Handling

### Common Error Responses
- `400` - Validation errors, duplicate currency data
- `401` - Authentication required
- `404` - Currency not found or access denied
- `500` - Server errors

### Frontend Error Handling
- Form validation with real-time feedback
- API error messages displayed to users
- Graceful degradation when currency data unavailable

## Testing

### Backend Tests
- **Currency Model Tests** (`packages/backend/tests/currency.test.ts`)
  - Model validation and constraints
  - Default currency creation
  - Usage detection for properties
  
- **Currency Routes Tests** (`packages/backend/tests/routes/currencies.routes.test.ts`)
  - CRUD operations
  - Authentication and authorization
  - Error handling scenarios

### Frontend Tests
- **Currency Hook Tests** (`packages/frontend/tests/hooks/useCurrencies.test.tsx`)
  - State management and API interactions
  - Error handling and loading states
  
- **Currency Page Tests** (`packages/frontend/tests/components/currencies/CurrenciesPage.test.tsx`)
  - UI interactions and form handling
  - CRUD operations through UI
  - Error display and user feedback

## Usage Instructions

### For Users

1. **Access Currency Management**
   - Navigate to "Currencies" in the sidebar
   - View existing currencies or create new ones

2. **Create New Currency**
   - Click "Add Currency" button
   - Fill in currency name (required) and symbol (required)
   - Optionally add 3-letter currency code
   - Click "Create" to save

3. **Edit Currency**
   - Click "Edit" next to any currency
   - Modify name, symbol, or code
   - Click "Update" to save changes

4. **Delete Currency**
   - Click "Delete" next to currency
   - Confirm deletion (only works if not used by properties)

5. **Assign Currency to Property**
   - When creating/editing a property
   - Select currency from dropdown (optional)
   - Currency will be used for all financial displays

### For Developers

1. **Adding Currency Support to New Features**
   ```typescript
   // Import currency types
   import { ICurrency } from '@alom-rentals/shared';
   
   // Use currency in components
   const { currencies } = useCurrencies();
   
   // Display currency symbol
   {property.currency?.symbol || '$'} {amount}
   ```

2. **Backend Currency Queries**
   ```typescript
   // Populate currency in queries
   .populate('currencyId', 'name symbol code')
   
   // Check currency usage
   const isInUse = await currency.isInUse();
   ```

## Future Enhancements

1. **Currency Conversion**
   - Add exchange rate management
   - Multi-currency reporting
   - Historical rate tracking

2. **Advanced Features**
   - Currency-specific formatting rules
   - Regional currency defaults
   - Bulk currency operations

3. **Integration**
   - Property service cost calculations
   - Rental amount displays
   - Financial reporting by currency

## Migration Notes

- Existing properties without currency assignment will continue to work
- Default currencies are created for existing users on first login
- No breaking changes to existing API endpoints

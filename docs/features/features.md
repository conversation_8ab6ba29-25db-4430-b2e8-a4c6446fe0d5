# Alom Rentals - Complete Features Inventory

This document provides a comprehensive inventory of all implemented features in the Alom Rentals application. This serves as a reference for validation when implementing new features.

## 🏗️ Architecture Overview

- **Monorepo Structure**: Yarn workspaces with shared types and schemas
- **Backend**: Hono API server with TypeScript and MongoDB
- **Frontend**: React with TypeScript, React Router, and TanStack Query
- **Shared Package**: Common types, schemas, and validation rules

## 🔐 Authentication & User Management

### User Authentication
- **User Registration**: Email, password, full name validation
- **User Login**: JWT-based authentication
- **Password Security**: bcrypt hashing
- **Session Management**: JWT tokens with 7-day expiration
- **Route Protection**: Auth middleware for protected routes
- **Auto-redirect**: Authenticated users redirect from auth page

### User Model
- **Fields**: `_id`, `email`, `password`, `fullName`, `createdAt`, `updatedAt`
- **Validation**: Email uniqueness, password minimum 6 characters
- **Public Interface**: Excludes password in public responses

## 🏠 Property Management

### Property CRUD Operations
- **Create Property**: Name, address, description, type, rental status
- **View Properties**: List view with property cards
- **Update Property**: Edit property details
- **Property Types**: Residential, commercial, land, other
- **Rental Status**: Rented/vacant indicator

### Property Ownership System
- **Multi-owner Support**: Properties can have multiple owners
- **Owner Types**: Users (registered) or Partners (non-registered)
- **Ownership Percentages**: Must sum to 100%
- **Owner Validation**: Automatic user/partner resolution
- **Ownership Display**: Shows all owners with percentages

### Property Currency Assignment
- **Currency Selection**: Each property can have assigned currency
- **Default Currency**: Falls back to user's default if not specified
- **Currency Display**: Shows currency symbol and code in property views

## 👥 Partner Management

### Partner CRUD Operations
- **Create Partner**: Name, email for non-registered property co-owners
- **View Partners**: List all partners with search functionality
- **Update Partner**: Edit partner information
- **Delete Partner**: Remove partners (with usage validation)

### Partner Integration
- **Property Ownership**: Partners can own property percentages
- **User Conversion**: Partners can register and become users
- **Relationship Tracking**: Links partners to properties and services

## 👤 Guest Management

### Guest CRUD Operations
- **Create Guest**: Comprehensive guest information
- **View Guests**: List with search and filtering
- **Update Guest**: Edit guest details
- **Delete Guest**: Remove guests (with rental validation)

### Guest Information System
- **Personal Details**: First name, last name, date of birth, email
- **Document Management**: Document type and number (National ID, Visa, Passport, Foreigner ID)
- **Contact Information**: Phone number, relationship with owner
- **Foreigner Support**: Nationality, country of origin, next destination
- **Local Guest Support**: City of residency, city of birth, trip motivation
- **Age Validation**: Must be 18 or older

## 💰 Currency Management

### Currency CRUD Operations
- **Create Currency**: Name, symbol, optional 3-letter code
- **View Currencies**: List all user currencies
- **Update Currency**: Edit currency details
- **Delete Currency**: Remove unused currencies

### Default Currency System
- **Auto-creation**: Default currencies (USD, EUR, GBP) created for new users
- **Usage Validation**: Prevents deletion of currencies in use
- **Unique Constraints**: Name and code unique per user

## 🏢 Property Services Management

### Service CRUD Operations
- **Create Service**: Name, cost, frequency, payment responsibility
- **View Services**: List services per property
- **Update Service**: Edit service details
- **Delete Service**: Remove services

### Service Configuration
- **Frequency Options**: Monthly, quarterly, yearly
- **Status Management**: Active/inactive services
- **Payment Responsibility**: User-paid, partner-paid, or both
- **Mandatory Services**: Mark essential services
- **Cost Calculation**: Automatic monthly equivalent calculation

### Service Cost Summary
- **Total Monthly Cost**: Sum of all active services
- **User Monthly Cost**: Services paid by user
- **Partner Monthly Cost**: Services paid by partners
- **Mandatory Monthly Cost**: Essential services cost
- **Service Counts**: Active vs total services

## 🏨 Short-Term Rental Management

### Rental CRUD Operations
- **Create Rental**: Property, dates, amount, cost, guests
- **View Rentals**: Table and calendar views
- **Update Rental**: Edit rental details
- **Delete Rental**: Remove rentals with commission cleanup

### Rental Configuration
- **Date Validation**: Start date must be today or future, end after start
- **Overlap Prevention**: Comprehensive validation prevents overlapping active/confirmed rentals per property
- **Reference Numbers**: Auto-generated unique identifiers
- **Status Management**: Active, ended, cancelled
- **Payment Tracking**: Paid/unpaid status with payment date
- **Validation Rules**: Robust server-side validation with proper error handling
- **Financial Validation**: Total discounts and commissions cannot exceed profit (rental amount - cost amount)
- **Discount Management**: Discount reason required when discount amount is provided

### Guest Management in Rentals
- **Multiple Guests**: Support for multiple guests per rental
- **Principal Contact**: One guest designated as main contact
- **Guest Validation**: At least one guest required, principal contact validation
- **Guest Creation**: Create new guests inline during rental creation
- **Guest Association**: Link existing guests by ID or create new guest objects
- **Flexible Guest Input**: Support for both existing guest references and new guest data

### Rental Cost Calculation
- **Auto-calculation**: Cost from active property services
- **Manual Override**: Editable calculated costs
- **Discount Support**: Apply discounts with reasons
- **Profit Calculation**: Rental amount - cost - discount

### Commission System
- **Multiple Commissions**: Support for multiple commissions per rental
- **Commission Types**: Percentage or fixed amount
- **Commission Recipients**: Users or partners
- **Commission Status**: Pending or paid with payment tracking
- **Commission Calculation**: Automatic percentage calculations
- **Commission Validation**: Total commissions + discounts cannot exceed profit

### Rental List Display
- **Enhanced Financial Columns**: Separate display for rental amount, cost, discounts, commissions, and net profit
- **Visual Financial Indicators**:
  - Discounts shown in red when present
  - Commissions displayed in orange with calculated totals
  - Net profit color-coded (green for positive, red for negative)
- **Comprehensive Information**: Property details, guest information, dates, and complete financial breakdown
- **Status Filtering**: Filter rentals by active, ended, cancelled status
- **Responsive Design**: Mobile-friendly table layout with proper column management

## 📅 Calendar Features

### Rental Calendar View
- **Monthly Calendar**: Full month view with rental overlays
- **Rental Display**: Property name, guest name, amount per day
- **Status Color Coding**: Green (active), blue (ended), red (cancelled)
- **Check-in/Check-out Indicators**: Visual markers for start/end dates
- **Month Navigation**: Previous/next month navigation
- **Property Filtering**: Filter rentals by specific properties

### Calendar Interactions
- **Enhanced Rental Details Modal**:
  - Large, scrollable modal (3xl size) with improved UX
  - Maximum height constraint (90% of viewport) with internal scrolling
  - Comprehensive rental information display
  - Fixed header and footer for better navigation
- **Edit Navigation**: Direct navigation from modal to edit form
- **Availability Visualization**: Clear view of available dates
- **Multi-day Rental Display**: Rentals span across multiple days
- **Responsive Modal Design**: Adapts to different screen sizes while maintaining usability

## 📊 Dashboard & Analytics

### Dashboard Statistics
- **Property Count**: Total properties owned
- **Monthly Income**: Current month rental income
- **Pending Debts**: Unpaid rental costs
- **Net Income**: Monthly income minus costs
- **Yearly Metrics**: Total rentals and revenue for current year
- **Average Revenue**: Monthly average calculations

### Monthly Rental Analytics
- **Monthly Breakdown**: Revenue, costs, net income by month
- **Rental Counts**: Number of rentals per month
- **Visual Charts**: Bar charts and line graphs
- **Trend Analysis**: Performance over time

### Quick Actions
- **Property Creation**: Direct link to add new property
- **Rental Creation**: Direct link to create rental
- **Guest Registration**: Direct link to add guest

## 🎨 User Interface Components

### Reusable UI Components
- **Forms**: Validated forms with error handling
- **Tables**: Sortable, searchable data tables
- **Modals**: Confirmation dialogs and detail views
- **Cards**: Property and rental display cards
- **Buttons**: Consistent button styling and states
- **Loading States**: Spinners and skeleton loading
- **Error Handling**: User-friendly error messages

### Navigation & Routing
- **Sidebar Navigation**: Persistent navigation menu
- **Breadcrumbs**: Clear navigation hierarchy
- **URL State Management**: Query parameters for filters and views
- **Protected Routes**: Authentication-based route access
- **Dynamic Routing**: Parameter-based page routing

## 🔍 Search & Filtering

### Search Functionality
- **Property Search**: Search by name and address
- **Guest Search**: Search by name and email
- **Partner Search**: Search by name and email
- **Rental Search**: Search by property and guest names

### Filtering Options
- **Status Filters**: Filter by rental status, service status
- **Property Filters**: Filter rentals by specific properties
- **Date Filters**: Filter by date ranges (implicit in calendar)

## 📱 Responsive Design

### Mobile Support
- **Responsive Layout**: Adapts to different screen sizes
- **Touch-friendly**: Appropriate touch targets
- **Mobile Navigation**: Collapsible sidebar on mobile
- **Form Optimization**: Mobile-friendly form inputs

## 🔒 Data Validation & Security

### Input Validation
- **Zod Schemas**: Comprehensive validation rules with discriminated unions
- **Frontend Validation**: Real-time form validation
- **Backend Validation**: Server-side validation enforcement
- **Type Safety**: TypeScript throughout the stack
- **Advanced Schema Validation**:
  - Discriminated unions for complex data structures
  - Guest validation supporting both existing guest IDs and new guest objects
  - Proper error messages for validation failures
  - Schema composition for reusable validation patterns

### Security Features
- **Authentication Required**: All operations require authentication
- **User Isolation**: Users can only access their own data
- **Input Sanitization**: Trim and validate all inputs
- **SQL Injection Prevention**: MongoDB with proper queries

## 🧪 Testing Infrastructure

### Test Coverage
- **API Route Tests**: Comprehensive backend testing
- **Authentication Tests**: Login, registration, and auth middleware
- **Model Tests**: Database model validation
- **Integration Tests**: End-to-end API testing
- **Short-Term Rental Tests**: Complete test suite covering:
  - Rental creation with guest validation
  - Overlapping rental prevention
  - Cost calculation from property services
  - CRUD operations (create, read, update, delete)
  - Authentication and authorization
  - Error handling and validation

## 📦 Data Models Summary

### Core Models
- **User**: Authentication and profile
- **Property**: Real estate properties with ownership
- **Partner**: Non-registered property co-owners
- **Guest**: Rental guests with document management
- **Currency**: Multi-currency support
- **PropertyService**: Property-related services and costs
- **ShortTermRental**: Rental bookings and management
- **Commission**: Rental commission tracking

### Relationships
- **User → Properties**: One-to-many (creator)
- **Property → Owners**: Many-to-many (users and partners)
- **Property → Services**: One-to-many
- **Property → Rentals**: One-to-many
- **Rental → Guests**: Many-to-many
- **Rental → Commissions**: One-to-many

This inventory serves as the foundation for understanding the current system capabilities and ensuring new features integrate properly with existing functionality.

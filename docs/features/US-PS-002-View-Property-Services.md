# US-PS-002: View Property Services

## Overview

The View Property Services feature allows property owners to view and manage all services associated with their properties. This feature provides a comprehensive overview of service costs, payment responsibilities, and enables filtering and searching capabilities.

## User Story

**As a** property owner  
**I want to** view all services associated with a property  
**So that** I can see the complete cost breakdown

## Features

### 1. Service List Display

- **Complete Service Overview**: View all services for a selected property in an organized list
- **Service Information**: Each service displays:
  - Service name
  - Cost amount with currency formatting
  - Payment frequency (monthly, quarterly, yearly)
  - Status (active/inactive)
  - Payment responsibility (user, partner, or both)
  - Mandatory service indicator

### 2. Cost Summary Dashboard

- **Total Monthly Cost**: Shows the monthly equivalent of all active services
- **Payment Breakdown**: Displays costs split by:
  - User payments
  - Partner payments
  - Mandatory services
- **Service Counts**: Shows active vs total service counts

### 3. Service Organization

- **Status Grouping**: Services are automatically grouped by status
  - Active services displayed first
  - Inactive services shown separately
- **Visual Indicators**: Clear visual distinction between active and inactive services

### 4. Filtering and Search

- **Status Filter**: Filter services by:
  - All services
  - Active only
  - Inactive only
- **Name Search**: Search services by name (case-insensitive, partial matching)
- **Real-time Filtering**: Results update immediately as you type

### 5. Monthly Equivalent Calculations

- **Automatic Conversion**: Non-monthly services show their monthly equivalent
  - Quarterly: cost ÷ 3
  - Yearly: cost ÷ 12
- **Accurate Budgeting**: Helps with monthly budget planning

## How to Use

### Accessing Service View

1. Navigate to the Properties section
2. Select a property from your property list
3. Click on the "Services" tab or section
4. The service list will load automatically

### Viewing Service Details

Each service card displays:
- **Service Name**: Clear identification of the service
- **Cost**: Original cost with currency symbol
- **Frequency**: How often the payment occurs
- **Status Badge**: Green for active, gray for inactive
- **Monthly Equivalent**: For non-monthly services (if applicable)
- **Payment Icons**: Visual indicators showing who pays (user/partner)
- **Mandatory Shield**: Orange shield icon for mandatory services

### Using Filters

**Status Filter:**
1. Click the status dropdown (defaults to "All Services")
2. Select "Active Only" to see only active services
3. Select "Inactive Only" to see only inactive services
4. Select "All Services" to see everything

**Search:**
1. Click in the search box (with magnifying glass icon)
2. Type any part of a service name
3. Results filter automatically as you type
4. Clear the search box to see all services again

### Understanding the Cost Summary

The cost summary cards show:
- **Total Monthly Cost**: Sum of all active services (converted to monthly)
- **User Pays**: Monthly amount the user is responsible for
- **Partner Pays**: Monthly amount partners are responsible for
- **Mandatory**: Monthly amount for mandatory services

## Technical Implementation

### Frontend Components

- **ServiceList**: Main container component managing state and layout
- **ServiceItem**: Individual service display component
- **Cost Summary Cards**: Dashboard showing financial overview

### Backend API

- **GET /api/properties/:id/services**: Retrieve all services for a property
- **PropertyService.calculateCostSummary()**: Calculate monthly cost equivalents

### Data Flow

1. User selects a property
2. Frontend fetches services via API
3. Services are grouped by status
4. Cost summary is calculated
5. Filters and search are applied client-side for performance

## Testing

### Unit Tests

- **ServiceList Component**: Tests filtering, searching, and display logic
- **ServiceItem Component**: Tests individual service rendering and interactions
- **Backend API**: Tests service retrieval and cost calculations

### Test Coverage

- ✅ Service list display
- ✅ Individual service information display
- ✅ Status grouping (active first)
- ✅ Cost summary calculations
- ✅ Status filtering
- ✅ Name searching
- ✅ Monthly equivalent calculations
- ✅ Payment responsibility display
- ✅ Empty states
- ✅ Loading states

### Manual Testing Scenarios

1. **Basic Viewing**:
   - Create a property with multiple services
   - Verify all services are displayed
   - Check that all required information is shown

2. **Status Grouping**:
   - Create both active and inactive services
   - Verify active services appear first
   - Check visual distinction between statuses

3. **Cost Summary**:
   - Create services with different frequencies
   - Verify monthly equivalents are calculated correctly
   - Check payment responsibility breakdown

4. **Filtering**:
   - Test status filter with all options
   - Verify search functionality with various terms
   - Test combined filtering (search + status)

5. **Edge Cases**:
   - Test with no services
   - Test with only active services
   - Test with only inactive services
   - Test with very long service names

## Accessibility

- **Keyboard Navigation**: All interactive elements are keyboard accessible
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Color Contrast**: Status indicators meet WCAG guidelines
- **Focus Management**: Clear focus indicators for all interactive elements

## Performance Considerations

- **Client-side Filtering**: Search and filter operations are performed client-side for instant feedback
- **Efficient Rendering**: Services are rendered using React keys for optimal updates
- **Lazy Loading**: Cost summary is only calculated when services exist

## Future Enhancements

- **Export Functionality**: Export service lists to CSV/PDF
- **Service Categories**: Group services by category (utilities, insurance, etc.)
- **Cost Trends**: Historical cost tracking and trend analysis
- **Bulk Operations**: Select multiple services for bulk actions
- **Advanced Filters**: Filter by cost range, payment responsibility, etc.

## Related Features

- **US-PS-001**: Add Property Services
- **US-PS-003**: Edit Property Services  
- **US-PS-004**: Delete Property Services
- **Property Management**: Core property functionality

version: '3.8'

services:
  mongodb:
    image: mongo:7.0
    container_name: alomrentals-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: property-finance
    volumes:
      - mongodb_data:/data/db
      - mongodb_config:/data/configdb
    networks:
      - alomrentals-network

  mongo-express:
    image: mongo-express:1.0.2
    container_name: alomrentals-mongo-express
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: password123
      ME_CONFIG_MONGODB_URL: *****************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
      ME_CONFIG_MONGODB_SERVER: mongodb
    depends_on:
      - mongodb
    networks:
      - alomrentals-network

volumes:
  mongodb_data:
    driver: local
  mongodb_config:
    driver: local

networks:
  alomrentals-network:
    driver: bridge

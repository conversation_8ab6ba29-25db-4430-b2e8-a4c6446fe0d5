# Docker Setup for alomrentals

This project includes Docker Compose configuration for MongoDB and Mongo Express to simplify development setup.

## Services

### MongoDB

- **Port**: 27017
- **Username**: admin
- **Password**: password123
- **Database**: property-finance
- **Container Name**: alomrentals-mongodb

### Mongo Express (Web UI)

- **Port**: 8081
- **URL**: http://localhost:8081
- **Username**: admin
- **Password**: admin123
- **Container Name**: alomrentals-mongo-express

## Quick Start

### 1. Start Docker Services

```bash
npm run docker:up
```

### 2. Update Environment Variables

Copy the example environment file and update MongoDB URI:

```bash
cp .env.example .env
```

Make sure your `.env` file contains:

```env
MONGODB_URI=*****************************************************************************
```

### 3. Start the Application

```bash
npm run dev
```

### 4. Access Mongo Express

Open http://localhost:8081 in your browser to manage your MongoDB database.

## Available Scripts

| Script                   | Description                                          |
| ------------------------ | ---------------------------------------------------- |
| `npm run docker:up`      | Start MongoDB and Mongo Express containers           |
| `npm run docker:down`    | Stop and remove containers                           |
| `npm run docker:logs`    | View container logs                                  |
| `npm run docker:restart` | Restart containers                                   |
| `npm run docker:clean`   | Stop containers and remove volumes (⚠️ deletes data) |

## Data Persistence

MongoDB data is persisted in Docker volumes:

- `mongodb_data`: Database files
- `mongodb_config`: Configuration files

Data will persist between container restarts unless you run `npm run docker:clean`.

## Troubleshooting

### Port Conflicts

If ports 27017 or 8081 are already in use:

1. Stop the conflicting services
2. Or modify the ports in `docker-compose.yml`

### Connection Issues

1. Ensure Docker is running
2. Check if containers are up: `docker ps`
3. View logs: `npm run docker:logs`

### Reset Database

To completely reset the database:

```bash
npm run docker:clean
npm run docker:up
```

## Production Notes

⚠️ **Important**: This Docker setup is for development only. For production:

- Change default passwords
- Use environment variables for secrets
- Configure proper networking
- Set up backup strategies
- Use MongoDB Atlas or managed database service

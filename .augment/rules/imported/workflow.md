---
type: "manual"
---

# Process to implement User stories

- Read the file "features" located in ./docs/features/features.md to know what we currently support

- Read the codebase to understand our code styled, way of working and folder structure.

- Do the changes required to achieve the goals.

- Update the features file with the changes imlemented.

- Add tests to all files added that required tests like Libraries, Components, Functions, etc.

- Before finishing run tests, build and lint for possible issues.

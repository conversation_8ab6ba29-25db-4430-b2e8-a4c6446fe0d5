import { z } from 'zod';

export const currencyCreateSchema = z.object({
  name: z.string().min(1, 'Currency name is required').max(50, 'Currency name must be less than 50 characters'),
  symbol: z.string().min(1, 'Currency symbol is required').max(3, 'Currency symbol must be 3 characters or less'),
  code: z
    .string()
    .length(3, 'Currency code must be exactly 3 characters')
    .regex(/^[A-Z]{3}$/, 'Currency code must be 3 uppercase letters')
    .optional(),
});

export const currencyUpdateSchema = z.object({
  name: z.string().min(1, 'Currency name is required').max(50, 'Currency name must be less than 50 characters').optional(),
  symbol: z.string().min(1, 'Currency symbol is required').max(3, 'Currency symbol must be 3 characters or less').optional(),
  code: z
    .string()
    .length(3, 'Currency code must be exactly 3 characters')
    .regex(/^[A-Z]{3}$/, 'Currency code must be 3 uppercase letters')
    .optional(),
});

export const currencyParamsSchema = z.object({
  id: z.string().min(1, 'Currency ID is required'),
});

export type CurrencyCreateInput = z.infer<typeof currencyCreateSchema>;
export type CurrencyUpdateInput = z.infer<typeof currencyUpdateSchema>;
export type CurrencyParamsInput = z.infer<typeof currencyParamsSchema>;

import { z } from 'zod';

export const userRoleSchema = z.enum(['ADMIN', 'OPERATOR', 'CUSTOMER']);
export const accountStatusSchema = z.enum(['ACTIVE', 'INACTIVE', 'SUSPENDED']);

export const emailSchema = z.string().email('Invalid email format').min(1, 'Email is required');

export const passwordSchema = z.string().min(6, 'Password must be at least 6 characters');

export const nameSchema = z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters');

export const loginSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
});

export const registerSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  fullName: nameSchema,
});

export const userParamsSchema = z.object({
  id: z.string().min(1, 'User ID is required'),
});

// Role management schemas
export const roleChangeSchema = z.object({
  role: userRoleSchema,
});

export const bulkRoleChangeSchema = z.object({
  userIds: z.array(z.string().min(1, 'User ID is required')).min(1, 'At least one user ID is required'),
  role: userRoleSchema,
});

// User account management schemas
export const userCreateSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  fullName: nameSchema,
  role: userRoleSchema,
});

export const accountStatusChangeSchema = z.object({
  status: accountStatusSchema,
});

export type LoginInput = z.infer<typeof loginSchema>;
export type RegisterInput = z.infer<typeof registerSchema>;
export type UserParamsInput = z.infer<typeof userParamsSchema>;
export type RoleChangeInput = z.infer<typeof roleChangeSchema>;
export type BulkRoleChangeInput = z.infer<typeof bulkRoleChangeSchema>;
export type UserCreateInput = z.infer<typeof userCreateSchema>;
export type AccountStatusChangeInput = z.infer<typeof accountStatusChangeSchema>;

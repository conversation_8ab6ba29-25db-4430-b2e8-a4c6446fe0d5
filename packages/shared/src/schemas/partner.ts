import { z } from 'zod';
import { emailSchema, nameSchema } from './user';

export const partnerCreateSchema = z.object({
  email: emailSchema,
  name: nameSchema,
});

export const partnerUpdateSchema = z.object({
  email: emailSchema.optional(),
  name: nameSchema.optional(),
});

export const partnerParamsSchema = z.object({
  id: z.string().min(1, 'Partner ID is required'),
});

export type PartnerCreateInput = z.infer<typeof partnerCreateSchema>;
export type PartnerUpdateInput = z.infer<typeof partnerUpdateSchema>;
export type PartnerParamsInput = z.infer<typeof partnerParamsSchema>;

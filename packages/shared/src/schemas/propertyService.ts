import { z } from 'zod';

export const serviceFrequencySchema = z.enum(['monthly', 'quarterly', 'yearly']);
export const serviceStatusSchema = z.enum(['active', 'inactive']);

export const propertyServiceCreateSchema = z.object({
  name: z.string().min(1, 'Service name is required').max(100, 'Service name must be less than 100 characters'),
  cost: z.number().min(0, 'Cost must be a positive number').multipleOf(0.01, 'Cost must have at most 2 decimal places'),
  frequency: serviceFrequencySchema,
  status: serviceStatusSchema.default('active'),
  mandatory: z.boolean().default(false),
  paidByUser: z.boolean().default(true),
  paidByPartner: z.boolean().default(false),
});

export const propertyServiceUpdateSchema = z.object({
  name: z.string().min(1, 'Service name is required').max(100, 'Service name must be less than 100 characters').optional(),
  cost: z.number().min(0, 'Cost must be a positive number').multipleOf(0.01, 'Cost must have at most 2 decimal places').optional(),
  frequency: serviceFrequencySchema.optional(),
  status: serviceStatusSchema.optional(),
  mandatory: z.boolean().optional(),
  paidByUser: z.boolean().optional(),
  paidByPartner: z.boolean().optional(),
});

export const propertyServiceParamsSchema = z.object({
  id: z.string().min(1, 'Property ID is required'),
});

export const propertyServiceWithIdParamsSchema = z.object({
  id: z.string().min(1, 'Property ID is required'),
  serviceId: z.string().min(1, 'Service ID is required'),
});

export type PropertyServiceCreateInput = z.infer<typeof propertyServiceCreateSchema>;
export type PropertyServiceUpdateInput = z.infer<typeof propertyServiceUpdateSchema>;
export type PropertyServiceParamsInput = z.infer<typeof propertyServiceParamsSchema>;

import { z } from 'zod';

export const documentTypeSchema = z.enum(['National ID', 'Visa', 'Passport', 'Foreigner ID']);

export const guestBaseSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters'),
  dateOfBirth: z
    .string()
    .or(z.date())
    .refine((date) => {
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        return age - 1 >= 18;
      }
      return age >= 18;
    }, 'Guest must be at least 18 years old'),
  email: z.string().email('Invalid email format').min(1, 'Email is required'),
  documentType: documentTypeSchema,
  documentNumber: z
    .string()
    .min(1, 'Document number is required')
    .max(50, 'Document number must be less than 50 characters')
    .regex(/^[a-zA-Z0-9\-\s]+$/, 'Document number must be alphanumeric'),
  phoneNumber: z.string().max(20, 'Phone number must be less than 20 characters').optional(),
  relationshipWithOwner: z.string().max(100, 'Relationship must be less than 100 characters').default('Guest'),

  // Foreigner fields
  isForeigner: z.boolean(),
  nationality: z.string().max(50, 'Nationality must be less than 50 characters').optional(),
  countryOfOrigin: z.string().max(50, 'Country of origin must be less than 50 characters').optional(),
  nextDestination: z.string().max(100, 'Next destination must be less than 100 characters').optional(),

  // Local guest fields
  cityOfResidency: z.string().max(50, 'City of residency must be less than 50 characters').optional(),
  cityOfBirth: z.string().max(50, 'City of birth must be less than 50 characters').optional(),
  tripMotivation: z.string().max(200, 'Trip motivation must be less than 200 characters').optional(),
});

export const guestCreateSchema = guestBaseSchema.refine(
  (data) => {
    // If foreigner, require nationality and country of origin
    if (data.isForeigner) {
      return data.nationality && data.countryOfOrigin;
    }
    return true;
  },
  {
    message: 'Nationality and country of origin are required for foreign guests',
    path: ['nationality'],
  }
);

export const guestUpdateSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name must be less than 50 characters').optional(),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name must be less than 50 characters').optional(),
  dateOfBirth: z
    .string()
    .or(z.date())
    .refine((date) => {
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        return age - 1 >= 18;
      }
      return age >= 18;
    }, 'Guest must be at least 18 years old')
    .optional(),
  email: z.string().email('Invalid email format').min(1, 'Email is required').optional(),
  documentType: documentTypeSchema.optional(),
  documentNumber: z
    .string()
    .min(1, 'Document number is required')
    .max(50, 'Document number must be less than 50 characters')
    .regex(/^[a-zA-Z0-9\-\s]+$/, 'Document number must be alphanumeric')
    .optional(),
  phoneNumber: z.string().max(20, 'Phone number must be less than 20 characters').optional(),
  relationshipWithOwner: z.string().max(100, 'Relationship must be less than 100 characters').optional(),

  // Foreigner fields
  isForeigner: z.boolean().optional(),
  nationality: z.string().max(50, 'Nationality must be less than 50 characters').optional(),
  countryOfOrigin: z.string().max(50, 'Country of origin must be less than 50 characters').optional(),
  nextDestination: z.string().max(100, 'Next destination must be less than 100 characters').optional(),

  // Local guest fields
  cityOfResidency: z.string().max(50, 'City of residency must be less than 50 characters').optional(),
  cityOfBirth: z.string().max(50, 'City of birth must be less than 50 characters').optional(),
  tripMotivation: z.string().max(200, 'Trip motivation must be less than 200 characters').optional(),
});

export const guestParamsSchema = z.object({
  id: z.string().min(1, 'Guest ID is required'),
});

export type GuestCreateInput = z.infer<typeof guestCreateSchema>;
export type GuestUpdateInput = z.infer<typeof guestUpdateSchema>;
export type GuestParamsInput = z.infer<typeof guestParamsSchema>;

import { z } from 'zod';
import { emailSchema, nameSchema } from './user';

export const percentageSchema = z.number().min(0, 'Percentage must be at least 0').max(100, 'Percentage must be at most 100');

export const ownerTypeSchema = z.enum(['User', 'Partner']);

export const ownerSchema = z.object({
  ownerType: ownerTypeSchema,
  email: emailSchema,
  name: nameSchema,
  ownershipPercentage: percentageSchema,
});

export const propertyTypeSchema = z.enum(['residential', 'commercial', 'land', 'other']);

export const propertyCreateSchema = z.object({
  name: z.string().min(1, 'Property name is required'),
  address: z.string().min(1, 'Address is required'),
  description: z.string().optional(),
  propertyType: propertyTypeSchema,
  isRented: z.boolean(),
  owners: z.array(ownerSchema).min(1, 'At least one owner is required'),
  currencyId: z.string().optional(),
});

export const propertyUpdateSchema = z.object({
  name: z.string().min(1, 'Property name is required').optional(),
  address: z.string().min(1, 'Address is required').optional(),
  description: z.string().optional(),
  propertyType: propertyTypeSchema.optional(),
  isRented: z.boolean().optional(),
  currencyId: z.string().optional(),
  owners: z.array(ownerSchema).min(1, 'At least one owner is required').optional(),
});

export const propertyParamsSchema = z.object({
  id: z.string().min(1, 'Property ID is required'),
});

export type OwnerInput = z.infer<typeof ownerSchema>;
export type PropertyCreateInput = z.infer<typeof propertyCreateSchema>;
export type PropertyUpdateInput = z.infer<typeof propertyUpdateSchema>;
export type PropertyParamsInput = z.infer<typeof propertyParamsSchema>;

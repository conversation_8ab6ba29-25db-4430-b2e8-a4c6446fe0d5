import { z } from 'zod';

export const operatorAssignmentPermissionsSchema = z.object({
  canCreateProperties: z.boolean(),
  canEditProperties: z.boolean(),
  canDeleteProperties: z.boolean(),
  canManageRentals: z.boolean(),
  canViewFinancials: z.boolean(),
});

export const operatorAssignmentCreateSchema = z.object({
  operatorId: z.string().min(1, 'Operator ID is required'),
  customerId: z.string().min(1, 'Customer ID is required'),
  permissions: operatorAssignmentPermissionsSchema,
});

export const bulkOperatorAssignmentCreateSchema = z.object({
  operatorId: z.string().min(1, 'Operator ID is required'),
  customerIds: z.array(z.string().min(1, 'Customer ID is required')).min(1, 'At least one customer ID is required'),
  permissions: operatorAssignmentPermissionsSchema,
});

export const operatorAssignmentUpdateSchema = z.object({
  permissions: operatorAssignmentPermissionsSchema.optional(),
  isActive: z.boolean().optional(),
});

export const operatorAssignmentParamsSchema = z.object({
  id: z.string().min(1, 'Assignment ID is required'),
});

export const operatorParamsSchema = z.object({
  operatorId: z.string().min(1, 'Operator ID is required'),
});

// Note: Types are exported from types/operatorAssignment.ts to avoid conflicts

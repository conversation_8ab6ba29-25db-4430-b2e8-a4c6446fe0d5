import { z } from 'zod';
import { guestBaseSchema } from './guest';

export const rentalStatusSchema = z.enum(['active', 'ended', 'cancelled']);
export const commissionTypeSchema = z.enum(['percentage', 'fixed']);
export const commissionStatusSchema = z.enum(['pending', 'paid']);
export const commissionReceiverTypeSchema = z.enum(['User', 'Partner']);

export const rentalGuestSchema = z
  .object({
    guestId: z.string().optional(),
    guest: guestBaseSchema.optional(),
    isPrincipalContact: z.boolean(),
  })
  .refine((data) => data.guestId || data.guest, {
    message: 'Either guestId or guest data must be provided',
    path: ['guest'],
  });

export const commissionCreateSchema = z.object({
  amount: z.number().min(0, 'Commission amount must be positive').multipleOf(0.01, 'Commission amount must have at most 2 decimal places'),
  type: commissionTypeSchema,
  receiverType: commissionReceiverTypeSchema,
  receiverId: z.string().min(1, 'Receiver ID is required'),
});

export const commissionUpdateSchema = commissionCreateSchema.extend({
  _id: z.string().optional(),
  status: commissionStatusSchema.optional(),
  paidDate: z.string().or(z.date()).optional(),
});

export const shortTermRentalCreateSchema = z
  .object({
    property: z.string().min(1, 'Property is required'),
    startDate: z
      .string()
      .or(z.date())
      .refine((date) => {
        const startDate = new Date(date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        return startDate >= today;
      }, 'Start date must be today or in the future'),
    endDate: z.string().or(z.date()),
    rentalAmount: z.number().min(0.01, 'Rental amount must be greater than 0').multipleOf(0.01, 'Rental amount must have at most 2 decimal places'),
    costAmount: z.number().min(0, 'Cost amount must be positive').multipleOf(0.01, 'Cost amount must have at most 2 decimal places'),
    status: rentalStatusSchema.default('active'),
    isPaid: z.boolean().default(false),
    paidDate: z.string().or(z.date()).optional(),
    discountAmount: z.number().min(0, 'Discount amount must be positive').multipleOf(0.01, 'Discount amount must have at most 2 decimal places').optional(),
    discountReason: z.string().max(200, 'Discount reason must be less than 200 characters').optional(),
    guests: z
      .array(rentalGuestSchema)
      .min(1, 'At least one guest is required')
      .refine((guests) => guests.filter((g) => g.isPrincipalContact).length === 1, {
        message: 'Exactly one guest must be designated as principal contact',
        path: ['guests'],
      }),
    commissions: z.array(commissionCreateSchema).optional(),
  })
  .refine(
    (data) => {
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);
      return endDate > startDate;
    },
    {
      message: 'End date must be after start date',
      path: ['endDate'],
    }
  )
  .refine(
    (data) => {
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);
      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays <= 365; // Short-term rental should be less than 1 year
    },
    {
      message: 'Short-term rental cannot exceed 365 days',
      path: ['endDate'],
    }
  )
  .refine(
    (data) => {
      // Calculate total commissions
      const totalCommissions =
        data.commissions?.reduce((total, commission) => {
          if (commission.type === 'percentage') {
            return total + (data.rentalAmount * commission.amount) / 100;
          }
          return total + commission.amount;
        }, 0) || 0;

      // Calculate available profit
      const availableProfit = data.rentalAmount - data.costAmount;
      const totalDeductions = (data.discountAmount || 0) + totalCommissions;

      return totalDeductions <= availableProfit;
    },
    {
      message: 'Total of discounts and commissions cannot exceed the profit (rental amount - cost amount)',
      path: ['commissions'],
    }
  )
  .refine(
    (data) => {
      if (data.discountAmount && data.discountAmount > 0 && !data.discountReason) {
        return false;
      }
      return true;
    },
    {
      message: 'Discount reason is required when discount amount is provided',
      path: ['discountReason'],
    }
  );

export const shortTermRentalUpdateSchema = z
  .object({
    startDate: z.string().or(z.date()).optional(),
    endDate: z.string().or(z.date()).optional(),
    rentalAmount: z.number().min(0.01, 'Rental amount must be greater than 0').multipleOf(0.01, 'Rental amount must have at most 2 decimal places').optional(),
    costAmount: z.number().min(0, 'Cost amount must be positive').multipleOf(0.01, 'Cost amount must have at most 2 decimal places').optional(),
    status: rentalStatusSchema.optional(),
    isPaid: z.boolean().optional(),
    paidDate: z.string().or(z.date()).optional(),
    discountAmount: z.number().min(0, 'Discount amount must be positive').multipleOf(0.01, 'Discount amount must have at most 2 decimal places').optional(),
    discountReason: z.string().max(200, 'Discount reason must be less than 200 characters').optional(),
    guests: z.array(rentalGuestSchema).optional(),
    commissions: z.array(commissionUpdateSchema).optional(),
  })
  .refine(
    (data) => {
      if (data.startDate && data.endDate) {
        const startDate = new Date(data.startDate);
        const endDate = new Date(data.endDate);
        return endDate > startDate;
      }
      return true;
    },
    {
      message: 'End date must be after start date',
      path: ['endDate'],
    }
  )
  .refine(
    (data) => {
      // Only validate if we have the required fields for calculation
      if (data.rentalAmount !== undefined && data.costAmount !== undefined) {
        // Calculate total commissions
        const totalCommissions =
          data.commissions?.reduce((total, commission) => {
            if (commission.type === 'percentage') {
              return total + (data.rentalAmount! * commission.amount) / 100;
            }
            return total + commission.amount;
          }, 0) || 0;

        // Calculate available profit
        const availableProfit = data.rentalAmount - data.costAmount;
        const totalDeductions = (data.discountAmount || 0) + totalCommissions;

        return totalDeductions <= availableProfit;
      }
      return true;
    },
    {
      message: 'Total of discounts and commissions cannot exceed the profit (rental amount - cost amount)',
      path: ['commissions'],
    }
  )
  .refine(
    (data) => {
      if (data.discountAmount && data.discountAmount > 0 && !data.discountReason) {
        return false;
      }
      return true;
    },
    {
      message: 'Discount reason is required when discount amount is provided',
      path: ['discountReason'],
    }
  );

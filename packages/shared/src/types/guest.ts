export type DocumentType = 'National ID' | 'Visa' | 'Passport' | 'Foreigner ID';

export interface IGuest {
  _id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  email: string;
  documentType: DocumentType;
  documentNumber: string;
  phoneNumber?: string;
  relationshipWithOwner: string; // Default: "Guest"

  // Foreigner-specific fields
  isForeigner: boolean;
  nationality?: string;
  countryOfOrigin?: string;
  nextDestination?: string;

  // Local guest fields
  cityOfResidency?: string;
  cityOfBirth?: string;
  tripMotivation?: string;

  createdBy: string; // User ID
  createdAt: Date;
  updatedAt: Date;
}

export interface IGuestCreateData {
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  email: string;
  documentType: DocumentType;
  documentNumber: string;
  phoneNumber?: string;
  relationshipWithOwner?: string;

  // Foreigner-specific fields
  isForeigner: boolean;
  nationality?: string;
  countryOfOrigin?: string;
  nextDestination?: string;

  // Local guest fields
  cityOfResidency?: string;
  cityOfBirth?: string;
  tripMotivation?: string;
}

export interface IGuestUpdateData {
  firstName?: string;
  lastName?: string;
  dateOfBirth?: Date;
  email?: string;
  documentType?: DocumentType;
  documentNumber?: string;
  phoneNumber?: string;
  relationshipWithOwner?: string;

  // Foreigner-specific fields
  isForeigner?: boolean;
  nationality?: string;
  countryOfOrigin?: string;
  nextDestination?: string;

  // Local guest fields
  cityOfResidency?: string;
  cityOfBirth?: string;
  tripMotivation?: string;
}

export interface IGuestPublic {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  documentType: DocumentType;
  phoneNumber?: string;
  relationshipWithOwner: string;
  isForeigner: boolean;
}

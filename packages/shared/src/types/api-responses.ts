import { IUserPublic } from './user';
import { IProperty } from './property';
import { IPartner } from './partner';
import { IGuest } from './guest';
import { ICurrency } from './currency';
import { IShortTermRental } from './shortTermRental';
import { IPropertyService } from './propertyService';

// Common API response wrapper
export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  message?: string;
}

// Pagination interface
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationInfo;
}

// Auth responses
export interface AuthResponse {
  token: string;
  user: IUserPublic;
}

export interface UserResponse {
  user: IUserPublic;
}

// Property responses
export interface PropertiesResponse {
  properties: IProperty[];
}

export interface PropertyResponse {
  property: IProperty;
}

// Partner responses
export interface PartnersResponse {
  partners: IPartner[];
}

export interface PartnerResponse {
  partner: IPartner;
}

// Guest responses
export interface GuestsResponse {
  guests: IGuest[];
}

export interface GuestResponse {
  guest: IGuest;
}

// Currency responses
export interface CurrenciesResponse {
  currencies: ICurrency[];
}

export interface CurrencyResponse {
  currency: ICurrency;
}

// Short-term rental responses
export interface ShortTermRentalsResponse {
  rentals: IShortTermRental[];
  pagination: PaginationInfo;
}

export interface ShortTermRentalResponse {
  rental: IShortTermRental;
}

// Property service responses
export interface PropertyServicesResponse {
  services: IPropertyService[];
}

export interface PropertyServiceResponse {
  service: IPropertyService;
}

export interface PropertyServiceSummary {
  totalMonthlyCost: number;
  userMonthlyCost: number;
  partnerMonthlyCost: number;
  mandatoryMonthlyCost: number;
  activeServicesCount: number;
  totalServicesCount: number;
}

export interface PropertyServiceSummaryResponse {
  summary: PropertyServiceSummary;
}

// Cost calculation response
export interface CostCalculationService {
  id: string;
  name: string;
  cost: number;
  frequency: string;
  mandatory: boolean;
}

export interface CostCalculationResponse {
  costAmount: number;
  services: CostCalculationService[];
}

// Dashboard responses
export interface DashboardStats {
  totalProperties: number;
  totalMonthlyIncome: number;
  totalPendingDebts: number;
  netPropertyIncome: number;
}

export interface DashboardStatsResponse {
  stats: DashboardStats;
}

export interface MonthlyRentalData {
  month: string; // Format: "2024-01", "2024-02", etc.
  monthName: string; // Format: "January", "February", etc.
  year: number;
  totalRentals: number;
  totalRevenue: number;
  totalCosts: number;
  netIncome: number;
}

export interface MonthlyRentalStatsResponse {
  data: MonthlyRentalData[];
}

// Error response
export interface ErrorResponse {
  error: string;
  details?: any;
}

// Success response for operations that don't return data
export interface SuccessResponse {
  message: string;
  success: boolean;
}

// User search response
export interface UserSearchResult {
  id: string;
  email: string;
  fullName: string;
}

export interface UserSearchResponse {
  users: UserSearchResult[];
}

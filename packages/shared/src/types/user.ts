export type UserRole = 'ADMIN' | 'OPERATOR' | 'CUSTOMER';
export type AccountStatus = 'ACTIVE' | 'INACTIVE' | 'SUSPENDED';

export interface IUser {
  _id: string;
  email: string;
  password: string;
  fullName: string;
  role: UserRole;
  accountStatus: AccountStatus;
  lastLoginAt?: Date | null;
  loginCount: number;
  createdAt: Date;
  updatedAt: Date;
  // Password management fields
  passwordResetToken?: string | null;
  passwordResetExpires?: Date | null;
  forcePasswordChange?: boolean;
  lastPasswordChange?: Date;
  passwordChangedBy?: string | null;
}

// Interface for User document methods (used in backend)
export interface IUserMethods {
  generatePasswordResetToken(): string;
  clearPasswordResetToken(): void;
  isPasswordResetTokenValid(token: string): boolean;
  setForcePasswordChange(changedBy?: string): void;
  clearForcePasswordChange(): void;
}

export interface IUserPublic {
  id: string;
  email: string;
  fullName: string;
  role: UserRole;
  accountStatus: AccountStatus;
  lastLoginAt?: Date | null;
  loginCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ILoginCredentials {
  email: string;
  password: string;
}

export interface IRegisterData {
  email: string;
  password: string;
  fullName: string;
}

export interface IAuthResponse {
  token: string;
  user: IUserPublic;
}

// Role management interfaces
export interface IRoleChangeData {
  role: UserRole;
}

export interface IRoleChangeResponse {
  success: boolean;
  message: string;
  user: IUserPublic;
}

export interface IBulkRoleChangeData {
  userIds: string[];
  role: UserRole;
}

export interface IBulkRoleChangeResponse {
  success: boolean;
  message: string;
  updatedCount: number;
  failedUsers?: string[];
}

// User account management interfaces
export interface IUserCreateData {
  email: string;
  password: string;
  fullName: string;
  role: UserRole;
}

export interface IUserCreateResponse {
  success: boolean;
  message: string;
  user: IUserPublic;
}

export interface IAccountStatusChangeData {
  status: AccountStatus;
}

export interface IAccountStatusChangeResponse {
  success: boolean;
  message: string;
  user: IUserPublic;
}

export interface IUserDeleteResponse {
  success: boolean;
  message: string;
}

// Audit log interface for role changes
export interface IRoleChangeAuditLog {
  _id: string;
  userId: string;
  changedBy: string;
  oldRole: UserRole;
  newRole: UserRole;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

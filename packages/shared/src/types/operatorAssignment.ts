import { IUserPublic } from './user';

export interface IOperatorAssignmentPermissions {
  canCreateProperties: boolean;
  canEditProperties: boolean;
  canDeleteProperties: boolean;
  canManageRentals: boolean;
  canViewFinancials: boolean;
}

export interface IOperatorAssignment {
  _id: string;
  operatorId: string;
  customerId: string;
  permissions: IOperatorAssignmentPermissions;
  isActive: boolean;
  assignedBy: string;
  assignedAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface IAssignedCustomer {
  id: string;
  email: string;
  fullName: string;
  permissions: IOperatorAssignmentPermissions;
  assignedAt: Date;
}

export interface IOperatorWithAssignments {
  id: string;
  email: string;
  fullName: string;
  role: string;
  assignedCustomers: IAssignedCustomer[];
  totalAssignments: number;
}

export interface IAvailableCustomer {
  id: string;
  email: string;
  fullName: string;
  createdAt: Date;
}

export interface OperatorAssignmentCreateInput {
  operatorId: string;
  customerId: string;
  permissions: IOperatorAssignmentPermissions;
}

export interface BulkOperatorAssignmentCreateInput {
  operatorId: string;
  customerIds: string[];
  permissions: IOperatorAssignmentPermissions;
}

export interface OperatorAssignmentUpdateInput {
  permissions?: IOperatorAssignmentPermissions;
  isActive?: boolean;
}

export interface OperatorAssignmentResponse {
  assignment: IOperatorAssignment;
}

export interface OperatorAssignmentsResponse {
  assignments: IOperatorAssignment[];
}

export interface BulkOperatorAssignmentResponse {
  success: number;
  errors: number;
  data: IOperatorAssignment[];
  errorDetails?: Array<{
    customerId: string;
    error: string;
  }>;
}

export interface OperatorsWithAssignmentsResponse {
  data: IOperatorWithAssignments[];
}

export interface AvailableCustomersResponse {
  data: IAvailableCustomer[];
}

export interface AssignedCustomersResponse {
  data: IAssignedCustomer[];
}

import { IProperty } from './property';
import { IGuest } from './guest';
import { IUserPublic } from './user';
import { IPartner } from './partner';

export type RentalStatus = 'active' | 'ended' | 'cancelled';
export type CommissionType = 'percentage' | 'fixed';
export type CommissionStatus = 'pending' | 'paid';
export type CommissionReceiverType = 'User' | 'Partner';

export interface IRentalGuest {
  guest: IGuest;
  isPrincipalContact: boolean;
}

export interface ICommission {
  _id: string;
  amount: number;
  type: CommissionType;
  status: CommissionStatus;
  receiverType: CommissionReceiverType;
  receiver: IUserPublic | IPartner;
  paidDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface IShortTermRental {
  _id: string;
  property: IProperty;
  propertyId: string;
  startDate: Date;
  endDate: Date;
  rentalAmount: number;
  costAmount: number;
  status: RentalStatus;
  isPaid: boolean;
  paidDate?: Date;
  discountAmount?: number;
  discountReason?: string;
  guests: IRentalGuest[];
  commissions: ICommission[];
  referenceNumber: string;
  createdBy: string; // User ID
  createdAt: Date;
  updatedAt: Date;
}

export interface IShortTermRentalCreateData {
  property: string;
  startDate: Date | string;
  endDate: Date | string;
  rentalAmount: number;
  costAmount: number;
  status: RentalStatus;
  isPaid: boolean;
  paidDate?: Date | string;
  discountAmount?: number;
  discountReason?: string;
  guests: {
    guestId?: string; // For existing guests
    guest?: Omit<IGuest, '_id' | 'createdBy' | 'createdAt' | 'updatedAt'>; // For new guests
    isPrincipalContact: boolean;
  }[];
  commissions?: {
    amount: number;
    type: CommissionType;
    receiverType: CommissionReceiverType;
    receiverId: string;
  }[];
}

export interface IShortTermRentalUpdateData {
  startDate?: Date | string;
  endDate?: Date | string;
  rentalAmount?: number;
  costAmount?: number;
  status?: RentalStatus;
  isPaid?: boolean;
  paidDate?: Date | string;
  discountAmount?: number;
  discountReason?: string;
  guests?: {
    guestId?: string;
    guest?: Omit<IGuest, '_id' | 'createdBy' | 'createdAt' | 'updatedAt'>;
    isPrincipalContact: boolean;
  }[];
  commissions?: {
    _id?: string;
    amount: number;
    type: CommissionType;
    receiverType: CommissionReceiverType;
    receiverId: string;
    status?: CommissionStatus;
    paidDate?: Date | string;
  }[];
}

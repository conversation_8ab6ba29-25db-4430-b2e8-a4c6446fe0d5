import { IPartner } from './partner';
import { IUserPublic } from './user';
import { ICurrency } from './currency';

export type OwnerType = 'User' | 'Partner';

export interface IOwner {
  ownerType: OwnerType;
  email: string;
  name: string;
  ownershipPercentage: number;
}

export interface IPropertyOwner {
  ownerType: OwnerType;
  owner: IUserPublic | IPartner;
  ownershipPercentage: number;
}

export interface IProperty {
  _id: string;
  name: string;
  address: string;
  description?: string;
  propertyType: 'residential' | 'commercial' | 'land' | 'other';
  isRented: boolean;
  owners: IPropertyOwner[];
  currency?: ICurrency; // Property currency
  currencyId?: string; // Currency ID reference
  createdBy: string; // User ID
  createdAt: Date;
  updatedAt: Date;
  // Cost information (calculated from PropertyServices)
  totalMonthlyCost?: number;
  userMonthlyCost?: number;
  partnerMonthlyCost?: number;
  activeServicesCount?: number;
}

export interface IPropertyCreateData {
  name: string;
  address: string;
  description?: string;
  propertyType: 'residential' | 'commercial' | 'land' | 'other';
  isRented: boolean;
  owners: IOwner[];
  currencyId?: string; // Optional currency ID
}

export interface IPropertyUpdateData {
  name?: string;
  address?: string;
  description?: string;
  propertyType?: 'residential' | 'commercial' | 'land' | 'other';
  isRented?: boolean;
  currencyId?: string; // Optional currency ID update
  owners?: IOwner[]; // Optional owners update
}

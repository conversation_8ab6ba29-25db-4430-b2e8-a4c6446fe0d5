export type ServiceFrequency = 'monthly' | 'quarterly' | 'yearly';
export type ServiceStatus = 'active' | 'inactive';

export interface IPropertyService {
  _id: string;
  propertyId: string;
  name: string;
  cost: number;
  frequency: ServiceFrequency;
  status: ServiceStatus;
  mandatory: boolean;
  paidByUser: boolean;
  paidByPartner: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface IPropertyServiceCreateData {
  name: string;
  cost: number;
  frequency: ServiceFrequency;
  status?: ServiceStatus;
  mandatory?: boolean;
  paidByUser?: boolean;
  paidByPartner?: boolean;
}

export interface IPropertyServiceUpdateData {
  name?: string;
  cost?: number;
  frequency?: ServiceFrequency;
  status?: ServiceStatus;
  mandatory?: boolean;
  paidByUser?: boolean;
  paidByPartner?: boolean;
}

export interface IServiceCostSummary {
  totalMonthlyCost: number;
  userMonthlyCost: number;
  partnerMonthlyCost: number;
  mandatoryMonthlyCost: number;
  activeServicesCount: number;
  totalServicesCount: number;
}

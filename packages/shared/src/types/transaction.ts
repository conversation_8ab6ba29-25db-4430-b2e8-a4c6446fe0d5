import { z } from 'zod';
import { IUser } from './user';
import { IProperty } from './property';

export interface ITransaction {
  _id: string;
  propertyId: IProperty;
  amount: number;
  description: string;
  type: 'property_cost' | 'rental_payment' | 'dividend_payment' | 'manual';
  category?: string;
  ownerId?: IUser;
  ownerType?: 'User' | 'Partner';
  rentalId?: string;
  date: Date;
  createdBy: IUser;
  status: 'active' | 'deleted';
  modifiedBy?: IUser;
  deletedBy?: IUser;
  modifiedDate?: Date;
  deletedDate?: Date;
  recurrent: 'none' | 'daily' | 'weekly' | 'monthly' | 'yearly';
  createdAt: Date;
  updatedAt: Date;
}

export const transactionCreateSchema = z.object({
  propertyId: z.string(),
  amount: z.number(),
  description: z.string().min(1),
  type: z.enum(['property_cost', 'rental_payment', 'dividend_payment', 'manual']),
  category: z.string().optional(),
  ownerId: z.string().optional(),
  ownerType: z.enum(['User', 'Partner']).optional(),
  rentalId: z.string().optional(),
  date: z.date().optional(),
  recurrent: z.enum(['none', 'daily', 'weekly', 'monthly', 'yearly']).default('none'),
});

export const transactionUpdateSchema = transactionCreateSchema.partial();

export type ITransactionCreateData = z.infer<typeof transactionCreateSchema>;
export type ITransactionUpdateData = z.infer<typeof transactionUpdateSchema>;

export interface TransactionSummary {
  totalIncome: number;
  totalExpenses: number;
  netIncome: number;
  transactionCount: number;
}

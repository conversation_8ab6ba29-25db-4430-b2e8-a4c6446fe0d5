import { Schema, model } from 'mongoose';
import { IPropertyService } from '@alom-rentals/shared';

const propertyServiceSchema = new Schema<IPropertyService>(
  {
    propertyId: {
      type: Schema.Types.ObjectId,
      ref: 'Property',
      required: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
    },
    cost: {
      type: Number,
      required: true,
      min: 0,
      set: (value: number) => Math.round(value * 100) / 100, // Ensure 2 decimal places
    },
    frequency: {
      type: String,
      enum: ['monthly', 'quarterly', 'yearly'],
      required: true,
    },
    status: {
      type: String,
      enum: ['active', 'inactive'],
      default: 'active',
    },
    mandatory: {
      type: Boolean,
      default: false,
    },
    paidByUser: {
      type: Boolean,
      default: true,
    },
    paidByPartner: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

// Instance method to calculate monthly equivalent cost
propertyServiceSchema.methods.getMonthlyEquivalent = function(): number {
  switch (this.frequency) {
    case 'monthly':
      return this.cost;
    case 'quarterly':
      return this.cost / 3;
    case 'yearly':
      return this.cost / 12;
    default:
      return this.cost;
  }
};

// Static method to calculate cost summary for a property
propertyServiceSchema.statics.calculateCostSummary = async function(propertyId: string) {
  const services = await this.find({ propertyId, status: 'active' });
  
  let totalMonthlyCost = 0;
  let userMonthlyCost = 0;
  let partnerMonthlyCost = 0;
  let mandatoryMonthlyCost = 0;
  
  services.forEach((service: any) => {
    const monthlyEquivalent = service.getMonthlyEquivalent();
    totalMonthlyCost += monthlyEquivalent;
    
    if (service.paidByUser) {
      userMonthlyCost += monthlyEquivalent;
    }
    
    if (service.paidByPartner) {
      partnerMonthlyCost += monthlyEquivalent;
    }
    
    if (service.mandatory) {
      mandatoryMonthlyCost += monthlyEquivalent;
    }
  });
  
  return {
    totalMonthlyCost: Math.round(totalMonthlyCost * 100) / 100,
    userMonthlyCost: Math.round(userMonthlyCost * 100) / 100,
    partnerMonthlyCost: Math.round(partnerMonthlyCost * 100) / 100,
    mandatoryMonthlyCost: Math.round(mandatoryMonthlyCost * 100) / 100,
    activeServicesCount: services.length,
    totalServicesCount: await this.countDocuments({ propertyId }),
  };
};

// Indexes for performance
propertyServiceSchema.index({ propertyId: 1, status: 1 });
propertyServiceSchema.index({ propertyId: 1, frequency: 1 });

export default model<IPropertyService>('PropertyService', propertyServiceSchema);

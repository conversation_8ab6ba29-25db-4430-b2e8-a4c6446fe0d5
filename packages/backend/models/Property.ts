import mongoose, { Document, Schema } from 'mongoose';
import { IProperty as IPropertyShared, IPropertyOwner as IPropertyOwnerShared } from '@alom-rentals/shared';
import { IUser } from './User';
import { IPartner } from './Partner';

export interface IPropertyOwner extends Omit<IPropertyOwnerShared, 'owner'> {
  owner: IUser['_id'] | IPartner['_id'];
}

export interface IProperty extends Document, Omit<IPropertyShared, '_id' | 'owners' | 'createdBy'> {
  owners: IPropertyOwner[];
  createdBy: IUser['_id'];
}

const propertySchema = new Schema<IProperty>(
  {
    name: {
      type: String,
      required: true,
    },
    address: String,
    description: String,
    propertyType: {
      type: String,
      enum: ['residential', 'commercial', 'land', 'other'],
      default: 'residential',
    },
    isRented: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    currencyId: {
      type: Schema.Types.ObjectId,
      ref: 'Currency',
      required: false, // Optional for backward compatibility
    },
    owners: [
      {
        ownerType: {
          type: String,
          enum: ['User', 'Partner'],
          required: true,
        },
        owner: {
          type: Schema.Types.ObjectId,
          required: true,
          refPath: 'owners.ownerType',
        },
        ownershipPercentage: {
          type: Number,
          required: true,
          min: 0,
          max: 100,
        },
      },
    ],
  },
  {
    timestamps: true,
  }
);

// Index for faster queries
propertySchema.index({ createdBy: 1 });

// Helper method to populate owners dynamically
propertySchema.methods.populateOwners = function () {
  return this.populate({
    path: 'owners.owner',
    select: 'name email fullName',
  });
};

export default mongoose.models.Property || mongoose.model<IProperty>('Property', propertySchema);

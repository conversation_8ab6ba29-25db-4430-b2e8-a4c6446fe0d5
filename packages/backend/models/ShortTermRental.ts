import mongoose, { Document, Schema } from 'mongoose';
import { IShortTermRental as IShortTermRentalShared, IRentalGuest as IRentalGuestShared, RentalStatus } from '@alom-rentals/shared';
import { IUser } from './User';
import { IProperty } from './Property';
import { IGuest } from './Guest';
import { ICommission } from './Commission';

export interface IRentalGuest extends Omit<IRentalGuestShared, 'guest'> {
  guest: IGuest['_id'];
}

export interface IShortTermRental extends Document, Omit<IShortTermRentalShared, '_id' | 'property' | 'guests' | 'commissions' | 'createdBy'> {
  property: IProperty['_id'];
  guests: IRentalGuest[];
  commissions: ICommission['_id'][];
  createdBy: IUser['_id'];
  populateAll(): Promise<this>;
}

const rentalGuestSchema = new Schema<IRentalGuest>({
  guest: {
    type: Schema.Types.ObjectId,
    ref: 'Guest',
    required: true,
  },
  isPrincipalContact: {
    type: Boolean,
    required: true,
    default: false,
  },
});

const shortTermRentalSchema = new Schema<IShortTermRental>(
  {
    property: {
      type: Schema.Types.ObjectId,
      ref: 'Property',
      required: true,
    },
    startDate: {
      type: Date,
      required: true,
      validate: {
        validator: function (date: Date) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          return date >= today;
        },
        message: 'Start date must be today or in the future',
      },
    } as any,
    endDate: {
      type: Date,
      required: true,
      validate: {
        validator: function (this: IShortTermRental, date: Date) {
          return date > this.startDate;
        },
        message: 'End date must be after start date',
      },
    },
    rentalAmount: {
      type: Number,
      required: true,
      min: 0.01,
      set: (value: number) => Math.round(value * 100) / 100, // Ensure 2 decimal places
    },
    costAmount: {
      type: Number,
      required: true,
      min: 0,
      set: (value: number) => Math.round(value * 100) / 100, // Ensure 2 decimal places
    },
    status: {
      type: String,
      enum: ['active', 'ended', 'cancelled'] as RentalStatus[],
      default: 'active',
    },
    isPaid: {
      type: Boolean,
      default: false,
    },
    paidDate: {
      type: Date,
    },
    discountAmount: {
      type: Number,
      min: 0,
      set: (value: number) => (value ? Math.round(value * 100) / 100 : value), // Ensure 2 decimal places
    },
    discountReason: {
      type: String,
      maxlength: 200,
      trim: true,
    },
    guests: [rentalGuestSchema],
    commissions: [
      {
        type: Schema.Types.ObjectId,
        ref: 'Commission',
      },
    ],
    referenceNumber: {
      type: String,
      unique: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for faster queries
shortTermRentalSchema.index({ property: 1 });
shortTermRentalSchema.index({ createdBy: 1 });
shortTermRentalSchema.index({ startDate: 1, endDate: 1 });
shortTermRentalSchema.index({ status: 1 });
// referenceNumber already has unique index from schema definition

// Validation for rental duration (short-term should be less than 1 year)
shortTermRentalSchema.pre('save', function (this: IShortTermRental, next) {
  const diffTime = Math.abs(this.endDate.getTime() - this.startDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays > 365) {
    return next(new Error('Short-term rental cannot exceed 365 days'));
  }

  // Validate that exactly one guest is principal contact
  const principalContacts = this.guests.filter((g: any) => g.isPrincipalContact);
  if (principalContacts.length !== 1) {
    return next(new Error('Exactly one guest must be designated as principal contact'));
  }

  // Validate discount amount doesn't exceed profit
  if (this.discountAmount && this.discountAmount > 0) {
    const profit = this.rentalAmount - this.costAmount;
    if (this.discountAmount > profit) {
      return next(new Error('Discount amount cannot exceed profit (rental amount - cost amount)'));
    }

    // Validate that discount reason is provided when discount amount is set
    if (!this.discountReason || this.discountReason.trim() === '') {
      return next(new Error('Discount reason is required when discount amount is provided'));
    }
  }

  next();
});

// Check for overlapping rentals
shortTermRentalSchema.pre('save', async function (this: IShortTermRental, next) {
  // Only check for overlaps if this is a new rental or dates have changed
  if (this.isNew || this.isModified('startDate') || this.isModified('endDate')) {
    const overlappingRental = await mongoose.model('ShortTermRental').findOne({
      _id: { $ne: this._id }, // Exclude current rental
      property: this.property,
      status: { $in: ['active', 'confirmed'] }, // Only check active/confirmed rentals
      $or: [
        // New rental starts during existing rental
        { startDate: { $lte: this.startDate }, endDate: { $gt: this.startDate } },
        // New rental ends during existing rental
        { startDate: { $lt: this.endDate }, endDate: { $gte: this.endDate } },
        // New rental completely contains existing rental
        { startDate: { $gte: this.startDate }, endDate: { $lte: this.endDate } },
      ],
    });

    if (overlappingRental) {
      return next(new Error('This rental overlaps with an existing rental for the same property'));
    }
  }

  next();
});

// Validate discount and commission totals don't exceed profit
shortTermRentalSchema.pre('save', async function (this: IShortTermRental, next) {
  if (this.isModified('discountAmount') || this.isModified('commissions') || this.isModified('rentalAmount') || this.isModified('costAmount')) {
    // Calculate total commissions
    let totalCommissions = 0;
    if (this.commissions && this.commissions.length > 0) {
      const Commission = mongoose.model('Commission');
      const commissions = await Commission.find({ _id: { $in: this.commissions } });
      totalCommissions = commissions.reduce((total: number, commission: any) => {
        if (commission.type === 'percentage') {
          return total + (this.rentalAmount * commission.amount) / 100;
        }
        return total + commission.amount;
      }, 0);
    }

    // Calculate available profit
    const availableProfit = this.rentalAmount - this.costAmount;
    const totalDeductions = (this.discountAmount || 0) + totalCommissions;

    if (totalDeductions > availableProfit) {
      return next(new Error('Total of discounts and commissions cannot exceed the profit (rental amount - cost amount)'));
    }
  }

  next();
});

// Validate discount reason is required when discount amount is provided
shortTermRentalSchema.pre('save', function (this: IShortTermRental, next) {
  if (this.discountAmount && this.discountAmount > 0 && !this.discountReason) {
    return next(new Error('Discount reason is required when discount amount is provided'));
  }

  next();
});

// Generate reference number before saving
shortTermRentalSchema.pre('save', async function (this: IShortTermRental, next) {
  if (this.isNew && !this.referenceNumber) {
    const year = new Date().getFullYear();
    const count = await mongoose.model('ShortTermRental').countDocuments({
      createdAt: {
        $gte: new Date(year, 0, 1),
        $lt: new Date(year + 1, 0, 1),
      },
    });
    this.referenceNumber = `STR-${year}-${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

// Helper method to populate all related data
shortTermRentalSchema.methods.populateAll = function () {
  return this.populate([
    {
      path: 'property',
      select: 'name address propertyType',
      populate: {
        path: 'currencyId',
        select: 'name symbol code',
      },
    },
    {
      path: 'guests.guest',
      select: 'firstName lastName email documentType documentNumber phoneNumber fullName',
    },
    {
      path: 'commissions',
      populate: {
        path: 'receiver',
        select: 'name email fullName',
      },
    },
    {
      path: 'createdBy',
      select: 'fullName email',
    },
  ]);
};

export default mongoose.models.ShortTermRental || mongoose.model<IShortTermRental>('ShortTermRental', shortTermRentalSchema);

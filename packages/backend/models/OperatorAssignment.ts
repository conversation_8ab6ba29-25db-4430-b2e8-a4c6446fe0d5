import mongoose, { Document, Schema } from 'mongoose';
import { IOperatorAssignment as IOperatorAssignmentShared, IOperatorAssignmentPermissions } from '@alom-rentals/shared';
import { IUser } from './User';

export interface IOperatorAssignment extends Document, Omit<IOperatorAssignmentShared, '_id' | 'operatorId' | 'customerId' | 'assignedBy'> {
  operatorId: IUser['_id'];
  customerId: IUser['_id'];
  assignedBy: IUser['_id'];
}

const operatorAssignmentPermissionsSchema = new Schema<IOperatorAssignmentPermissions>(
  {
    canCreateProperties: { type: Boolean, required: true, default: true },
    canEditProperties: { type: Boolean, required: true, default: true },
    canDeleteProperties: { type: Boolean, required: true, default: false },
    canManageRentals: { type: Boolean, required: true, default: true },
    canViewFinancials: { type: Boolean, required: true, default: true },
  },
  { _id: false }
);

const operatorAssignmentSchema = new Schema<IOperatorAssignment>(
  {
    operatorId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    customerId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    permissions: {
      type: operatorAssignmentPermissionsSchema,
      required: true,
    },
    isActive: {
      type: Boolean,
      required: true,
      default: true,
    },
    assignedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    assignedAt: {
      type: Date,
      required: true,
      default: Date.now,
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for better query performance
operatorAssignmentSchema.index({ operatorId: 1, isActive: 1 });
operatorAssignmentSchema.index({ customerId: 1, isActive: 1 });
operatorAssignmentSchema.index({ operatorId: 1, customerId: 1 }, { unique: true });

export default mongoose.models.OperatorAssignment || mongoose.model<IOperatorAssignment>('OperatorAssignment', operatorAssignmentSchema);

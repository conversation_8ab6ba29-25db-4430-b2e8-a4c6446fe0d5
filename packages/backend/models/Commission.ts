import mongoose, { Document, Schema } from 'mongoose';
import { ICommission as ICommissionShared, CommissionType, CommissionStatus, CommissionReceiverType } from '@alom-rentals/shared';
import { IUser } from './User';
import { IPartner } from './Partner';

export interface ICommission extends Document, Omit<ICommissionShared, '_id' | 'receiver'> {
  receiver: IUser['_id'] | IPartner['_id'];
}

const commissionSchema = new Schema<ICommission>(
  {
    amount: {
      type: Number,
      required: true,
      min: 0,
      set: (value: number) => Math.round(value * 100) / 100, // Ensure 2 decimal places
    },
    type: {
      type: String,
      enum: ['percentage', 'fixed'] as CommissionType[],
      required: true,
    },
    status: {
      type: String,
      enum: ['pending', 'paid'] as CommissionStatus[],
      default: 'pending',
    },
    receiverType: {
      type: String,
      enum: ['User', 'Partner'] as CommissionReceiverType[],
      required: true,
    },
    receiver: {
      type: Schema.Types.ObjectId,
      required: true,
      refPath: 'receiverType',
    },
    paidDate: {
      type: Date,
    },
  },
  {
    timestamps: true,
  }
);

// Index for faster queries
commissionSchema.index({ receiver: 1 });
commissionSchema.index({ status: 1 });

export default mongoose.models.Commission || mongoose.model<ICommission>('Commission', commissionSchema);

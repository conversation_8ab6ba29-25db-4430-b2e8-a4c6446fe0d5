import { Schema, model } from 'mongoose';
import { ITransaction } from '@alom-rentals/shared';

const transactionSchema = new Schema<ITransaction>(
  {
    propertyId: {
      type: Schema.Types.ObjectId,
      ref: 'Property',
      required: true,
    },
    amount: {
      type: Number,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: ['property_cost', 'rental_payment', 'dividend_payment', 'manual'],
      required: true,
    },
    category: {
      type: String,
      required: false,
    },
    ownerId: {
      type: Schema.Types.ObjectId,
      refPath: 'ownerType',
      required: false,
    },
    ownerType: {
      type: String,
      enum: ['User', 'Partner'],
      required: false,
    },
    rentalId: {
      type: Schema.Types.ObjectId,
      ref: 'ShortTermRental',
      required: false,
    },
    date: {
      type: Date,
      required: true,
      default: Date.now,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    status: {
      type: String,
      enum: ['active', 'deleted'],
      default: 'active',
    },
    modifiedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: false,
    },
    deletedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: false,
    },
    modifiedDate: {
      type: Date,
      required: false,
    },
    deletedDate: {
      type: Date,
      required: false,
    },
    recurrent: {
      type: String,
      enum: ['none', 'daily', 'weekly', 'monthly', 'yearly'],
      default: 'none',
    },
  },
  {
    timestamps: true,
  }
);

// Indexes for performance
transactionSchema.index({ propertyId: 1, status: 1 });
transactionSchema.index({ createdBy: 1, status: 1 });
transactionSchema.index({ date: -1 });
transactionSchema.index({ type: 1, status: 1 });

export default model<ITransaction>('Transaction', transactionSchema);
import mongoose, { Document, Schema } from 'mongoose';
import { IUser as IUserShared, UserRole } from '@alom-rentals/shared';

export interface IUser extends Document, Omit<IUserShared, '_id'> {
  // Password management methods
  generatePasswordResetToken?(): string;
  clearPasswordResetToken?(): void;
  isPasswordResetTokenValid?(token: string): boolean;
  setForcePasswordChange?(changedBy?: string): void;
  clearForcePasswordChange?(): void;
}

const userSchema = new Schema<any>(
  {
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
    },
    password: {
      type: String,
      required: true,
    },
    fullName: {
      type: String,
      required: true,
    },
    role: {
      type: String,
      enum: ['ADMIN', 'OPERATOR', 'CUSTOMER'],
      default: 'CUSTOMER',
      required: true,
    },
    accountStatus: {
      type: String,
      enum: ['ACTIVE', 'INACTIVE', 'SUSPENDED'],
      default: 'ACTIVE',
      required: true,
    },
    lastLoginAt: {
      type: Date,
      default: null,
    },
    loginCount: {
      type: Number,
      default: 0,
    },
    // Password management fields
    passwordResetToken: {
      type: String,
      default: null,
    },
    passwordResetExpires: {
      type: Date,
      default: null,
    },
    forcePasswordChange: {
      type: Boolean,
      default: false,
    },
    lastPasswordChange: {
      type: Date,
      default: Date.now,
    },
    passwordChangedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null,
    },
  },
  {
    timestamps: true,
  }
);

// Index for role-based queries
userSchema.index({ role: 1 });
userSchema.index({ role: 1, createdAt: -1 });
userSchema.index({ accountStatus: 1 });
userSchema.index({ passwordResetToken: 1 });
userSchema.index({ passwordResetExpires: 1 });

// Password management methods
userSchema.methods.generatePasswordResetToken = function () {
  const crypto = require('crypto');
  const resetToken = crypto.randomBytes(32).toString('hex');

  this.passwordResetToken = crypto.createHash('sha256').update(resetToken).digest('hex');
  this.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

  return resetToken;
};

userSchema.methods.clearPasswordResetToken = function () {
  this.passwordResetToken = null;
  this.passwordResetExpires = null;
};

userSchema.methods.isPasswordResetTokenValid = function (token: string) {
  if (!this.passwordResetToken || !this.passwordResetExpires) {
    return false;
  }

  const crypto = require('crypto');
  const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

  return hashedToken === this.passwordResetToken && this.passwordResetExpires > new Date();
};

userSchema.methods.setForcePasswordChange = function (changedBy?: string) {
  this.forcePasswordChange = true;
  this.passwordChangedBy = changedBy || null;
};

userSchema.methods.clearForcePasswordChange = function () {
  this.forcePasswordChange = false;
  this.lastPasswordChange = new Date();
};

export default mongoose.models.User || mongoose.model<IUser>('User', userSchema);

import mongoose, { Document, Schema } from 'mongoose';
import { IUser } from './User';

export interface IAuditLog extends Document {
  userId?: IUser['_id'];
  changedBy?: IUser['_id'];
  action: string;
  resourceType: string;
  resourceId?: string;
  oldValues?: Record<string, unknown>;
  newValues?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}

const auditLogSchema = new Schema<IAuditLog>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: false,
    },
    changedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: false,
    },
    action: {
      type: String,
      required: true,
      maxlength: 100,
    },
    resourceType: {
      type: String,
      required: true,
      maxlength: 50,
    },
    resourceId: {
      type: String,
      required: false,
      maxlength: 36,
    },
    oldValues: {
      type: Schema.Types.Mixed,
      required: false,
    },
    newValues: {
      type: Schema.Types.Mixed,
      required: false,
    },
    ipAddress: {
      type: String,
      required: false,
      maxlength: 45,
    },
    userAgent: {
      type: String,
      required: false,
    },
  },
  {
    timestamps: { createdAt: true, updatedAt: false },
  }
);

// Indexes for better query performance
auditLogSchema.index({ userId: 1, createdAt: -1 });
auditLogSchema.index({ resourceType: 1, resourceId: 1, createdAt: -1 });
auditLogSchema.index({ action: 1, createdAt: -1 });
auditLogSchema.index({ createdAt: -1 });

export const AuditLog = mongoose.models.AuditLog || mongoose.model<IAuditLog>('AuditLog', auditLogSchema);

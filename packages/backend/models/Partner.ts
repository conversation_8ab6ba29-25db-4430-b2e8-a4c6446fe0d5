import mongoose, { Document, Schema } from 'mongoose';
import { IPartner as IPartnerShared } from '@alom-rentals/shared';
import { IUser } from './User';

export interface IPartner extends Document, Omit<IPartnerShared, '_id' | 'createdBy'> {
  createdBy: IUser['_id'];
}

const partnerSchema = new Schema<IPartner>(
  {
    email: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Index for faster queries
partnerSchema.index({ email: 1, createdBy: 1 });
partnerSchema.index({ createdBy: 1 });

export default mongoose.models.Partner || mongoose.model<IPartner>('Partner', partnerSchema);

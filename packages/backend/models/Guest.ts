import mongoose, { Document, Schema, Model } from 'mongoose';
import { IGuest as IGuestShared, DocumentType } from '@alom-rentals/shared';
import { IUser } from './User';

export interface IGuest extends Document, Omit<IGuestShared, '_id' | 'createdBy'> {
  createdBy: IUser['_id'];
}
interface GuestVirtuals {
  fullName: string;
}
export type GuestType = Model<IGuest, {}, {}, GuestVirtuals>;
const guestSchema = new Schema<IGuest, GuestType, {}, {}, GuestVirtuals>({
  firstName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
  },
  dateOfBirth: {
    type: Date,
    required: true,
    validate: {
      validator: function (date: Date) {
        const today = new Date();
        const age = today.getFullYear() - date.getFullYear();
        const monthDiff = today.getMonth() - date.getMonth();

        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
          return age - 1 >= 18;
        }
        return age >= 18;
      },
      message: 'Guest must be at least 18 years old',
    },
  },
  email: {
    type: String,
    required: true,
    trim: true,
    lowercase: true,
    validate: {
      validator: function (email: string) {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
      },
      message: 'Invalid email format',
    },
  },
  documentType: {
    type: String,
    required: true,
    enum: ['National ID', 'Visa', 'Passport', 'Foreigner ID'] as DocumentType[],
  },
  documentNumber: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50,
    validate: {
      validator: function (docNumber: string) {
        return /^[a-zA-Z0-9\-\s]+$/.test(docNumber);
      },
      message: 'Document number must be alphanumeric',
    },
  },
  phoneNumber: {
    type: String,
    trim: true,
    maxlength: 20,
  },
  relationshipWithOwner: {
    type: String,
    trim: true,
    maxlength: 100,
    default: 'Guest',
  },

  // Foreigner-specific fields
  isForeigner: {
    type: Boolean,
    required: true,
    default: false,
  },
  nationality: {
    type: String,
    trim: true,
    maxlength: 50,
  },
  countryOfOrigin: {
    type: String,
    trim: true,
    maxlength: 50,
  },
  nextDestination: {
    type: String,
    trim: true,
    maxlength: 100,
  },

  // Local guest fields
  cityOfResidency: {
    type: String,
    trim: true,
    maxlength: 50,
  },
  cityOfBirth: {
    type: String,
    trim: true,
    maxlength: 50,
  },
  tripMotivation: {
    type: String,
    trim: true,
    maxlength: 200,
  },

  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
});

// Compound index for unique email and document number per user
guestSchema.index({ email: 1, createdBy: 1 }, { unique: true });
guestSchema.index({ documentNumber: 1, createdBy: 1 }, { unique: true });

// Validation for foreigner fields
guestSchema.pre('save', function (next) {
  if (this.isForeigner) {
    if (!this.nationality || !this.countryOfOrigin) {
      return next(new Error('Nationality and country of origin are required for foreign guests'));
    }
  }
  next();
});

// Virtual for full name
guestSchema.virtual('fullName').get(function () {
  return `${this.firstName} ${this.lastName}`;
});

// Ensure virtual fields are serialized
guestSchema.set('toJSON', {
  virtuals: true,
});

const Guest = mongoose.models.Guest || mongoose.model('Guest', guestSchema);

export default Guest;

import mongoose, { Schema, Document, MongooseError } from 'mongoose';
import { ICurrency } from '@alom-rentals/shared';
import { IUser } from './User';

export interface ICurrencyDocument extends Document, Omit<ICurrency, 'createdBy' | '_id'> {
  isInUse(): Promise<boolean>;
  createdBy: IUser['_id'];
}

export interface ICurrencyModel extends mongoose.Model<ICurrencyDocument> {
  createDefaultCurrencies(userId: string): Promise<void>;
}

const currencySchema = new Schema<ICurrencyDocument>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
    },
    symbol: {
      type: String,
      required: true,
      trim: true,
      maxlength: 3,
    },
    code: {
      type: String,
      trim: true,
      length: 3,
      uppercase: true,
      match: /^[A-Z]{3}$/,
      sparse: true, // Allow multiple null values but enforce uniqueness for non-null values
    },
    createdBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Compound unique index to ensure name and code are unique per user
currencySchema.index({ name: 1, createdBy: 1 }, { unique: true });
currencySchema.index({ code: 1, createdBy: 1 }, { unique: true, sparse: true });

// Static method to create default currencies for a user
currencySchema.statics.createDefaultCurrencies = async function (userId: string) {
  const defaultCurrencies = [
    { name: 'US Dollar', symbol: '$', code: 'USD', createdBy: userId },
    { name: 'Euro', symbol: '€', code: 'EUR', createdBy: userId },
    { name: 'British Pound', symbol: '£', code: 'GBP', createdBy: userId },
  ];

  try {
    // Check if user already has currencies
    const existingCount = await this.countDocuments({ createdBy: userId });
    if (existingCount === 0) {
      // Insert currencies one by one to handle potential duplicates better
      for (const currencyData of defaultCurrencies) {
        try {
          await this.create(currencyData);
        } catch (duplicateError: any) {
          // Ignore duplicate key errors for individual currencies
          if (duplicateError.code !== 11000) {
            throw duplicateError;
          }
        }
      }
    }
  } catch (error: unknown) {
    // Ignore duplicate key errors - user might have created some currencies manually
    if (error instanceof MongooseError && 'code' in error && error.code !== 11000) {
      throw error;
    }
  }
};

// Instance method to check if currency is being used by any properties
currencySchema.methods.isInUse = async function () {
  try {
    // Check if Property model exists before using it
    if (mongoose.models.Property) {
      const Property = mongoose.model('Property');
      const count = await Property.countDocuments({ currencyId: this._id });
      return count > 0;
    }
    return false;
  } catch (error) {
    // If there's an error (e.g., Property model not loaded), assume not in use
    return false;
  }
};

const Currency = (mongoose.models.Currency as ICurrencyModel) || mongoose.model<ICurrencyDocument, ICurrencyModel>('Currency', currencySchema);

export default Currency;

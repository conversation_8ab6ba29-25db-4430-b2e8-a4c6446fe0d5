import mongoose, { Document, Schema } from 'mongoose';
import { IUser } from './User';

export interface INotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  weeklyReports: boolean;
  monthlyReports: boolean;
  budgetAlerts: boolean;
  investmentUpdates: boolean;
}

export interface IProfile extends Document {
  userId: IUser['_id'];
  age?: number;
  salary?: number;
  savingsAmount: number;
  investmentRiskLevel: 'low' | 'medium' | 'high';
  notificationSettings: INotificationSettings;
  createdAt: Date;
  updatedAt: Date;
}

const profileSchema = new Schema<IProfile>(
  {
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      unique: true,
    },
    age: {
      type: Number,
      min: 18,
      max: 120,
    },
    salary: {
      type: Number,
      min: 0,
    },
    savingsAmount: {
      type: Number,
      min: 0,
      default: 0,
    },
    investmentRiskLevel: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'medium',
    },
    notificationSettings: {
      emailNotifications: {
        type: Boolean,
        default: true,
      },
      pushNotifications: {
        type: Boolean,
        default: true,
      },
      weeklyReports: {
        type: Boolean,
        default: true,
      },
      monthlyReports: {
        type: Boolean,
        default: true,
      },
      budgetAlerts: {
        type: Boolean,
        default: true,
      },
      investmentUpdates: {
        type: Boolean,
        default: false,
      },
    },
  },
  {
    timestamps: true,
  }
);

// Index for faster queries
profileSchema.index({ userId: 1 });

// Virtual to populate user data
profileSchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true,
});

// Ensure virtual fields are serialized
profileSchema.set('toJSON', { virtuals: true });
profileSchema.set('toObject', { virtuals: true });

export default mongoose.models.Profile || mongoose.model<IProfile>('Profile', profileSchema);

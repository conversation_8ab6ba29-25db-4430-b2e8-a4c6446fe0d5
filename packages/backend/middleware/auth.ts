import jwt from 'jsonwebtoken';
import { Context, Next } from 'hono';
import User, { IUser } from '../models/User';
import OperatorAssignment from '../models/OperatorAssignment';
import { UserRole } from '@alom-rentals/shared';

interface JWTPayload {
  userId: string;
  role?: UserRole; // Optional for backward compatibility
}

declare module 'hono' {
  interface ContextVariableMap {
    user: IUser;
  }
}

export const authenticate = async (c: Context, next: Next) => {
  try {
    const authHeader = c.req.header('Authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return c.json({ error: 'Access denied. No token provided.' }, 401);
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as JWTPayload;
    const user = await User.findById(decoded.userId).select('-password');

    if (!user) {
      return c.json({ error: 'Invalid token.' }, 401);
    }

    c.set('user', user);
    await next();
  } catch (error) {
    return c.json({ error: 'Invalid token.' }, 401);
  }
};

// Backward compatibility alias
export const auth = authenticate;

// Role-based access control middleware
export const requireRole = (allowedRoles: UserRole[]) => {
  return async (c: Context, next: Next) => {
    const user = c.get('user') as IUser;

    if (!user) {
      return c.json({ error: 'Authentication required' }, 401);
    }

    if (!allowedRoles.includes(user.role)) {
      return c.json({ error: 'Insufficient permissions' }, 403);
    }

    await next();
  };
};

// Check if user has specific role
export const hasRole = (user: IUser, role: UserRole): boolean => {
  return user.role === role;
};

// Check if user has any of the specified roles
export const hasAnyRole = (user: IUser, roles: UserRole[]): boolean => {
  return roles.includes(user.role);
};

// Build data access query based on user role and permissions
export const buildDataAccessQuery = async (
  user: IUser,
  baseQuery: any = {},
  options: {
    userIdField?: string;
    createdByField?: string;
    ownersField?: string;
    targetUserId?: string;
  } = {}
): Promise<any> => {
  const { userIdField = 'userId', createdByField = 'createdBy', ownersField = 'owners', targetUserId } = options;

  // Admin can see all data, optionally filtered by target user
  if (user.role === 'ADMIN') {
    if (targetUserId) {
      return {
        ...baseQuery,
        $or: [{ [createdByField]: targetUserId }, { [userIdField]: targetUserId }, { [`${ownersField}.owner`]: targetUserId }],
      };
    }
    return baseQuery; // No additional filtering for admin
  }

  // Operator can see data for assigned customers
  if (user.role === 'OPERATOR') {
    const assignedCustomerIds = await getAssignedCustomerIds((user._id as any).toString());

    if (assignedCustomerIds.length === 0) {
      // Operator has no assignments, return query that matches nothing
      return { ...baseQuery, _id: { $in: [] } };
    }

    const operatorAccessQuery = {
      $or: [{ [createdByField]: { $in: assignedCustomerIds } }, { [userIdField]: { $in: assignedCustomerIds } }],
    };

    // Add owners field if it exists (for properties)
    if (ownersField) {
      operatorAccessQuery.$or.push({ [`${ownersField}.owner`]: { $in: assignedCustomerIds } });
    }

    return {
      ...baseQuery,
      ...operatorAccessQuery,
    };
  }

  // For customers, restrict to their own data
  const userAccessQuery = {
    $or: [{ [createdByField]: user._id }, { [userIdField]: user._id }],
  };

  // Add owners field if it exists (for properties)
  if (ownersField) {
    userAccessQuery.$or.push({ [`${ownersField}.owner`]: user._id });
  }

  return {
    ...baseQuery,
    ...userAccessQuery,
  };
};

// Check if user can access specific resource
export const canAccessResource = async (
  user: IUser,
  resourceId: string,
  model: any,
  options: {
    createdByField?: string;
    ownersField?: string;
  } = {}
): Promise<boolean> => {
  const { createdByField = 'createdBy', ownersField } = options;

  // Admin can access any resource
  if (user.role === 'ADMIN') {
    return true;
  }

  // For operators, check if they can access through assigned customers
  if (user.role === 'OPERATOR') {
    const assignedCustomerIds = await getAssignedCustomerIds((user._id as any).toString());

    if (assignedCustomerIds.length === 0) {
      return false;
    }

    const operatorAccessQuery: any = {
      _id: resourceId,
      $or: [{ [createdByField]: { $in: assignedCustomerIds } }],
    };

    // Add owners field if specified
    if (ownersField) {
      operatorAccessQuery.$or.push({ [`${ownersField}.owner`]: { $in: assignedCustomerIds } });
    }

    const resource = await model.findOne(operatorAccessQuery);
    return !!resource;
  }

  // Build query to check access for customers
  const accessQuery: any = {
    _id: resourceId,
    $or: [{ [createdByField]: user._id }],
  };

  // Add owners field if specified
  if (ownersField) {
    accessQuery.$or.push({ [`${ownersField}.owner`]: user._id });
  }

  const resource = await model.findOne(accessQuery);
  return !!resource;
};

// Get assigned customer IDs for an operator
export const getAssignedCustomerIds = async (operatorId: string): Promise<string[]> => {
  const assignments = await OperatorAssignment.find({
    operatorId,
    isActive: true,
  }).select('customerId');

  return assignments.map((assignment) => assignment.customerId.toString());
};

// Check if operator can access customer data
export const canOperatorAccessCustomer = async (operatorId: string, customerId: string): Promise<boolean> => {
  const assignment = await OperatorAssignment.findOne({
    operatorId,
    customerId,
    isActive: true,
  });

  return !!assignment;
};

// Get operator permissions for a specific customer
export const getOperatorPermissions = async (operatorId: string, customerId: string) => {
  const assignment = await OperatorAssignment.findOne({
    operatorId,
    customerId,
    isActive: true,
  });

  return assignment?.permissions || null;
};

// Middleware to validate operator customer context
export const validateOperatorCustomerAccess = (customerIdParam: string = 'customerId') => {
  return async (c: Context, next: Next) => {
    const user = c.get('user') as IUser;

    // Skip validation for admins
    if (user.role === 'ADMIN') {
      await next();
      return;
    }

    // Skip validation for customers (they can only access their own data)
    if (user.role === 'CUSTOMER') {
      await next();
      return;
    }

    // For operators, validate customer access
    if (user.role === 'OPERATOR') {
      const customerId = c.req.param(customerIdParam) || c.req.query(customerIdParam);

      if (!customerId) {
        return c.json({ error: 'Customer ID is required for operators' }, 400);
      }

      const hasAccess = await canOperatorAccessCustomer((user._id as any).toString(), customerId);

      if (!hasAccess) {
        return c.json({ error: 'Access denied. You are not assigned to this customer.' }, 403);
      }
    }

    await next();
  };
};

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import authRoutes from '../../routes/auth';
import guestRoutes from '../../routes/guests';
import partnerRoutes from '../../routes/partners';
import propertyRoutes from '../../routes/properties';
import propertyServicesRoutes from '../../routes/propertyServices';
import dashboardRoutes from '../../routes/dashboard';
import currencyRoutes from '../../routes/currencies';
import shortTermRentalsRoutes from '../../routes/shortTermRentals';
import adminRoutes from '../../routes/admin';
import operatorAssignmentRoutes from '../../routes/operatorAssignments';

export function createTestApp() {
  const app = new Hono();

  // CORS middleware
  app.use(
    '*',
    cors({
      origin: '*',
      allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowHeaders: ['Content-Type', 'Authorization'],
    })
  );

  // Routes
  app.route('/api/auth', authRoutes);
  app.route('/api/guests', guestRoutes);
  app.route('/api/partners', partnerRoutes);
  app.route('/api/properties', propertyRoutes);
  app.route('/api/properties', propertyServicesRoutes);
  app.route('/api/dashboard', dashboardRoutes);
  app.route('/api/currencies', currencyRoutes);
  app.route('/api/short-term-rentals', shortTermRentalsRoutes);
  app.route('/api/admin', adminRoutes);
  app.route('/api/operator-assignments', operatorAssignmentRoutes);

  return app;
}

// Export a default test app instance
export const testApp = createTestApp();

export async function testRequest(
  app: Hono,
  method: string,
  path: string,
  options: {
    body?: any;
    headers?: Record<string, string>;
    token?: string;
  } = {}
) {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (options.token) {
    headers.Authorization = `Bearer ${options.token}`;
  }

  const req = new Request(`http://localhost${path}`, {
    method: method.toUpperCase(),
    headers,
    body: options.body ? JSON.stringify(options.body) : undefined,
  });

  const response = await app.request(req);
  let data;

  try {
    data = await response.json();
  } catch {
    data = null;
  }

  return {
    status: response.status,
    body: data,
    response,
  };
}

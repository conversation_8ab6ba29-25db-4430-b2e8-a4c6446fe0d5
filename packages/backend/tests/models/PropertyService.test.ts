import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import PropertyService from '../../models/PropertyService';
import Property from '../../models/Property';
import Currency from '../../models/Currency';
import User from '../../models/User';

describe('PropertyService Model', () => {
  let user: any;
  let currency: any;
  let property: any;

  beforeEach(async () => {
    user = new User({
      fullName: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    });
    await user.save();

    currency = new Currency({
      name: 'US Dollar',
      code: 'USD',
      symbol: '$',
      createdBy: user._id,
    });
    await currency.save();

    property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'residential',
      currencyId: currency._id,
      owners: [
        {
          ownerType: 'User',
          owner: user._id,
          ownershipPercentage: 100,
        },
      ],
      createdBy: user._id,
    });
    await property.save();
  });

  afterEach(async () => {
    await PropertyService.deleteMany({});
    await Property.deleteMany({});
    await Currency.deleteMany({});
    await User.deleteMany({});
  });

  it('should create a property service with valid data', async () => {
    const serviceData = {
      propertyId: property._id,
      name: 'Cleaning Service',
      cost: 100.5,
      frequency: 'monthly',
      mandatory: true,
      status: 'active',
    };

    const service = new PropertyService(serviceData);
    await service.save();

    expect(service.propertyId).toEqual(property._id);
    expect(service.name).toBe('Cleaning Service');
    expect(service.cost).toBe(100.5);
    expect(service.frequency).toBe('monthly');
    expect(service.mandatory).toBe(true);
    expect(service.status).toBe('active');
    expect(service.createdAt).toBeDefined();
    expect(service.updatedAt).toBeDefined();
  });

  it('should require propertyId field', async () => {
    const service = new PropertyService({
      name: 'Cleaning Service',
      cost: 100.5,
      frequency: 'monthly',
      mandatory: true,
      status: 'active',
    });

    await expect(service.save()).rejects.toThrow();
  });

  it('should require name field', async () => {
    const service = new PropertyService({
      propertyId: property._id,
      cost: 100.5,
      frequency: 'monthly',
      mandatory: true,
      status: 'active',
    });

    await expect(service.save()).rejects.toThrow();
  });

  it('should require cost field', async () => {
    const service = new PropertyService({
      propertyId: property._id,
      name: 'Cleaning Service',
      frequency: 'monthly',
      mandatory: true,
      status: 'active',
    });

    await expect(service.save()).rejects.toThrow();
  });

  it('should validate cost is positive', async () => {
    const service = new PropertyService({
      propertyId: property._id,
      name: 'Cleaning Service',
      cost: -10,
      frequency: 'monthly',
      mandatory: true,
      status: 'active',
    });

    await expect(service.save()).rejects.toThrow();
  });

  it('should require frequency field', async () => {
    const service = new PropertyService({
      propertyId: property._id,
      name: 'Cleaning Service',
      cost: 100.5,
      mandatory: true,
      status: 'active',
    });

    await expect(service.save()).rejects.toThrow();
  });

  it('should validate frequency enum', async () => {
    const service = new PropertyService({
      propertyId: property._id,
      name: 'Cleaning Service',
      cost: 100.5,
      frequency: 'invalid-frequency',
      mandatory: true,
      status: 'active',
    });

    await expect(service.save()).rejects.toThrow();
  });

  it('should default mandatory to false', async () => {
    const service = new PropertyService({
      propertyId: property._id,
      name: 'Cleaning Service',
      cost: 100.5,
      frequency: 'monthly',
      status: 'active',
    });
    await service.save();

    expect(service.mandatory).toBe(false);
  });

  it('should default status to active', async () => {
    const service = new PropertyService({
      propertyId: property._id,
      name: 'Cleaning Service',
      cost: 100.5,
      frequency: 'monthly',
      mandatory: true,
    });
    await service.save();

    expect(service.status).toBe('active');
  });

  it('should validate status enum', async () => {
    const service = new PropertyService({
      propertyId: property._id,
      name: 'Cleaning Service',
      cost: 100.5,
      frequency: 'monthly',
      mandatory: true,
      status: 'invalid-status',
    });

    await expect(service.save()).rejects.toThrow();
  });

  it('should support all valid frequency values', async () => {
    const frequencies = ['monthly', 'quarterly', 'yearly'];

    for (const frequency of frequencies) {
      const service = new PropertyService({
        propertyId: property._id,
        name: `Service ${frequency}`,
        cost: 100,
        frequency,
        mandatory: true,
        status: 'active',
      });

      await expect(service.save()).resolves.toBeDefined();
    }
  });

  it('should support all valid status values', async () => {
    const statuses = ['active', 'inactive'];

    for (const status of statuses) {
      const service = new PropertyService({
        propertyId: property._id,
        name: `Service ${status}`,
        cost: 100,
        frequency: 'monthly',
        mandatory: true,
        status,
      });

      await expect(service.save()).resolves.toBeDefined();
    }
  });

  it('should trim whitespace from string fields', async () => {
    const service = new PropertyService({
      propertyId: property._id,
      name: '  Cleaning Service  ',
      cost: 100.5,
      frequency: 'monthly',
      mandatory: true,
      status: 'active',
    });
    await service.save();

    expect(service.name).toBe('Cleaning Service');
  });

  it('should find services by property', async () => {
    const property2 = new Property({
      name: 'Test Property 2',
      address: '456 Test Ave',
      propertyType: 'commercial',
      currencyId: currency._id,
      owners: [
        {
          ownerType: 'User',
          owner: user._id,
          ownershipPercentage: 100,
        },
      ],
      createdBy: user._id,
    });
    await property2.save();

    // Create services for property1
    await new PropertyService({
      propertyId: property._id,
      name: 'Service 1',
      cost: 100,
      frequency: 'monthly',
      mandatory: true,
      status: 'active',
    }).save();

    await new PropertyService({
      propertyId: property._id,
      name: 'Service 2',
      cost: 200,
      frequency: 'quarterly',
      mandatory: false,
      status: 'active',
    }).save();

    // Create service for property2
    await new PropertyService({
      propertyId: property2._id,
      name: 'Service 3',
      cost: 150,
      frequency: 'yearly',
      mandatory: true,
      status: 'active',
    }).save();

    const property1Services = await PropertyService.find({
      propertyId: property._id,
    });
    const property2Services = await PropertyService.find({
      propertyId: property2._id,
    });

    expect(property1Services).toHaveLength(2);
    expect(property2Services).toHaveLength(1);
  });

  it('should find active services only', async () => {
    await new PropertyService({
      propertyId: property._id,
      name: 'Active Service',
      cost: 100,
      frequency: 'monthly',
      mandatory: true,
      status: 'active',
    }).save();

    await new PropertyService({
      propertyId: property._id,
      name: 'Inactive Service',
      cost: 200,
      frequency: 'quarterly',
      mandatory: false,
      status: 'inactive',
    }).save();

    const activeServices = await PropertyService.find({
      propertyId: property._id,
      status: 'active',
    });

    expect(activeServices).toHaveLength(1);
    expect(activeServices[0].name).toBe('Active Service');
  });

  it('should calculate monthly equivalent correctly', async () => {
    const monthlyService = new PropertyService({
      propertyId: property._id,
      name: 'Monthly Service',
      cost: 100,
      frequency: 'monthly',
    });
    await monthlyService.save();

    const quarterlyService = new PropertyService({
      propertyId: property._id,
      name: 'Quarterly Service',
      cost: 300,
      frequency: 'quarterly',
    });
    await quarterlyService.save();

    const yearlyService = new PropertyService({
      propertyId: property._id,
      name: 'Yearly Service',
      cost: 1200,
      frequency: 'yearly',
    });
    await yearlyService.save();

    expect(monthlyService.getMonthlyEquivalent()).toBe(100);
    expect(quarterlyService.getMonthlyEquivalent()).toBe(100);
    expect(yearlyService.getMonthlyEquivalent()).toBe(100);
  });
});

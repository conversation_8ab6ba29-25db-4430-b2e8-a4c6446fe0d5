import mongoose from 'mongoose';
import { beforeEach, describe, it, expect } from 'vitest';
import Commission from '../../models/Commission';
import User from '../../models/User';
import Partner from '../../models/Partner';

beforeEach(async () => {
  // Clear all collections before each test
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

describe('Commission Model', () => {
  let testUser: any;
  let testPartner: any;

  beforeEach(async () => {
    testUser = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      fullName: 'Test User',
    });

    testPartner = await Partner.create({
      name: 'Test Partner',
      email: '<EMAIL>',
      createdBy: testUser._id,
    });
  });

  it('should create a valid commission with User receiver', async () => {
    const commissionData = {
      amount: 100.5,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
      status: 'pending',
    };

    const commission = await Commission.create(commissionData);

    expect(commission.amount).toBe(100.5);
    expect(commission.type).toBe('fixed');
    expect(commission.receiverType).toBe('User');
    expect((commission.receiver as any).toString()).toBe(testUser._id.toString());
    expect(commission.status).toBe('pending');
  });

  it('should create a valid commission with Partner receiver', async () => {
    const commissionData = {
      amount: 250.75,
      type: 'percentage',
      receiverType: 'Partner',
      receiver: testPartner._id,
      status: 'paid',
      paidDate: new Date('2024-01-15'),
    };

    const commission = await Commission.create(commissionData);

    expect(commission.amount).toBe(250.75);
    expect(commission.type).toBe('percentage');
    expect(commission.receiverType).toBe('Partner');
    expect((commission.receiver as any).toString()).toBe(testPartner._id.toString());
    expect(commission.status).toBe('paid');
    expect(commission.paidDate).toBeInstanceOf(Date);
  });

  it('should set default status to pending', async () => {
    const commissionData = {
      amount: 100,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
    };

    const commission = await Commission.create(commissionData);

    expect(commission.status).toBe('pending');
  });

  it('should round amount to 2 decimal places', async () => {
    const commissionData = {
      amount: 100.999, // Should be rounded to 101.00
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
    };

    const commission = await Commission.create(commissionData);

    expect(commission.amount).toBe(101);
  });

  it('should validate required fields', async () => {
    const incompleteData = {
      amount: 100,
      // Missing required fields
    };

    await expect(Commission.create(incompleteData)).rejects.toThrow();
  });

  it('should validate amount is not negative', async () => {
    const invalidData = {
      amount: -50,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
    };

    await expect(Commission.create(invalidData)).rejects.toThrow();
  });

  it('should validate commission type enum', async () => {
    const invalidData = {
      amount: 100,
      type: 'invalid_type',
      receiverType: 'User',
      receiver: testUser._id,
    };

    await expect(Commission.create(invalidData)).rejects.toThrow();
  });

  it('should validate commission status enum', async () => {
    const invalidData = {
      amount: 100,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
      status: 'invalid_status',
    };

    await expect(Commission.create(invalidData)).rejects.toThrow();
  });

  it('should validate receiver type enum', async () => {
    const invalidData = {
      amount: 100,
      type: 'fixed',
      receiverType: 'InvalidType',
      receiver: testUser._id,
    };

    await expect(Commission.create(invalidData)).rejects.toThrow();
  });

  it("should populate receiver when it's a User", async () => {
    const commission = await Commission.create({
      amount: 100,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
    });

    const populatedCommission = await Commission.findById(commission._id).populate('receiver');

    expect(populatedCommission?.receiver).toBeDefined();
    expect((populatedCommission?.receiver as any).fullName).toBe('Test User');
  });

  it("should populate receiver when it's a Partner", async () => {
    const commission = await Commission.create({
      amount: 100,
      type: 'fixed',
      receiverType: 'Partner',
      receiver: testPartner._id,
    });

    const populatedCommission = await Commission.findById(commission._id).populate('receiver');

    expect(populatedCommission?.receiver).toBeDefined();
    expect((populatedCommission?.receiver as any).name).toBe('Test Partner');
  });

  it('should handle percentage commission type', async () => {
    const commissionData = {
      amount: 15.5, // 15.5% commission
      type: 'percentage',
      receiverType: 'User',
      receiver: testUser._id,
    };

    const commission = await Commission.create(commissionData);

    expect(commission.type).toBe('percentage');
    expect(commission.amount).toBe(15.5);
  });

  it('should handle paid status with paid date', async () => {
    const paidDate = new Date('2024-01-15');
    const commissionData = {
      amount: 100,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
      status: 'paid',
      paidDate,
    };

    const commission = await Commission.create(commissionData);

    expect(commission.status).toBe('paid');
    expect(commission.paidDate).toEqual(paidDate);
  });

  it('should allow paid date to be undefined for pending commissions', async () => {
    const commissionData = {
      amount: 100,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
      status: 'pending',
    };

    const commission = await Commission.create(commissionData);

    expect(commission.status).toBe('pending');
    expect(commission.paidDate).toBeUndefined();
  });

  it('should have proper timestamps', async () => {
    const commissionData = {
      amount: 100,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
    };

    const commission = await Commission.create(commissionData);

    expect(commission.createdAt).toBeInstanceOf(Date);
    expect(commission.updatedAt).toBeInstanceOf(Date);
  });

  it('should update updatedAt when modified', async () => {
    const commission = await Commission.create({
      amount: 100,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
    });

    const originalUpdatedAt = commission.updatedAt;

    // Wait a bit to ensure timestamp difference
    await new Promise((resolve) => setTimeout(resolve, 10));

    commission.status = 'paid';
    commission.paidDate = new Date();
    await commission.save();

    expect(commission.updatedAt.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
  });

  it('should handle zero amount', async () => {
    const commissionData = {
      amount: 0,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
    };

    const commission = await Commission.create(commissionData);

    expect(commission.amount).toBe(0);
  });

  it('should handle large amounts', async () => {
    const commissionData = {
      amount: 999999.99,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
    };

    const commission = await Commission.create(commissionData);

    expect(commission.amount).toBe(999999.99);
  });
});

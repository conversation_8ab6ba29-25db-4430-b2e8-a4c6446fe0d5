import mongoose from 'mongoose';
import { beforeEach, describe, it, expect } from 'vitest';
import ShortTermRental from '../../models/ShortTermRental';
import User from '../../models/User';
import Property from '../../models/Property';
import Guest from '../../models/Guest';
import Commission from '../../models/Commission';

beforeEach(async () => {
  // Clear all collections before each test
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

describe('ShortTermRental Model', () => {
  let testUser: any;
  let testProperty: any;
  let testGuest: any;

  beforeEach(async () => {
    testUser = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      fullName: 'Test User',
    });

    testProperty = await Property.create({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'residential',
      currencyId: new mongoose.Types.ObjectId(),
      owners: [],
      createdBy: testUser._id,
    });

    testGuest = await Guest.create({
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: new Date('1990-01-01'),
      email: '<EMAIL>',
      documentType: 'Passport',
      documentNumber: '*********',
      phoneNumber: '+*********0',
      isForeigner: false,
      nationality: 'US',
      countryOfOrigin: 'US',
      nextDestination: 'Home',
      cityOfResidency: 'New York',
      cityOfBirth: 'New York',
      tripMotivation: 'Business',
      createdBy: testUser._id,
    });
  });

  it('should create a valid short-term rental', async () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const rentalData = {
      property: testProperty._id,
      propertyId: testProperty._id,
      startDate: tomorrow,
      endDate: nextWeek,
      rentalAmount: 1000,
      costAmount: 200,
      status: 'active',
      isPaid: false,
      guests: [
        {
          guest: testGuest._id,
          isPrincipalContact: true,
        },
      ],
      commissions: [],
      referenceNumber: 'STR-2024-0001',
      createdBy: testUser._id,
    };

    const rental = await ShortTermRental.create(rentalData);

    expect(rental.rentalAmount).toBe(1000);
    expect(rental.costAmount).toBe(200);
    expect(rental.status).toBe('active');
    expect(rental.guests).toHaveLength(1);
    expect(rental.guests[0].isPrincipalContact).toBe(true);
    expect(rental.referenceNumber).toBe('STR-2024-0001');
  });

  it('should auto-generate reference number', async () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const rentalData = {
      property: testProperty._id,
      propertyId: testProperty._id,
      startDate: tomorrow,
      endDate: nextWeek,
      rentalAmount: 1000,
      costAmount: 200,
      status: 'active',
      isPaid: false,
      guests: [
        {
          guest: testGuest._id,
          isPrincipalContact: true,
        },
      ],
      commissions: [],
      createdBy: testUser._id,
    };

    const rental = await ShortTermRental.create(rentalData);

    expect(rental.referenceNumber).toMatch(/STR-\d{4}-\d{4}/);
  });

  it('should validate start date is not in the past', async () => {
    const rentalData = {
      property: testProperty._id,
      propertyId: testProperty._id,
      startDate: new Date('2020-01-01'), // Past date
      endDate: new Date('2020-01-07'),
      rentalAmount: 1000,
      costAmount: 200,
      status: 'active',
      isPaid: false,
      guests: [
        {
          guest: testGuest._id,
          isPrincipalContact: true,
        },
      ],
      commissions: [],
      referenceNumber: 'STR-2024-0001',
      createdBy: testUser._id,
    };

    await expect(ShortTermRental.create(rentalData)).rejects.toThrow();
  });

  it('should validate end date is after start date', async () => {
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);

    const rentalData = {
      property: testProperty._id,
      propertyId: testProperty._id,
      startDate: nextWeek, // Later date
      endDate: tomorrow, // Earlier date - should fail
      rentalAmount: 1000,
      costAmount: 200,
      status: 'active',
      isPaid: false,
      guests: [
        {
          guest: testGuest._id,
          isPrincipalContact: true,
        },
      ],
      commissions: [],
      referenceNumber: 'STR-2024-0001',
      createdBy: testUser._id,
    };

    await expect(ShortTermRental.create(rentalData)).rejects.toThrow();
  });

  it('should validate rental duration is less than 365 days', async () => {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() + 1); // Tomorrow
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 367); // 366 days from tomorrow = more than 365 days

    const rentalData = {
      property: testProperty._id,
      propertyId: testProperty._id,
      startDate,
      endDate,
      rentalAmount: 1000,
      costAmount: 200,
      status: 'active',
      isPaid: false,
      guests: [
        {
          guest: testGuest._id,
          isPrincipalContact: true,
        },
      ],
      commissions: [],
      referenceNumber: 'STR-2024-0001',
      createdBy: testUser._id,
    };

    await expect(ShortTermRental.create(rentalData)).rejects.toThrow('Short-term rental cannot exceed 365 days');
  });

  it('should validate exactly one principal contact', async () => {
    const secondGuest = await Guest.create({
      firstName: 'Jane',
      lastName: 'Smith',
      dateOfBirth: new Date('1985-05-15'),
      email: '<EMAIL>',
      documentType: 'National ID',
      documentNumber: '987654321',
      phoneNumber: '+0987654321',
      isForeigner: true,
      nationality: 'CA',
      countryOfOrigin: 'Canada',
      nextDestination: 'Toronto',
      cityOfResidency: 'Toronto',
      cityOfBirth: 'Toronto',
      tripMotivation: 'Tourism',
      createdBy: testUser._id,
    });

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const rentalData = {
      property: testProperty._id,
      propertyId: testProperty._id,
      startDate: tomorrow,
      endDate: nextWeek,
      rentalAmount: 1000,
      costAmount: 200,
      status: 'active',
      isPaid: false,
      guests: [
        {
          guest: testGuest._id,
          isPrincipalContact: true,
        },
        {
          guest: secondGuest._id,
          isPrincipalContact: true, // Two principal contacts - should fail
        },
      ],
      commissions: [],
      referenceNumber: 'STR-2024-0001',
      createdBy: testUser._id,
    };

    await expect(ShortTermRental.create(rentalData)).rejects.toThrow('Exactly one guest must be designated as principal contact');
  });

  it("should validate discount amount doesn't exceed profit", async () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const rentalData = {
      property: testProperty._id,
      propertyId: testProperty._id,
      startDate: tomorrow,
      endDate: nextWeek,
      rentalAmount: 1000,
      costAmount: 200,
      discountAmount: 900, // Discount exceeds profit (1000 - 200 = 800)
      status: 'active',
      isPaid: false,
      guests: [
        {
          guest: testGuest._id,
          isPrincipalContact: true,
        },
      ],
      commissions: [],
      referenceNumber: 'STR-2024-0001',
      createdBy: testUser._id,
    };

    await expect(ShortTermRental.create(rentalData)).rejects.toThrow('Discount amount cannot exceed profit');
  });

  it('should round monetary amounts to 2 decimal places', async () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const rentalData = {
      property: testProperty._id,
      propertyId: testProperty._id,
      startDate: tomorrow,
      endDate: nextWeek,
      rentalAmount: 1000.999, // Should be rounded to 1001.00
      costAmount: 200.555, // Should be rounded to 200.56
      discountAmount: 50.123, // Should be rounded to 50.12
      discountReason: 'Test discount',
      status: 'active',
      isPaid: false,
      guests: [
        {
          guest: testGuest._id,
          isPrincipalContact: true,
        },
      ],
      commissions: [],
      referenceNumber: 'STR-2024-0001',
      createdBy: testUser._id,
    };

    const rental = await ShortTermRental.create(rentalData);

    expect(rental.rentalAmount).toBe(1001);
    expect(rental.costAmount).toBe(200.56);
    expect(rental.discountAmount).toBe(50.12);
  });

  it('should populate all related data', async () => {
    const commission = await Commission.create({
      amount: 100,
      type: 'fixed',
      receiverType: 'User',
      receiver: testUser._id,
      status: 'pending',
    });

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const rental = await ShortTermRental.create({
      property: testProperty._id,
      propertyId: testProperty._id,
      startDate: tomorrow,
      endDate: nextWeek,
      rentalAmount: 1000,
      costAmount: 200,
      status: 'active',
      isPaid: false,
      guests: [
        {
          guest: testGuest._id,
          isPrincipalContact: true,
        },
      ],
      commissions: [commission._id],
      referenceNumber: 'STR-2024-0001',
      createdBy: testUser._id,
    });

    const populatedRental = await rental.populate([{ path: 'property' }, { path: 'guests.guest' }, { path: 'commissions' }, { path: 'createdBy' }]);

    expect((populatedRental.property as any).name).toBe('Test Property');
    expect((populatedRental.guests[0].guest as any).firstName).toBe('John');
    expect((populatedRental.commissions[0] as any).amount).toBe(100);
    expect((populatedRental.createdBy as any).fullName).toBe('Test User');
  });

  it('should validate required fields', async () => {
    const incompleteData = {
      property: testProperty._id,
      // Missing required fields
    };

    await expect(ShortTermRental.create(incompleteData)).rejects.toThrow();
  });

  it('should set default values correctly', async () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);

    const rentalData = {
      property: testProperty._id,
      propertyId: testProperty._id,
      startDate: tomorrow,
      endDate: nextWeek,
      rentalAmount: 1000,
      costAmount: 200,
      guests: [
        {
          guest: testGuest._id,
          isPrincipalContact: true,
        },
      ],
      commissions: [],
      referenceNumber: 'STR-2024-0001',
      createdBy: testUser._id,
    };

    const rental = await ShortTermRental.create(rentalData);

    expect(rental.status).toBe('active'); // Default status
    expect(rental.isPaid).toBe(false); // Default isPaid
  });

  it('should create unique reference numbers', async () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date();
    nextWeek.setDate(nextWeek.getDate() + 7);
    const twoWeeks = new Date();
    twoWeeks.setDate(twoWeeks.getDate() + 14);
    const threeWeeks = new Date();
    threeWeeks.setDate(threeWeeks.getDate() + 21);

    const rentalData1 = {
      property: testProperty._id,
      propertyId: testProperty._id,
      startDate: tomorrow,
      endDate: nextWeek,
      rentalAmount: 1000,
      costAmount: 200,
      status: 'active',
      isPaid: false,
      guests: [{ guest: testGuest._id, isPrincipalContact: true }],
      commissions: [],
      createdBy: testUser._id,
    };

    const rentalData2 = {
      ...rentalData1,
      startDate: twoWeeks,
      endDate: threeWeeks,
    };

    const rental1 = await ShortTermRental.create(rentalData1);
    const rental2 = await ShortTermRental.create(rentalData2);

    expect(rental1.referenceNumber).not.toBe(rental2.referenceNumber);
    expect(rental1.referenceNumber).toMatch(/STR-\d{4}-0001/);
    expect(rental2.referenceNumber).toMatch(/STR-\d{4}-0002/);
  });

  it('should save rental with discount correctly', async () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 3);

    const rental = new ShortTermRental({
      property: testProperty._id,
      startDate: futureDate,
      endDate: endDate,
      rentalAmount: 1000,
      costAmount: 300,
      discountAmount: 100,
      discountReason: 'Early booking discount',
      guests: [{ guest: testGuest._id, isPrincipalContact: true }],
      status: 'active',
      createdBy: testUser._id,
    });

    await rental.save();
    expect(rental.discountAmount).toBe(100);
    expect(rental.discountReason).toBe('Early booking discount');
  });

  it('should handle discount validation', async () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 3);

    const rental = new ShortTermRental({
      property: testProperty._id,
      startDate: futureDate,
      endDate: endDate,
      rentalAmount: 1000,
      costAmount: 300,
      discountAmount: 1500, // More than profit
      guests: [{ guest: testGuest._id, isPrincipalContact: true }],
      status: 'active',
      createdBy: testUser._id,
    });

    await expect(rental.save()).rejects.toThrow();
  });

  it('should handle commissions correctly', async () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 3);

    // Create test commissions
    const commission1 = await Commission.create({
      amount: 50,
      type: 'fixed',
      receiverType: 'Partner',
      receiver: new mongoose.Types.ObjectId(),
      status: 'pending',
      createdBy: testUser._id,
    });

    const commission2 = await Commission.create({
      amount: 10, // 10% of 1000 = 100
      type: 'percentage',
      receiverType: 'Partner',
      receiver: new mongoose.Types.ObjectId(),
      status: 'pending',
      createdBy: testUser._id,
    });

    const rental = new ShortTermRental({
      property: testProperty._id,
      startDate: futureDate,
      endDate: endDate,
      rentalAmount: 1000,
      costAmount: 300,
      commissions: [commission1._id, commission2._id],
      guests: [{ guest: testGuest._id, isPrincipalContact: true }],
      status: 'active',
      createdBy: testUser._id,
    });

    await rental.save();
    expect(rental.commissions).toHaveLength(2);
  });

  it('should handle discount with commissions', async () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 3);

    const commission = await Commission.create({
      amount: 5, // 5% of 1000 = 50
      type: 'percentage',
      receiverType: 'Partner',
      receiver: new mongoose.Types.ObjectId(),
      status: 'pending',
      createdBy: testUser._id,
    });

    const rental = new ShortTermRental({
      property: testProperty._id,
      startDate: futureDate,
      endDate: endDate,
      rentalAmount: 1000,
      costAmount: 300,
      discountAmount: 100,
      discountReason: 'Early booking discount',
      commissions: [commission._id],
      guests: [{ guest: testGuest._id, isPrincipalContact: true }],
      status: 'active',
      createdBy: testUser._id,
    });

    await rental.save();
    expect(rental.discountAmount).toBe(100);
    expect(rental.discountReason).toBe('Early booking discount');
    expect(rental.commissions).toHaveLength(1);
  });

  it('should validate discount reason when discount amount is provided', async () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 3);

    const rental = new ShortTermRental({
      property: testProperty._id,
      startDate: futureDate,
      endDate: endDate,
      rentalAmount: 1000,
      costAmount: 300,
      discountAmount: 100,
      // Missing discountReason
      guests: [{ guest: testGuest._id, isPrincipalContact: true }],
      status: 'active',
      createdBy: testUser._id,
    });

    await expect(rental.save()).rejects.toThrow();
  });

  it('should allow empty discount fields', async () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 3);

    const rental = new ShortTermRental({
      property: testProperty._id,
      startDate: futureDate,
      endDate: endDate,
      rentalAmount: 1000,
      costAmount: 300,
      guests: [{ guest: testGuest._id, isPrincipalContact: true }],
      status: 'active',
      createdBy: testUser._id,
    });

    await rental.save();
    expect(rental.discountAmount).toBeUndefined();
    expect(rental.discountReason).toBeUndefined();
  });

  it('should validate total discounts and commissions do not exceed profit', async () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 3);

    // Create a commission that, combined with discount, exceeds profit
    const commission = await Commission.create({
      amount: 400, // Fixed amount
      type: 'fixed',
      receiverType: 'Partner',
      receiver: new mongoose.Types.ObjectId(),
      status: 'pending',
      createdBy: testUser._id,
    });

    const rental = new ShortTermRental({
      property: testProperty._id,
      startDate: futureDate,
      endDate: endDate,
      rentalAmount: 1000,
      costAmount: 300, // Profit = 700
      discountAmount: 400, // Discount + Commission (400 + 400) = 800 > 700 (profit)
      discountReason: 'Test discount',
      commissions: [commission._id],
      guests: [{ guest: testGuest._id, isPrincipalContact: true }],
      status: 'active',
      createdBy: testUser._id,
    });

    await expect(rental.save()).rejects.toThrow('Total of discounts and commissions cannot exceed the profit');
  });

  it('should allow discounts and commissions that equal profit', async () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1);
    const endDate = new Date();
    endDate.setDate(endDate.getDate() + 3);

    // Create a commission
    const commission = await Commission.create({
      amount: 300, // Fixed amount
      type: 'fixed',
      receiverType: 'Partner',
      receiver: new mongoose.Types.ObjectId(),
      status: 'pending',
      createdBy: testUser._id,
    });

    const rental = new ShortTermRental({
      property: testProperty._id,
      startDate: futureDate,
      endDate: endDate,
      rentalAmount: 1000,
      costAmount: 300, // Profit = 700
      discountAmount: 400, // Discount + Commission (400 + 300) = 700 = profit
      discountReason: 'Test discount',
      commissions: [commission._id],
      guests: [{ guest: testGuest._id, isPrincipalContact: true }],
      status: 'active',
      createdBy: testUser._id,
    });

    await rental.save();
    expect(rental.discountAmount).toBe(400);
    expect(rental.commissions).toHaveLength(1);
  });
});

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import Partner from '../../models/Partner';
import User from '../../models/User';

describe('Partner Model', () => {
  let user: any;

  beforeEach(async () => {
    user = new User({
      fullName: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    });
    await user.save();
  });

  afterEach(async () => {
    await Partner.deleteMany({});
    await User.deleteMany({});
  });

  it('should create a partner with valid data', async () => {
    const partnerData = {
      name: '<PERSON>',
      email: '<EMAIL>',
      createdBy: user._id,
    };

    const partner = new Partner(partnerData);
    await partner.save();

    expect(partner.name).toBe('<PERSON>');
    expect(partner.email).toBe('<EMAIL>');
    expect(partner.createdBy).toEqual(user._id);
    expect(partner.createdAt).toBeDefined();
    expect(partner.updatedAt).toBeDefined();
  });

  it('should require name field', async () => {
    const partner = new Partner({
      email: '<EMAIL>',
      createdBy: user._id,
    });

    await expect(partner.save()).rejects.toThrow();
  });

  it('should require email field', async () => {
    const partner = new Partner({
      name: 'John Doe',
      createdBy: user._id,
    });

    await expect(partner.save()).rejects.toThrow();
  });

  it('should allow any email format (no validation)', async () => {
    const partner = new Partner({
      name: 'John Doe',
      email: 'invalid-email',
      createdBy: user._id,
    });

    await expect(partner.save()).resolves.toBeDefined();
  });

  it('should require createdBy field', async () => {
    const partner = new Partner({
      name: 'John Doe',
      email: '<EMAIL>',
    });

    await expect(partner.save()).rejects.toThrow();
  });

  it('should allow duplicate emails per user (no unique constraint)', async () => {
    const partnerData = {
      name: 'John Doe',
      email: '<EMAIL>',
      createdBy: user._id,
    };

    const partner1 = new Partner(partnerData);
    await partner1.save();

    const partner2 = new Partner({
      ...partnerData,
      name: 'Jane Doe',
    });

    await expect(partner2.save()).resolves.toBeDefined();
  });

  it('should allow same email for different users', async () => {
    const user2 = new User({
      fullName: 'Test User 2',
      email: '<EMAIL>',
      password: 'password123',
    });
    await user2.save();

    const partner1 = new Partner({
      name: 'John Doe',
      email: '<EMAIL>',
      createdBy: user._id,
    });
    await partner1.save();

    const partner2 = new Partner({
      name: 'Jane Doe',
      email: '<EMAIL>',
      createdBy: user2._id,
    });

    await expect(partner2.save()).resolves.toBeDefined();
  });

  it('should trim whitespace from string fields', async () => {
    const partner = new Partner({
      name: '  John Doe  ',
      email: '  <EMAIL>  ',
      createdBy: user._id,
    });
    await partner.save();

    expect(partner.name).toBe('John Doe');
    expect(partner.email).toBe('<EMAIL>');
  });

  it('should convert email to lowercase', async () => {
    const partner = new Partner({
      name: 'John Doe',
      email: '<EMAIL>',
      createdBy: user._id,
    });
    await partner.save();

    expect(partner.email).toBe('<EMAIL>');
  });

  it('should have proper JSON representation', async () => {
    const partner = new Partner({
      name: 'John Doe',
      email: '<EMAIL>',
      createdBy: user._id,
    });
    await partner.save();

    const json = partner.toJSON();
    expect(json).toHaveProperty('_id');
    expect(json).toHaveProperty('name', 'John Doe');
    expect(json).toHaveProperty('email', '<EMAIL>');
    expect(json).toHaveProperty('createdBy');
    expect(json).toHaveProperty('createdAt');
    expect(json).toHaveProperty('updatedAt');
  });

  it('should find partners by user', async () => {
    const user2 = new User({
      fullName: 'Test User 2',
      email: '<EMAIL>',
      password: 'password123',
    });
    await user2.save();

    // Create partners for user1
    await new Partner({
      name: 'Partner 1',
      email: '<EMAIL>',
      createdBy: user._id,
    }).save();

    await new Partner({
      name: 'Partner 2',
      email: '<EMAIL>',
      createdBy: user._id,
    }).save();

    // Create partner for user2
    await new Partner({
      name: 'Partner 3',
      email: '<EMAIL>',
      createdBy: user2._id,
    }).save();

    const user1Partners = await Partner.find({ createdBy: user._id });
    const user2Partners = await Partner.find({ createdBy: user2._id });

    expect(user1Partners).toHaveLength(2);
    expect(user2Partners).toHaveLength(1);
  });

  it('should support searching by name', async () => {
    await new Partner({
      name: 'John Doe',
      email: '<EMAIL>',
      createdBy: user._id,
    }).save();

    await new Partner({
      name: 'Jane Smith',
      email: '<EMAIL>',
      createdBy: user._id,
    }).save();

    await new Partner({
      name: 'Bob Johnson',
      email: '<EMAIL>',
      createdBy: user._id,
    }).save();

    const searchResults = await Partner.find({
      createdBy: user._id,
      name: { $regex: 'John', $options: 'i' },
    });

    expect(searchResults).toHaveLength(2); // John Doe and Bob Johnson
  });

  it('should support searching by email', async () => {
    await new Partner({
      name: 'John Doe',
      email: '<EMAIL>',
      createdBy: user._id,
    }).save();

    await new Partner({
      name: 'Jane Smith',
      email: '<EMAIL>',
      createdBy: user._id,
    }).save();

    const searchResults = await Partner.find({
      createdBy: user._id,
      email: { $regex: 'example', $options: 'i' },
    });

    expect(searchResults).toHaveLength(1); // Only <EMAIL>
  });
});

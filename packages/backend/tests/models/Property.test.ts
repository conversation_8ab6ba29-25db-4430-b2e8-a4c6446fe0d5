import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import Property from '../../models/Property';
import Currency from '../../models/Currency';
import User from '../../models/User';

describe('Property Model', () => {
  let user: any;
  let currency: any;

  beforeEach(async () => {
    user = new User({
      fullName: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    });
    await user.save();

    currency = new Currency({
      name: 'US Dollar',
      code: 'USD',
      symbol: '$',
      createdBy: user._id,
    });
    await currency.save();
  });

  afterEach(async () => {
    await Property.deleteMany({});
    await Currency.deleteMany({});
    await User.deleteMany({});
  });

  it('should create a property with valid data', async () => {
    const propertyData = {
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'residential',
      currencyId: currency._id,
      owners: [
        {
          ownerType: 'User',
          owner: user._id,
          ownershipPercentage: 100,
        },
      ],
      createdBy: user._id,
    };

    const property = new Property(propertyData);
    await property.save();

    expect(property.name).toBe('Test Property');
    expect(property.address).toBe('123 Test St');
    expect(property.propertyType).toBe('residential');
    expect(property.currencyId).toEqual(currency._id);
    expect(property.owners).toHaveLength(1);
    expect(property.owners[0].ownerType).toBe('User');
    expect(property.owners[0].owner).toEqual(user._id);
    expect(property.owners[0].ownershipPercentage).toBe(100);
    expect(property.createdBy).toEqual(user._id);
  });

  it('should require name field', async () => {
    const property = new Property({
      address: '123 Test St',
      propertyType: 'apartment',
      currencyId: currency._id,
      owners: [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          percentage: 100,
        },
      ],
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should require address field', async () => {
    const property = new Property({
      name: 'Test Property',
      propertyType: 'apartment',
      currencyId: currency._id,
      owners: [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          percentage: 100,
        },
      ],
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should require propertyType field', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      currencyId: currency._id,
      owners: [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          percentage: 100,
        },
      ],
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should validate propertyType enum', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'invalid-type',
      currencyId: currency._id,
      owners: [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          percentage: 100,
        },
      ],
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should require currencyId field', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'apartment',
      owners: [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          percentage: 100,
        },
      ],
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should require owners field', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'apartment',
      currencyId: currency._id,
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should require at least one owner', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'apartment',
      currencyId: currency._id,
      owners: [],
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should require owner name', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'apartment',
      currencyId: currency._id,
      owners: [
        {
          email: '<EMAIL>',
          percentage: 100,
        },
      ],
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should require owner email', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'apartment',
      currencyId: currency._id,
      owners: [
        {
          name: 'John Doe',
          percentage: 100,
        },
      ],
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should require owner percentage', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'apartment',
      currencyId: currency._id,
      owners: [
        {
          name: 'John Doe',
          email: '<EMAIL>',
        },
      ],
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should validate owner percentage range', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'apartment',
      currencyId: currency._id,
      owners: [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          percentage: 150, // Invalid: > 100
        },
      ],
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should validate owner email format', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'apartment',
      currencyId: currency._id,
      owners: [
        {
          name: 'John Doe',
          email: 'invalid-email',
          percentage: 100,
        },
      ],
      createdBy: user._id,
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should require createdBy field', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'apartment',
      currencyId: currency._id,
      owners: [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          percentage: 100,
        },
      ],
    });

    await expect(property.save()).rejects.toThrow();
  });

  it('should support multiple owners', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'residential',
      currencyId: currency._id,
      owners: [
        {
          ownerType: 'User',
          owner: user._id,
          ownershipPercentage: 60,
        },
        {
          ownerType: 'User',
          owner: user._id,
          ownershipPercentage: 40,
        },
      ],
      createdBy: user._id,
    });
    await property.save();

    expect(property.owners).toHaveLength(2);
    expect(property.owners[0].ownershipPercentage).toBe(60);
    expect(property.owners[1].ownershipPercentage).toBe(40);
  });

  it('should not trim whitespace from string fields (basic model)', async () => {
    const property = new Property({
      name: '  Test Property  ',
      address: '  123 Test St  ',
      propertyType: 'residential',
      currencyId: currency._id,
      owners: [
        {
          ownerType: 'User',
          owner: user._id,
          ownershipPercentage: 100,
        },
      ],
      createdBy: user._id,
    });
    await property.save();

    expect(property.name).toBe('  Test Property  ');
    expect(property.address).toBe('  123 Test St  ');
  });

  it('should work with basic owner structure', async () => {
    const property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'residential',
      currencyId: currency._id,
      owners: [
        {
          ownerType: 'User',
          owner: user._id,
          ownershipPercentage: 100,
        },
      ],
      createdBy: user._id,
    });
    await property.save();

    expect(property.owners[0].ownerType).toBe('User');
    expect(property.owners[0].owner).toEqual(user._id);
  });
});

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import Profile from '../../models/Profile';
import User from '../../models/User';

describe('Profile Model', () => {
  let user: any;

  beforeEach(async () => {
    user = new User({
      fullName: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    });
    await user.save();
  });

  afterEach(async () => {
    await Profile.deleteMany({});
    await User.deleteMany({});
  });

  it('should create a profile with valid data', async () => {
    const profileData = {
      userId: user._id,
      age: 30,
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
      notificationSettings: {
        emailNotifications: true,
        pushNotifications: false,
        weeklyReports: true,
        monthlyReports: true,
        budgetAlerts: true,
        investmentUpdates: false,
      },
    };

    const profile = new Profile(profileData);
    await profile.save();

    expect(profile.userId).toEqual(user._id);
    expect(profile.age).toBe(30);
    expect(profile.salary).toBe(50000);
    expect(profile.savingsAmount).toBe(10000);
    expect(profile.investmentRiskLevel).toBe('medium');
    expect(profile.notificationSettings.emailNotifications).toBe(true);
    expect(profile.notificationSettings.pushNotifications).toBe(false);
    expect(profile.notificationSettings.weeklyReports).toBe(true);
    expect(profile.notificationSettings.monthlyReports).toBe(true);
    expect(profile.notificationSettings.budgetAlerts).toBe(true);
    expect(profile.notificationSettings.investmentUpdates).toBe(false);
    expect(profile.createdAt).toBeDefined();
    expect(profile.updatedAt).toBeDefined();
  });

  it('should require userId field', async () => {
    const profile = new Profile({
      age: 30,
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
    });

    await expect(profile.save()).rejects.toThrow();
  });

  it('should validate age minimum value', async () => {
    const profile = new Profile({
      userId: user._id,
      age: 17, // Below minimum
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
    });

    await expect(profile.save()).rejects.toThrow();
  });

  it('should validate age maximum value', async () => {
    const profile = new Profile({
      userId: user._id,
      age: 121, // Above maximum
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
    });

    await expect(profile.save()).rejects.toThrow();
  });

  it('should validate salary minimum value', async () => {
    const profile = new Profile({
      userId: user._id,
      age: 30,
      salary: -1000, // Negative value
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
    });

    await expect(profile.save()).rejects.toThrow();
  });

  it('should validate savingsAmount minimum value', async () => {
    const profile = new Profile({
      userId: user._id,
      age: 30,
      salary: 50000,
      savingsAmount: -5000, // Negative value
      investmentRiskLevel: 'medium',
    });

    await expect(profile.save()).rejects.toThrow();
  });

  it('should validate investmentRiskLevel enum', async () => {
    const profile = new Profile({
      userId: user._id,
      age: 30,
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'invalid-level',
    });

    await expect(profile.save()).rejects.toThrow();
  });

  it('should support all valid investmentRiskLevel values', async () => {
    const riskLevels = ['low', 'medium', 'high'];

    for (const riskLevel of riskLevels) {
      const profile = new Profile({
        userId: user._id,
        age: 30,
        salary: 50000,
        savingsAmount: 10000,
        investmentRiskLevel: riskLevel,
      });

      await expect(profile.save()).resolves.toBeDefined();
      await Profile.findByIdAndDelete(profile._id); // Clean up for next iteration
    }
  });

  it('should have default notification settings', async () => {
    const profile = new Profile({
      userId: user._id,
      age: 30,
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
    });
    await profile.save();

    expect(profile.notificationSettings).toBeDefined();
    expect(profile.notificationSettings.emailNotifications).toBe(true);
    expect(profile.notificationSettings.pushNotifications).toBe(true);
    expect(profile.notificationSettings.weeklyReports).toBe(true);
    expect(profile.notificationSettings.monthlyReports).toBe(true);
    expect(profile.notificationSettings.budgetAlerts).toBe(true);
    expect(profile.notificationSettings.investmentUpdates).toBe(false);
  });

  it('should allow partial notification settings', async () => {
    const profile = new Profile({
      userId: user._id,
      age: 30,
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
      notificationSettings: {
        emailNotifications: false,
      },
    });
    await profile.save();

    expect(profile.notificationSettings.emailNotifications).toBe(false);
    expect(profile.notificationSettings.pushNotifications).toBe(true); // Default
    expect(profile.notificationSettings.weeklyReports).toBe(true); // Default
    expect(profile.notificationSettings.monthlyReports).toBe(true); // Default
    expect(profile.notificationSettings.budgetAlerts).toBe(true); // Default
    expect(profile.notificationSettings.investmentUpdates).toBe(false); // Default
  });

  it('should enforce unique userId', async () => {
    const profileData = {
      userId: user._id,
      age: 30,
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
    };

    const profile1 = new Profile(profileData);
    await profile1.save();

    const profile2 = new Profile({
      ...profileData,
      age: 25,
    });

    await expect(profile2.save()).rejects.toThrow();
  });

  it('should support updating profile fields', async () => {
    const profile = new Profile({
      userId: user._id,
      age: 30,
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
    });
    await profile.save();

    profile.age = 31;
    profile.salary = 55000;
    profile.investmentRiskLevel = 'high';
    await profile.save();

    expect(profile.age).toBe(31);
    expect(profile.salary).toBe(55000);
    expect(profile.investmentRiskLevel).toBe('high');
  });

  it('should support updating notification settings', async () => {
    const profile = new Profile({
      userId: user._id,
      age: 30,
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
    });
    await profile.save();

    profile.notificationSettings.emailNotifications = false;
    profile.notificationSettings.pushNotifications = false;
    profile.notificationSettings.weeklyReports = false;
    await profile.save();

    expect(profile.notificationSettings.emailNotifications).toBe(false);
    expect(profile.notificationSettings.pushNotifications).toBe(false);
    expect(profile.notificationSettings.weeklyReports).toBe(false);
  });

  it('should have proper JSON representation', async () => {
    const profile = new Profile({
      userId: user._id,
      age: 30,
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
    });
    await profile.save();

    const json = profile.toJSON();
    expect(json).toHaveProperty('_id');
    expect(json).toHaveProperty('userId');
    expect(json).toHaveProperty('age', 30);
    expect(json).toHaveProperty('salary', 50000);
    expect(json).toHaveProperty('savingsAmount', 10000);
    expect(json).toHaveProperty('investmentRiskLevel', 'medium');
    expect(json).toHaveProperty('notificationSettings');
    expect(json).toHaveProperty('createdAt');
    expect(json).toHaveProperty('updatedAt');
  });

  it('should find profile by userId', async () => {
    const profile = new Profile({
      userId: user._id,
      age: 30,
      salary: 50000,
      savingsAmount: 10000,
      investmentRiskLevel: 'medium',
    });
    await profile.save();

    const foundProfile = await Profile.findOne({ userId: user._id });
    expect(foundProfile).toBeDefined();
    expect(foundProfile?.age).toBe(30);
    expect(foundProfile?.salary).toBe(50000);
  });

  it('should support optional fields', async () => {
    const profile = new Profile({
      userId: user._id,
    });
    await profile.save();

    expect(profile.userId).toEqual(user._id);
    expect(profile.age).toBeUndefined();
    expect(profile.salary).toBeUndefined();
    expect(profile.savingsAmount).toBe(0); // Has default value;
    expect(profile.investmentRiskLevel).toBe('medium'); // Has default value
    expect(profile.notificationSettings).toBeDefined(); // Has defaults
  });
});

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import mongoose from 'mongoose';
import PropertyService from '../models/PropertyService';
import Property from '../models/Property';
import User from '../models/User';

describe('PropertyService Model', () => {
  let testUser: any;
  let testProperty: any;

  beforeEach(async () => {
    // Create test user
    testUser = new User({
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Test User',
    });
    await testUser.save();

    // Create test property
    testProperty = new Property({
      name: 'Test Property',
      address: '123 Test St',
      description: 'A test property',
      propertyType: 'residential',
      isRented: false,
      createdBy: testUser._id,
      owners: [
        {
          ownerType: 'User',
          owner: testUser._id,
          ownershipPercentage: 100,
        },
      ],
    });
    await testProperty.save();
  });

  afterEach(async () => {
    await PropertyService.deleteMany({});
    await Property.deleteMany({});
    await User.deleteMany({});
  });

  describe('Service Creation', () => {
    it('should create a service with valid data', async () => {
      const serviceData = {
        propertyId: testProperty._id,
        name: 'Internet Service',
        cost: 50.99,
        frequency: 'monthly',
        status: 'active',
        mandatory: true,
        paidByUser: true,
        paidByPartner: false,
      };

      const service = new PropertyService(serviceData);
      await service.save();

      expect(service.name).toBe('Internet Service');
      expect(service.cost).toBe(50.99);
      expect(service.frequency).toBe('monthly');
      expect(service.status).toBe('active');
      expect(service.mandatory).toBe(true);
      expect(service.paidByUser).toBe(true);
      expect(service.paidByPartner).toBe(false);
    });

    it('should set default values correctly', async () => {
      const serviceData = {
        propertyId: testProperty._id,
        name: 'Basic Service',
        cost: 25.0,
        frequency: 'monthly',
      };

      const service = new PropertyService(serviceData);
      await service.save();

      expect(service.status).toBe('active');
      expect(service.mandatory).toBe(false);
      expect(service.paidByUser).toBe(true);
      expect(service.paidByPartner).toBe(false);
    });

    it('should require mandatory fields', async () => {
      const service = new PropertyService({});

      await expect(service.save()).rejects.toThrow();
    });

    it('should validate frequency enum', async () => {
      const serviceData = {
        propertyId: testProperty._id,
        name: 'Invalid Service',
        cost: 25.0,
        frequency: 'invalid',
      };

      const service = new PropertyService(serviceData);
      await expect(service.save()).rejects.toThrow();
    });

    it('should validate status enum', async () => {
      const serviceData = {
        propertyId: testProperty._id,
        name: 'Invalid Service',
        cost: 25.0,
        frequency: 'monthly',
        status: 'invalid',
      };

      const service = new PropertyService(serviceData);
      await expect(service.save()).rejects.toThrow();
    });
  });

  describe('Monthly Equivalent Calculation', () => {
    it('should calculate monthly equivalent for monthly frequency', async () => {
      const service = new PropertyService({
        propertyId: testProperty._id,
        name: 'Monthly Service',
        cost: 100.0,
        frequency: 'monthly',
      });
      await service.save();

      expect(service.getMonthlyEquivalent()).toBe(100.0);
    });

    it('should calculate monthly equivalent for quarterly frequency', async () => {
      const service = new PropertyService({
        propertyId: testProperty._id,
        name: 'Quarterly Service',
        cost: 300.0,
        frequency: 'quarterly',
      });
      await service.save();

      expect(service.getMonthlyEquivalent()).toBe(100.0);
    });

    it('should calculate monthly equivalent for yearly frequency', async () => {
      const service = new PropertyService({
        propertyId: testProperty._id,
        name: 'Yearly Service',
        cost: 1200.0,
        frequency: 'yearly',
      });
      await service.save();

      expect(service.getMonthlyEquivalent()).toBe(100.0);
    });
  });

  describe('Cost Summary Calculation', () => {
    beforeEach(async () => {
      // Create test services
      const services = [
        {
          propertyId: testProperty._id,
          name: 'Internet',
          cost: 50.0,
          frequency: 'monthly',
          status: 'active',
          mandatory: true,
          paidByUser: true,
          paidByPartner: false,
        },
        {
          propertyId: testProperty._id,
          name: 'Insurance',
          cost: 600.0,
          frequency: 'quarterly',
          status: 'active',
          mandatory: true,
          paidByUser: false,
          paidByPartner: true,
        },
        {
          propertyId: testProperty._id,
          name: 'Maintenance',
          cost: 1200.0,
          frequency: 'yearly',
          status: 'active',
          mandatory: false,
          paidByUser: true,
          paidByPartner: true,
        },
        {
          propertyId: testProperty._id,
          name: 'Inactive Service',
          cost: 100.0,
          frequency: 'monthly',
          status: 'inactive',
          mandatory: false,
          paidByUser: true,
          paidByPartner: false,
        },
      ];

      await PropertyService.insertMany(services);
    });

    it('should calculate cost summary correctly', async () => {
      const summary = await PropertyService.calculateCostSummary(testProperty._id);

      // Monthly equivalents: Internet (50) + Insurance (200) + Maintenance (100) = 350
      expect(summary.totalMonthlyCost).toBe(350.0);

      // User pays: Internet (50) + Maintenance (100) = 150
      expect(summary.userMonthlyCost).toBe(150.0);

      // Partner pays: Insurance (200) + Maintenance (100) = 300
      expect(summary.partnerMonthlyCost).toBe(300.0);

      // Mandatory: Internet (50) + Insurance (200) = 250
      expect(summary.mandatoryMonthlyCost).toBe(250.0);

      expect(summary.activeServicesCount).toBe(3);
      expect(summary.totalServicesCount).toBe(4);
    });

    it('should exclude inactive services from calculations', async () => {
      // Set all services to inactive
      await PropertyService.updateMany({ propertyId: testProperty._id }, { status: 'inactive' });

      const summary = await PropertyService.calculateCostSummary(testProperty._id);

      expect(summary.totalMonthlyCost).toBe(0);
      expect(summary.userMonthlyCost).toBe(0);
      expect(summary.partnerMonthlyCost).toBe(0);
      expect(summary.mandatoryMonthlyCost).toBe(0);
      expect(summary.activeServicesCount).toBe(0);
      expect(summary.totalServicesCount).toBe(4);
    });
  });

  describe('Cost Precision', () => {
    it('should store costs with 2 decimal places', async () => {
      const service = new PropertyService({
        propertyId: testProperty._id,
        name: 'Precision Test',
        cost: 123.456,
        frequency: 'monthly',
      });
      await service.save();

      expect(service.cost).toBe(123.46);
    });
  });
});

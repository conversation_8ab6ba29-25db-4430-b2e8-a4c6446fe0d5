import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Hono } from 'hono';
import jwt from 'jsonwebtoken';
import {
  auth,
  buildDataAccessQuery,
  canAccessResource,
  getAssignedCustomerIds,
  canOperatorAccessCustomer,
  getOperatorPermissions,
} from '../../middleware/auth';
import User from '../../models/User';
import OperatorAssignment from '../../models/OperatorAssignment';
import Property from '../../models/Property';

describe('Auth Middleware', () => {
  let user: any;
  let token: string;
  let app: Hono;

  beforeEach(async () => {
    // Create test user
    user = new User({
      fullName: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    });
    await user.save();

    // Generate JWT token
    token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET || 'fallback-secret');

    // Create test app with auth middleware
    app = new Hono();
    app.use('*', auth);
    app.get('/protected', (c) => {
      const user = c.get('user');
      return c.json({ message: 'Access granted', userId: user._id });
    });
  });

  afterEach(async () => {
    await User.deleteMany({});
  });

  it('should allow access with valid token', async () => {
    const response = await app.request('/protected', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.message).toBe('Access granted');
    expect(data.userId).toBe(user._id.toString());
  });

  it('should deny access without token', async () => {
    const response = await app.request('/protected');

    expect(response.status).toBe(401);
    const data = await response.json();
    expect(data.error).toBe('Access denied. No token provided.');
  });

  it('should deny access with invalid token', async () => {
    const response = await app.request('/protected', {
      headers: {
        Authorization: 'Bearer invalid-token',
      },
    });

    expect(response.status).toBe(401);
    const data = await response.json();
    expect(data.error).toBe('Invalid token.');
  });

  it('should deny access with malformed authorization header', async () => {
    const response = await app.request('/protected', {
      headers: {
        Authorization: 'InvalidFormat',
      },
    });

    expect(response.status).toBe(401);
    const data = await response.json();
    expect(data.error).toBe('Invalid token.');
  });

  it('should deny access with token for non-existent user', async () => {
    // Delete the user but keep the token
    await User.findByIdAndDelete(user._id);

    const response = await app.request('/protected', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    expect(response.status).toBe(401);
    const data = await response.json();
    expect(data.error).toBe('Invalid token.');
  });

  it('should deny access with expired token', async () => {
    // Create an expired token
    const expiredToken = jwt.sign({ userId: user._id }, process.env.JWT_SECRET || 'fallback-secret', { expiresIn: '-1h' });

    const response = await app.request('/protected', {
      headers: {
        Authorization: `Bearer ${expiredToken}`,
      },
    });

    expect(response.status).toBe(401);
    const data = await response.json();
    expect(data.error).toBe('Invalid token.');
  });

  it('should set user in context for valid token', async () => {
    app.get('/user-info', (c) => {
      const user = c.get('user');
      return c.json({
        id: user._id,
        email: user.email,
        fullName: user.fullName,
      });
    });

    const response = await app.request('/user-info', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.email).toBe('<EMAIL>');
    expect(data.fullName).toBe('Test User');
    expect(data.id).toBe(user._id.toString());
  });
});

describe('Operator Assignment Auth Functions', () => {
  let adminUser: any;
  let operatorUser: any;
  let customerUser: any;
  let unassignedCustomer: any;
  let property: any;

  beforeEach(async () => {
    // Create test users
    adminUser = await User.create({
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Admin User',
      role: 'ADMIN',
    });

    operatorUser = await User.create({
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Operator User',
      role: 'OPERATOR',
    });

    customerUser = await User.create({
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Customer User',
      role: 'CUSTOMER',
    });

    unassignedCustomer = await User.create({
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Unassigned Customer',
      role: 'CUSTOMER',
    });

    // Create operator assignment
    await OperatorAssignment.create({
      operatorId: operatorUser._id,
      customerId: customerUser._id,
      assignedBy: adminUser._id,
      permissions: {
        canCreateProperties: true,
        canEditProperties: true,
        canDeleteProperties: false,
        canManageRentals: true,
        canViewFinancials: true,
      },
    });

    // Create test property
    property = await Property.create({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'residential',
      createdBy: customerUser._id,
      owners: [
        {
          ownerType: 'User',
          owner: customerUser._id,
          ownershipPercentage: 100,
        },
      ],
    });
  });

  describe('getAssignedCustomerIds', () => {
    it('should return assigned customer IDs for operator', async () => {
      const customerIds = await getAssignedCustomerIds(operatorUser._id.toString());

      expect(customerIds).toHaveLength(1);
      expect(customerIds[0]).toBe(customerUser._id.toString());
    });

    it('should return empty array for operator with no assignments', async () => {
      const anotherOperator = await User.create({
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'Another Operator',
        role: 'OPERATOR',
      });

      const customerIds = await getAssignedCustomerIds(anotherOperator._id.toString());
      expect(customerIds).toHaveLength(0);
    });
  });

  describe('canOperatorAccessCustomer', () => {
    it('should return true for assigned customer', async () => {
      const canAccess = await canOperatorAccessCustomer(operatorUser._id.toString(), customerUser._id.toString());

      expect(canAccess).toBe(true);
    });

    it('should return false for unassigned customer', async () => {
      const canAccess = await canOperatorAccessCustomer(operatorUser._id.toString(), unassignedCustomer._id.toString());

      expect(canAccess).toBe(false);
    });
  });

  describe('buildDataAccessQuery', () => {
    it('should return unrestricted query for admin', async () => {
      const query = await buildDataAccessQuery(adminUser, {});

      expect(query).toEqual({});
    });

    it('should return filtered query for operator', async () => {
      const query = await buildDataAccessQuery(
        operatorUser,
        {},
        {
          createdByField: 'createdBy',
          ownersField: 'owners',
        }
      );

      expect(query.$or).toBeDefined();
      expect(query.$or).toHaveLength(3);
      expect(query.$or[0].createdBy.$in).toContain(customerUser._id.toString());
    });

    it('should return user-specific query for customer', async () => {
      const query = await buildDataAccessQuery(
        customerUser,
        {},
        {
          createdByField: 'createdBy',
          ownersField: 'owners',
        }
      );

      expect(query.$or).toBeDefined();
      expect(query.$or).toHaveLength(3);
      expect(query.$or[0].createdBy).toBe(customerUser._id);
    });
  });

  describe('canAccessResource', () => {
    it('should allow admin to access any resource', async () => {
      const canAccess = await canAccessResource(adminUser, property._id.toString(), Property, { createdByField: 'createdBy', ownersField: 'owners' });

      expect(canAccess).toBe(true);
    });

    it('should allow operator to access assigned customer resource', async () => {
      const canAccess = await canAccessResource(operatorUser, property._id.toString(), Property, { createdByField: 'createdBy', ownersField: 'owners' });

      expect(canAccess).toBe(true);
    });

    it('should deny operator access to unassigned customer resource', async () => {
      const unassignedProperty = await Property.create({
        name: 'Unassigned Property',
        address: '456 Test Ave',
        propertyType: 'residential',
        createdBy: unassignedCustomer._id,
        owners: [
          {
            ownerType: 'User',
            owner: unassignedCustomer._id,
            ownershipPercentage: 100,
          },
        ],
      });

      const canAccess = await canAccessResource(operatorUser, unassignedProperty._id.toString(), Property, {
        createdByField: 'createdBy',
        ownersField: 'owners',
      });

      expect(canAccess).toBe(false);
    });
  });
});

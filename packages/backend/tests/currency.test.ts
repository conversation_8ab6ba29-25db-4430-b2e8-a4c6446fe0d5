import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import mongoose from 'mongoose';
import Currency from '../models/Currency';
import User from '../models/User';
import Property from '../models/Property';

describe('Currency Model Integration', () => {
  let testUser: any;

  beforeEach(async () => {
    // Create a test user
    testUser = new User({
      email: '<EMAIL>',
      password: 'hashedpassword',
      fullName: 'Test User',
    });
    await testUser.save();
  });

  afterEach(async () => {
    await Currency.deleteMany({});
    await User.deleteMany({});
    await Property.deleteMany({});
  });

  describe('Currency Creation', () => {
    it('should create a currency with valid data', async () => {
      const currencyData = {
        name: 'US Dollar',
        symbol: '$',
        code: 'USD',
        createdBy: testUser._id,
      };

      const currency = new Currency(currencyData);
      await currency.save();

      expect(currency.name).toBe('US Dollar');
      expect(currency.symbol).toBe('$');
      expect(currency.code).toBe('USD');
      expect((currency.createdBy as any).toString()).toBe(testUser._id.toString());
    });

    it('should create a currency without code', async () => {
      const currencyData = {
        name: 'Custom Currency',
        symbol: '¤',
        createdBy: testUser._id,
      };

      const currency = new Currency(currencyData);
      await currency.save();

      expect(currency.name).toBe('Custom Currency');
      expect(currency.symbol).toBe('¤');
      expect(currency.code).toBeUndefined();
    });

    it('should require name and symbol', async () => {
      const currency = new Currency({
        createdBy: testUser._id,
      });

      await expect(currency.save()).rejects.toThrow();
    });

    it('should validate currency code format', async () => {
      const currency = new Currency({
        name: 'Test Currency',
        symbol: '$',
        code: 'invalid', // Should be 3 uppercase letters
        createdBy: testUser._id,
      });

      await expect(currency.save()).rejects.toThrow(/validation failed/i);
    });

    it('should enforce unique name per user', async () => {
      const currencyData = {
        name: 'US Dollar',
        symbol: '$',
        createdBy: testUser._id,
      };

      await new Currency(currencyData).save();

      const duplicateCurrency = new Currency({
        ...currencyData,
        symbol: 'USD', // Different symbol but same name
      });

      // The unique constraint should prevent saving the second currency with the same name
      await expect(duplicateCurrency.save()).rejects.toThrow();
    });

    it('should allow same symbol for different currencies by same user', async () => {
      const currency1 = new Currency({
        name: 'US Dollar',
        symbol: '$',
        code: 'USD',
        createdBy: testUser._id,
      });
      await currency1.save();

      const currency2 = new Currency({
        name: 'Canadian Dollar',
        symbol: '$', // Same symbol but different name
        code: 'CAD', // Different code
        createdBy: testUser._id,
      });

      await currency2.save(); // Should not throw
      expect(currency2.symbol).toBe('$');
    });

    it('should allow same name/symbol for different users', async () => {
      const anotherUser = new User({
        email: '<EMAIL>',
        password: 'hashedpassword',
        fullName: 'Another User',
      });
      await anotherUser.save();

      const currency1 = new Currency({
        name: 'US Dollar',
        symbol: '$',
        createdBy: testUser._id,
      });
      await currency1.save();

      const currency2 = new Currency({
        name: 'US Dollar',
        symbol: '$',
        createdBy: anotherUser._id,
      });
      await currency2.save();

      expect(currency1.name).toBe(currency2.name);
      expect(currency1.symbol).toBe(currency2.symbol);
    });
  });

  describe('Default Currencies', () => {
    it('should create default currencies for a new user', async () => {
      await Currency.createDefaultCurrencies(testUser._id.toString());

      const currencies = await Currency.find({ createdBy: testUser._id });
      expect(currencies).toHaveLength(3);

      const currencyNames = currencies.map((c) => c.name);
      expect(currencyNames).toContain('US Dollar');
      expect(currencyNames).toContain('Euro');
      expect(currencyNames).toContain('British Pound');
    });

    it('should not create duplicate default currencies', async () => {
      // Create defaults twice
      await Currency.createDefaultCurrencies(testUser._id.toString());

      // Wait a bit to ensure the first operation completes
      await new Promise((resolve) => setTimeout(resolve, 100));

      await Currency.createDefaultCurrencies(testUser._id.toString());

      const currencies = await Currency.find({ createdBy: testUser._id });
      expect(currencies).toHaveLength(3); // Should still be 3, not 6
    });

    it('should not create defaults if user already has currencies', async () => {
      // Create a custom currency first
      const customCurrency = new Currency({
        name: 'Custom Currency',
        symbol: '¤',
        createdBy: testUser._id,
      });
      await customCurrency.save();

      await Currency.createDefaultCurrencies(testUser._id.toString());

      const currencies = await Currency.find({ createdBy: testUser._id });
      expect(currencies).toHaveLength(1); // Should only have the custom currency
      expect(currencies[0].name).toBe('Custom Currency');
    });
  });

  describe('Currency Usage Check', () => {
    it('should detect when currency is in use by properties', async () => {
      const currency = new Currency({
        name: 'Test Currency',
        symbol: 'T',
        createdBy: testUser._id,
      });
      await currency.save();

      // Create a property using this currency
      const property = new Property({
        name: 'Test Property',
        address: '123 Test St',
        propertyType: 'residential',
        isRented: false,
        createdBy: testUser._id,
        currencyId: currency._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 100,
          },
        ],
      });
      await property.save();

      const isInUse = await currency.isInUse();
      expect(isInUse).toBe(true);
    });

    it('should detect when currency is not in use', async () => {
      const currency = new Currency({
        name: 'Test Currency',
        symbol: 'T',
        createdBy: testUser._id,
      });
      await currency.save();

      const isInUse = await currency.isInUse();
      expect(isInUse).toBe(false);
    });
  });

  describe('Currency Validation', () => {
    it('should trim whitespace from name and symbol', async () => {
      const currency = new Currency({
        name: '  US Dollar  ',
        symbol: '  $  ',
        createdBy: testUser._id,
      });
      await currency.save();

      expect(currency.name).toBe('US Dollar');
      expect(currency.symbol).toBe('$');
    });

    it('should enforce maximum length for name', async () => {
      const longName = 'A'.repeat(51);
      const currency = new Currency({
        name: longName,
        symbol: '$',
        createdBy: testUser._id,
      });

      await expect(currency.save()).rejects.toThrow();
    });

    it('should enforce maximum length for symbol', async () => {
      const currency = new Currency({
        name: 'Test Currency',
        symbol: 'ABCD', // 4 characters, max is 3
        createdBy: testUser._id,
      });

      await expect(currency.save()).rejects.toThrow();
    });

    it('should convert code to uppercase', async () => {
      const currency = new Currency({
        name: 'Test Currency',
        symbol: '$',
        code: 'usd',
        createdBy: testUser._id,
      });
      await currency.save();

      expect(currency.code).toBe('USD');
    });
  });
});

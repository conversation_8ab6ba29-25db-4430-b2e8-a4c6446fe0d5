import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Hono } from 'hono';
import jwt from 'jsonwebtoken';
import Currency from '../../models/Currency';
import User from '../../models/User';
import Property from '../../models/Property';
import { createTestApp } from '../helpers/testApp';

describe('Currency Routes', () => {
  let app: Hono;
  let testUser: any;
  let authToken: string;

  beforeEach(async () => {
    app = createTestApp();

    // Create test user
    testUser = new User({
      email: '<EMAIL>',
      password: 'hashedpassword',
      fullName: 'Test User',
    });
    await testUser.save();

    // Generate auth token
    authToken = jwt.sign({ userId: testUser._id }, process.env.JWT_SECRET || 'fallback-secret');
  });

  afterEach(async () => {
    await Currency.deleteMany({});
    await User.deleteMany({});
    await Property.deleteMany({});
  });

  describe('GET /currencies', () => {
    it('should return all currencies for authenticated user', async () => {
      // Create test currencies
      const currency1 = new Currency({
        name: 'US Dollar',
        symbol: '$',
        code: 'USD',
        createdBy: testUser._id,
      });
      const currency2 = new Currency({
        name: 'Euro',
        symbol: '€',
        code: 'EUR',
        createdBy: testUser._id,
      });
      await currency1.save();
      await currency2.save();

      const res = await app.request('/api/currencies', {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(res.status).toBe(200);
      const data = await res.json();
      expect(data).toHaveLength(2);
      expect(data[0].name).toBe('Euro'); // Should be sorted by createdAt desc
      expect(data[1].name).toBe('US Dollar');
    });

    it('should require authentication', async () => {
      const res = await app.request('/api/currencies');
      expect(res.status).toBe(401);
    });

    it('should return empty array when user has no currencies', async () => {
      const res = await app.request('/api/currencies', {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(res.status).toBe(200);
      const data = await res.json();
      expect(data).toHaveLength(0);
    });
  });

  describe('GET /currencies/:id', () => {
    it('should return specific currency', async () => {
      const currency = new Currency({
        name: 'US Dollar',
        symbol: '$',
        code: 'USD',
        createdBy: testUser._id,
      });
      await currency.save();

      const res = await app.request(`/api/currencies/${currency._id}`, {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(res.status).toBe(200);
      const data = await res.json();
      expect(data.name).toBe('US Dollar');
      expect(data.symbol).toBe('$');
      expect(data.code).toBe('USD');
    });

    it('should return 404 for non-existent currency', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const res = await app.request(`/api/currencies/${fakeId}`, {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(res.status).toBe(404);
    });

    it('should not return currency from another user', async () => {
      const anotherUser = new User({
        email: '<EMAIL>',
        password: 'hashedpassword',
        fullName: 'Another User',
      });
      await anotherUser.save();

      const currency = new Currency({
        name: 'US Dollar',
        symbol: '$',
        createdBy: anotherUser._id,
      });
      await currency.save();

      const res = await app.request(`/api/currencies/${currency._id}`, {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(res.status).toBe(404);
    });
  });

  describe('POST /currencies', () => {
    it('should create a new currency', async () => {
      const currencyData = {
        name: 'US Dollar',
        symbol: '$',
        code: 'USD',
      };

      const res = await app.request('/api/currencies', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(currencyData),
      });

      expect(res.status).toBe(201);
      const data = await res.json();
      expect(data.name).toBe('US Dollar');
      expect(data.symbol).toBe('$');
      expect(data.code).toBe('USD');
      expect(data.createdBy).toBe(testUser._id.toString());
    });

    it('should create currency without code', async () => {
      const currencyData = {
        name: 'Custom Currency',
        symbol: '¤',
      };

      const res = await app.request('/api/currencies', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(currencyData),
      });

      expect(res.status).toBe(201);
      const data = await res.json();
      expect(data.name).toBe('Custom Currency');
      expect(data.symbol).toBe('¤');
      expect(data.code).toBeUndefined();
    });

    it('should return 400 for duplicate name', async () => {
      // Create first currency
      const currency = new Currency({
        name: 'US Dollar',
        symbol: '$',
        createdBy: testUser._id,
      });
      await currency.save();

      // Try to create duplicate
      const currencyData = {
        name: 'US Dollar',
        symbol: 'USD',
      };

      const res = await app.request('/api/currencies', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(currencyData),
      });

      expect(res.status).toBe(400);
      const data = await res.json();
      expect(data.error).toContain('name already exists');
    });

    it('should allow duplicate symbol with different name', async () => {
      // Create first currency
      const currency = new Currency({
        name: 'US Dollar',
        symbol: '$',
        code: 'USD',
        createdBy: testUser._id,
      });
      await currency.save();

      // Create currency with same symbol but different name
      const currencyData = {
        name: 'Canadian Dollar',
        symbol: '$',
        code: 'CAD',
      };

      const res = await app.request('/api/currencies', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(currencyData),
      });

      expect(res.status).toBe(201);
      const data = await res.json();
      expect(data.name).toBe('Canadian Dollar');
      expect(data.symbol).toBe('$');
    });

    it('should validate required fields', async () => {
      const res = await app.request('/api/currencies', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });

      expect(res.status).toBe(400);
    });
  });

  describe('PUT /currencies/:id', () => {
    it('should update currency', async () => {
      const currency = new Currency({
        name: 'US Dollar',
        symbol: '$',
        createdBy: testUser._id,
      });
      await currency.save();

      const updateData = {
        name: 'United States Dollar',
        code: 'USD',
      };

      const res = await app.request(`/api/currencies/${currency._id}`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      expect(res.status).toBe(200);
      const data = await res.json();
      expect(data.name).toBe('United States Dollar');
      expect(data.symbol).toBe('$'); // Should remain unchanged
      expect(data.code).toBe('USD');
    });

    it('should return 404 for non-existent currency', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const res = await app.request(`/api/currencies/${fakeId}`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: 'Updated' }),
      });

      expect(res.status).toBe(404);
    });
  });

  describe('DELETE /currencies/:id', () => {
    it('should delete currency when not in use', async () => {
      const currency = new Currency({
        name: 'US Dollar',
        symbol: '$',
        createdBy: testUser._id,
      });
      await currency.save();

      const res = await app.request(`/api/currencies/${currency._id}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(res.status).toBe(200);
      const data = await res.json();
      expect(data.message).toBe('Currency deleted successfully');

      // Verify currency is deleted
      const deletedCurrency = await Currency.findById(currency._id);
      expect(deletedCurrency).toBeNull();
    });

    it('should not delete currency when in use by properties', async () => {
      const currency = new Currency({
        name: 'US Dollar',
        symbol: '$',
        createdBy: testUser._id,
      });
      await currency.save();

      // Create property using this currency
      const property = new Property({
        name: 'Test Property',
        address: '123 Test St',
        propertyType: 'residential',
        isRented: false,
        createdBy: testUser._id,
        currencyId: currency._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 100,
          },
        ],
      });
      await property.save();

      const res = await app.request(`/api/currencies/${currency._id}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(res.status).toBe(400);
      const data = await res.json();
      expect(data.error).toContain('being used by properties');

      // Verify currency still exists
      const existingCurrency = await Currency.findById(currency._id);
      expect(existingCurrency).not.toBeNull();
    });

    it('should return 404 for non-existent currency', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const res = await app.request(`/api/currencies/${fakeId}`, {
        method: 'DELETE',
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(res.status).toBe(404);
    });
  });
});

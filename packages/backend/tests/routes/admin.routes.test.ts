import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import User from '../../models/User';
import { AuditLog } from '../../models/AuditLog';
import { createTestApp, testRequest } from '../helpers/testApp';

describe('Admin Routes', () => {
  const app = createTestApp();
  let adminUser: any;
  let operatorUser: any;
  let customerUser: any;
  let adminToken: string;
  let operatorToken: string;
  let customerToken: string;

  beforeEach(async () => {
    // Create test users with different roles
    const hashedPassword = await bcrypt.hash('password123', 10);

    adminUser = new User({
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'Admin User',
      role: 'ADMIN',
    });
    await adminUser.save();

    operatorUser = new User({
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'Operator User',
      role: 'OPERATOR',
    });
    await operatorUser.save();

    customerUser = new User({
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'Customer User',
      role: 'CUSTOMER',
    });
    await customerUser.save();

    // Generate auth tokens
    adminToken = jwt.sign({ userId: adminUser._id, role: 'ADMIN' }, process.env.JWT_SECRET || 'fallback-secret', { expiresIn: '7d' });

    operatorToken = jwt.sign({ userId: operatorUser._id, role: 'OPERATOR' }, process.env.JWT_SECRET || 'fallback-secret', { expiresIn: '7d' });

    customerToken = jwt.sign({ userId: customerUser._id, role: 'CUSTOMER' }, process.env.JWT_SECRET || 'fallback-secret', { expiresIn: '7d' });
  });

  afterEach(async () => {
    await User.deleteMany({});
    await AuditLog.deleteMany({});
  });

  describe('GET /api/admin/users', () => {
    it('should allow admin to get all users', async () => {
      const response = await testRequest(app, 'GET', '/api/admin/users', {
        token: adminToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(3);
      expect(response.body.data[0]).toHaveProperty('email');
      expect(response.body.data[0]).toHaveProperty('role');
      expect(response.body.data[0]).not.toHaveProperty('password');
    });

    it('should deny access to operators', async () => {
      const response = await testRequest(app, 'GET', '/api/admin/users', {
        token: operatorToken,
      });

      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Insufficient permissions');
    });

    it('should deny access to customers', async () => {
      const response = await testRequest(app, 'GET', '/api/admin/users', {
        token: customerToken,
      });

      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Insufficient permissions');
    });

    it('should deny access without token', async () => {
      const response = await testRequest(app, 'GET', '/api/admin/users');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });
  });

  describe('PATCH /api/admin/users/:id/role', () => {
    it('should allow admin to change user role', async () => {
      const response = await testRequest(app, 'PATCH', `/api/admin/users/${customerUser._id}/role`, {
        token: adminToken,
        body: { role: 'OPERATOR' },
      });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.user.role).toBe('OPERATOR');
      expect(response.body.message).toContain('CUSTOMER to OPERATOR');

      // Verify user was updated in database
      const updatedUser = await User.findById(customerUser._id);
      expect(updatedUser?.role).toBe('OPERATOR');

      // Verify audit log was created
      const auditLog = await AuditLog.findOne({ action: 'ROLE_CHANGE' });
      expect(auditLog).toBeTruthy();
      expect((auditLog?.userId as any).toString()).toBe((customerUser._id as any).toString());
      expect((auditLog?.changedBy as any).toString()).toBe((adminUser._id as any).toString());
    });

    it('should prevent admin from changing their own role to non-admin', async () => {
      const response = await testRequest(app, 'PATCH', `/api/admin/users/${adminUser._id}/role`, {
        token: adminToken,
        body: { role: 'CUSTOMER' },
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Cannot change your own admin role');
    });

    it('should allow admin to keep their admin role', async () => {
      const response = await testRequest(app, 'PATCH', `/api/admin/users/${adminUser._id}/role`, {
        token: adminToken,
        body: { role: 'ADMIN' },
      });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
    });

    it('should return 404 for non-existent user', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await testRequest(app, 'PATCH', `/api/admin/users/${fakeId}/role`, {
        token: adminToken,
        body: { role: 'OPERATOR' },
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('User not found');
    });

    it('should validate role values', async () => {
      const response = await testRequest(app, 'PATCH', `/api/admin/users/${customerUser._id}/role`, {
        token: adminToken,
        body: { role: 'INVALID_ROLE' },
      });

      expect(response.status).toBe(400);
    });

    it('should deny access to non-admin users', async () => {
      const response = await testRequest(app, 'PATCH', `/api/admin/users/${customerUser._id}/role`, {
        token: operatorToken,
        body: { role: 'ADMIN' },
      });

      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Insufficient permissions');
    });
  });

  describe('PATCH /api/admin/users/bulk/role', () => {
    it('should allow admin to bulk change user roles', async () => {
      const response = await testRequest(app, 'PATCH', '/api/admin/users/bulk/role', {
        token: adminToken,
        body: {
          userIds: [operatorUser._id.toString(), customerUser._id.toString()],
          role: 'CUSTOMER',
        },
      });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.updatedCount).toBe(2);

      // Verify users were updated
      const updatedOperator = await User.findById(operatorUser._id);
      const updatedCustomer = await User.findById(customerUser._id);
      expect(updatedOperator?.role).toBe('CUSTOMER');
      expect(updatedCustomer?.role).toBe('CUSTOMER');
    });

    it('should prevent admin from changing their own role in bulk operation', async () => {
      const response = await testRequest(app, 'PATCH', '/api/admin/users/bulk/role', {
        token: adminToken,
        body: {
          userIds: [adminUser._id.toString(), customerUser._id.toString()],
          role: 'CUSTOMER',
        },
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Cannot change your own admin role in bulk operation');
    });

    it('should handle empty user IDs array', async () => {
      const response = await testRequest(app, 'PATCH', '/api/admin/users/bulk/role', {
        token: adminToken,
        body: {
          userIds: [],
          role: 'CUSTOMER',
        },
      });

      expect(response.status).toBe(400);
    });

    it('should deny access to non-admin users', async () => {
      const response = await testRequest(app, 'PATCH', '/api/admin/users/bulk/role', {
        token: operatorToken,
        body: {
          userIds: [customerUser._id.toString()],
          role: 'ADMIN',
        },
      });

      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Insufficient permissions');
    });
  });

  describe('GET /api/admin/stats', () => {
    it('should return admin statistics', async () => {
      const response = await testRequest(app, 'GET', '/api/admin/stats', {
        token: adminToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('totalUsers', 3);
      expect(response.body.data).toHaveProperty('roleDistribution');
      expect(response.body.data.roleDistribution).toEqual({
        admin: 1,
        operator: 1,
        customer: 1,
      });
      expect(response.body.data).toHaveProperty('recentUsers');
    });

    it('should deny access to non-admin users', async () => {
      const response = await testRequest(app, 'GET', '/api/admin/stats', {
        token: operatorToken,
      });

      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Insufficient permissions');
    });
  });

  describe('GET /api/admin/audit/role-changes', () => {
    beforeEach(async () => {
      // Create some audit log entries
      await AuditLog.create({
        userId: customerUser._id,
        changedBy: adminUser._id,
        action: 'ROLE_CHANGE',
        resourceType: 'USER',
        resourceId: customerUser._id.toString(),
        oldValues: { role: 'CUSTOMER' },
        newValues: { role: 'OPERATOR' },
      });
    });

    it('should return role change audit logs', async () => {
      const response = await testRequest(app, 'GET', '/api/admin/audit/role-changes', {
        token: adminToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveLength(1);
      expect(response.body.data[0]).toHaveProperty('action', 'ROLE_CHANGE');
      expect(response.body.pagination).toHaveProperty('total', 1);
    });

    it('should support pagination', async () => {
      const response = await testRequest(app, 'GET', '/api/admin/audit/role-changes?page=1&limit=10', {
        token: adminToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.pagination).toHaveProperty('page', 1);
      expect(response.body.pagination).toHaveProperty('limit', 10);
    });

    it('should deny access to non-admin users', async () => {
      const response = await testRequest(app, 'GET', '/api/admin/audit/role-changes', {
        token: customerToken,
      });

      expect(response.status).toBe(403);
      expect(response.body.error).toBe('Insufficient permissions');
    });
  });
});

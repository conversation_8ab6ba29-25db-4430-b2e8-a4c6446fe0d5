import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import User from '../../models/User';
import { createTestApp, testRequest } from '../helpers/testApp';

describe('Auth Routes', () => {
  const app = createTestApp();
  let testUser: any;
  let authToken: string;

  beforeEach(async () => {
    // Create a test user
    const hashedPassword = await bcrypt.hash('password123', 10);
    testUser = new User({
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'Test User',
    });
    await testUser.save();

    // Generate auth token
    authToken = jwt.sign({ userId: testUser._id }, process.env.JWT_SECRET || 'fallback-secret', { expiresIn: '7d' });
  });

  afterEach(async () => {
    await User.deleteMany({});
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'New User',
      };

      const response = await testRequest(app, 'POST', '/api/auth/register', {
        body: userData,
      });

      expect(response.status).toBe(201);
      expect(response.body.token).toBeDefined();
      expect(response.body.user.email).toBe('<EMAIL>');
      expect(response.body.user.fullName).toBe('New User');
      expect(response.body.user.id).toBeDefined();

      // Verify user was created in database
      const createdUser = await User.findOne({ email: '<EMAIL>' });
      expect(createdUser).toBeTruthy();
      expect(createdUser?.fullName).toBe('New User');
    });

    it('should not register user with existing email', async () => {
      const userData = {
        email: '<EMAIL>', // Already exists
        password: 'password123',
        fullName: 'Duplicate User',
      };

      const response = await testRequest(app, 'POST', '/api/auth/register', {
        body: userData,
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('User already exists');
    });

    it('should validate required fields', async () => {
      const userData = {
        email: '<EMAIL>',
        // Missing password and fullName
      };

      const response = await testRequest(app, 'POST', '/api/auth/register', {
        body: userData,
      });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.name).toBe('ZodError');
    });

    it('should validate email format', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'password123',
        fullName: 'Test User',
      };

      const response = await testRequest(app, 'POST', '/api/auth/register', {
        body: userData,
      });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.name).toBe('ZodError');
    });

    it('should validate password length', async () => {
      const userData = {
        email: '<EMAIL>',
        password: '123', // Too short
        fullName: 'Test User',
      };

      const response = await testRequest(app, 'POST', '/api/auth/register', {
        body: userData,
      });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.name).toBe('ZodError');
    });

    it('should hash the password', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'Hash Test User',
      };

      await testRequest(app, 'POST', '/api/auth/register', {
        body: userData,
      });

      const user = await User.findOne({ email: '<EMAIL>' });
      expect(user?.password).not.toBe('password123');
      expect(user?.password.length).toBeGreaterThan(20); // Hashed password is longer
    });
  });

  describe('POST /api/auth/login', () => {
    it('should login with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const response = await testRequest(app, 'POST', '/api/auth/login', {
        body: loginData,
      });

      expect(response.status).toBe(200);
      expect(response.body.token).toBeDefined();
      expect(response.body.user.email).toBe('<EMAIL>');
      expect(response.body.user.fullName).toBe('Test User');
      expect(response.body.user.id).toBeDefined();
    });

    it('should not login with invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const response = await testRequest(app, 'POST', '/api/auth/login', {
        body: loginData,
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid credentials');
    });

    it('should not login with invalid password', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      const response = await testRequest(app, 'POST', '/api/auth/login', {
        body: loginData,
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Invalid credentials');
    });

    it('should validate required fields', async () => {
      const loginData = {
        email: '<EMAIL>',
        // Missing password
      };

      const response = await testRequest(app, 'POST', '/api/auth/login', {
        body: loginData,
      });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.name).toBe('ZodError');
    });

    it('should validate email format', async () => {
      const loginData = {
        email: 'invalid-email',
        password: 'password123',
      };

      const response = await testRequest(app, 'POST', '/api/auth/login', {
        body: loginData,
      });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.name).toBe('ZodError');
    });
  });

  describe('GET /api/auth/me', () => {
    it('should return current user with valid token', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/me', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.email).toBe('<EMAIL>');
      expect(response.body.fullName).toBe('Test User');
      expect(response.body.id).toBeDefined();
    });

    it('should return 401 without token', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/me');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it('should return 401 with invalid token', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/me', {
        token: 'invalid-token',
      });

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Invalid token.');
    });

    it('should return 401 with malformed authorization header', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/me', {
        headers: { Authorization: 'InvalidFormat' },
      });

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Invalid token.');
    });
  });

  describe('GET /api/auth/search', () => {
    beforeEach(async () => {
      // Create additional test users
      const users = [
        {
          email: '<EMAIL>',
          password: await bcrypt.hash('password123', 10),
          fullName: 'John Doe',
        },
        {
          email: '<EMAIL>',
          password: await bcrypt.hash('password123', 10),
          fullName: 'Jane Smith',
        },
        {
          email: '<EMAIL>',
          password: await bcrypt.hash('password123', 10),
          fullName: 'Admin User',
        },
      ];

      await User.insertMany(users);
    });

    it('should search users by name', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/search?q=John', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(1);
      expect(response.body[0].fullName).toBe('John Doe');
      expect(response.body[0].email).toBe('<EMAIL>');
    });

    it('should search users by email', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/search?q=admin', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(1);
      expect(response.body[0].email).toBe('<EMAIL>');
    });

    it('should search users case-insensitively', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/search?q=JANE', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(1);
      expect(response.body[0].fullName).toBe('Jane Smith');
    });

    it('should return empty array for short query', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/search?q=a', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(0);
    });

    it('should return empty array for no query', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/search', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(0);
    });

    it('should limit results to 10', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/search?q=example', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.length).toBeLessThanOrEqual(10);
    });

    it('should require authentication', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/search?q=John');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it('should only return email and fullName fields', async () => {
      const response = await testRequest(app, 'GET', '/api/auth/search?q=John', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body[0]).toHaveProperty('email');
      expect(response.body[0]).toHaveProperty('fullName');
      expect(response.body[0]).toHaveProperty('_id');
      expect(response.body[0]).not.toHaveProperty('password');
    });
  });
});

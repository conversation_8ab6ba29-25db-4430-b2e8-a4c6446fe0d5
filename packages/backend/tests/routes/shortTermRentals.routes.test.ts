import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { testApp } from '../helpers/testApp';
import User from '../../models/User';
import Property from '../../models/Property';
import ShortTermRental from '../../models/ShortTermRental';
import Guest from '../../models/Guest';
import Commission from '../../models/Commission';
import PropertyService from '../../models/PropertyService';
import Currency from '../../models/Currency';
import jwt from 'jsonwebtoken';

// Helper function to create future dates
const getFutureDate = (daysFromNow: number) => {
  const date = new Date();
  date.setDate(date.getDate() + daysFromNow);
  return date;
};

const getFutureDateString = (daysFromNow: number) => {
  return getFutureDate(daysFromNow).toISOString().split('T')[0];
};

describe('Short Term Rentals Routes', () => {
  let user: any;
  let property: any;
  let currency: any;
  let token: string;
  let testGuest: any;

  beforeEach(async () => {
    // Create test user
    user = new User({
      fullName: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    });
    await user.save();

    // Create test currency
    currency = new Currency({
      name: 'US Dollar',
      code: 'USD',
      symbol: '$',
      createdBy: user._id,
    });
    await currency.save();

    // Create test property
    property = new Property({
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'residential',
      currencyId: currency._id,
      owners: [
        {
          ownerType: 'User',
          owner: user._id,
          ownershipPercentage: 100,
        },
      ],
      createdBy: user._id,
    });
    await property.save();

    // Create test guest
    testGuest = new Guest({
      firstName: 'Test',
      lastName: 'Guest',
      email: '<EMAIL>',
      dateOfBirth: new Date('1990-01-01'),
      documentType: 'National ID',
      documentNumber: 'TEST123456',
      phoneNumber: '+*********0',
      isForeigner: false,
      createdBy: user._id,
    });
    await testGuest.save();

    // Generate JWT token
    token = jwt.sign({ userId: user._id }, process.env.JWT_SECRET || 'fallback-secret');
  });

  afterEach(async () => {
    await User.deleteMany({});
    await Property.deleteMany({});
    await ShortTermRental.deleteMany({});
    await Guest.deleteMany({});
    await Commission.deleteMany({});
    await PropertyService.deleteMany({});
    await Currency.deleteMany({});
  });

  describe('GET /', () => {
    it('should get all rentals for authenticated user', async () => {
      // Create test rental with future dates
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);

      const rental = new ShortTermRental({
        property: property._id,
        propertyId: property._id,
        startDate: tomorrow,
        endDate: nextWeek,
        rentalAmount: 1000,
        costAmount: 200,
        status: 'active',
        isPaid: false,
        guests: [
          {
            guest: testGuest._id,
            isPrincipalContact: true,
          },
        ],
        commissions: [],
        createdBy: user._id,
      });
      await rental.save();

      const response = await testApp.request('/api/short-term-rentals', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.rentals).toHaveLength(1);
      expect(data.pagination.total).toBe(1);
    });

    it('should filter rentals by status', async () => {
      // Create rentals with different statuses
      await new ShortTermRental({
        property: property._id,
        propertyId: property._id,
        startDate: getFutureDate(1),
        endDate: getFutureDate(7),
        rentalAmount: 1000,
        costAmount: 200,
        status: 'active',
        isPaid: false,
        guests: [
          {
            guest: testGuest._id,
            isPrincipalContact: true,
          },
        ],
        commissions: [],
        createdBy: user._id,
      }).save();

      await new ShortTermRental({
        property: property._id,
        propertyId: property._id,
        startDate: getFutureDate(10),
        endDate: getFutureDate(17),
        rentalAmount: 1000,
        costAmount: 200,
        status: 'ended',
        isPaid: true,
        guests: [
          {
            guest: testGuest._id,
            isPrincipalContact: true,
          },
        ],
        commissions: [],
        createdBy: user._id,
      }).save();

      const response = await testApp.request('/api/short-term-rentals?status=active', {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.rentals).toHaveLength(1);
      expect(data.rentals[0].status).toBe('active');
    });

    it('should require authentication', async () => {
      const response = await testApp.request('/api/short-term-rentals');
      expect(response.status).toBe(401);
    });
  });

  describe('GET /:id', () => {
    it('should get specific rental by id', async () => {
      const rental = new ShortTermRental({
        property: property._id,
        propertyId: property._id,
        startDate: getFutureDate(1),
        endDate: getFutureDate(7),
        rentalAmount: 1000,
        costAmount: 200,
        status: 'active',
        isPaid: false,
        guests: [
          {
            guest: testGuest._id,
            isPrincipalContact: true,
          },
        ],
        commissions: [],
        createdBy: user._id,
      });
      await rental.save();

      const response = await testApp.request(`/api/short-term-rentals/${rental._id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data._id).toBe((rental._id as any).toString());
    });

    it('should return 404 for non-existent rental', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await testApp.request(`/api/short-term-rentals/${fakeId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      expect(response.status).toBe(404);
    });
  });

  describe('GET /calculate-cost/:propertyId', () => {
    it('should calculate cost from property services', async () => {
      // Create property services
      await new PropertyService({
        propertyId: property._id,
        name: 'Cleaning',
        cost: 100,
        frequency: 'monthly',
        mandatory: true,
        status: 'active',
        createdBy: user._id,
      }).save();

      await new PropertyService({
        propertyId: property._id,
        name: 'Maintenance',
        cost: 50,
        frequency: 'monthly',
        mandatory: false,
        status: 'active',
        createdBy: user._id,
      }).save();

      const response = await testApp.request(`/api/short-term-rentals/calculate-cost/${property._id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.costAmount).toBe(150);
      expect(data.services).toHaveLength(2);
    });

    it('should return 404 for non-existent property', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await testApp.request(`/api/short-term-rentals/calculate-cost/${fakeId}`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      expect(response.status).toBe(404);
    });
  });

  describe('POST /', () => {
    it('should create a new rental', async () => {
      const guest = new Guest({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        dateOfBirth: new Date('1990-01-01'),
        documentType: 'Passport',
        documentNumber: '*********',
        phoneNumber: '+*********0',
        isForeigner: false,
        createdBy: user._id,
      });
      await guest.save();

      const rentalData = {
        property: property._id,
        startDate: getFutureDateString(1),
        endDate: getFutureDateString(7),
        rentalAmount: 1000,
        costAmount: 200,
        status: 'active',
        isPaid: false,
        guests: [
          {
            guestId: guest._id,
            isPrincipalContact: true,
          },
        ],
      };

      const response = await testApp.request('/api/short-term-rentals', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(rentalData),
      });

      expect(response.status).toBe(201);
      const data = await response.json();
      expect(data.rentalAmount).toBe(1000);
      expect(data.guests).toHaveLength(1);
    });

    it('should prevent overlapping rentals', async () => {
      // Create existing rental
      await new ShortTermRental({
        property: property._id,
        startDate: getFutureDate(1),
        endDate: getFutureDate(7),
        rentalAmount: 1000,
        costAmount: 200,
        status: 'active',
        isPaid: false,
        guests: [
          {
            guest: testGuest._id,
            isPrincipalContact: true,
          },
        ],
        commissions: [],
        createdBy: user._id,
      }).save();

      const rentalData = {
        property: property._id,
        startDate: getFutureDateString(3),
        endDate: getFutureDateString(10),
        rentalAmount: 1000,
        costAmount: 200,
        status: 'active',
        isPaid: false,
        guests: [
          {
            guestId: testGuest._id,
            isPrincipalContact: true,
          },
        ],
      };

      const response = await testApp.request('/api/short-term-rentals', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(rentalData),
      });

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toContain('overlaps');
    });
  });

  describe('PUT /:id', () => {
    it('should update a rental', async () => {
      const rental = new ShortTermRental({
        property: property._id,
        propertyId: property._id,
        startDate: getFutureDate(1),
        endDate: getFutureDate(7),
        rentalAmount: 1000,
        costAmount: 200,
        status: 'active',
        isPaid: false,
        guests: [
          {
            guest: testGuest._id,
            isPrincipalContact: true,
          },
        ],
        commissions: [],
        createdBy: user._id,
      });
      await rental.save();

      const updateData = {
        rentalAmount: 1200,
        isPaid: true,
      };

      const response = await testApp.request(`/api/short-term-rentals/${rental._id}`, {
        method: 'PUT',
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.rentalAmount).toBe(1200);
      expect(data.isPaid).toBe(true);
    });
  });

  describe('DELETE /:id', () => {
    it('should delete a rental', async () => {
      const rental = new ShortTermRental({
        property: property._id,
        propertyId: property._id,
        startDate: getFutureDate(1),
        endDate: getFutureDate(7),
        rentalAmount: 1000,
        costAmount: 200,
        status: 'active',
        isPaid: false,
        guests: [
          {
            guest: testGuest._id,
            isPrincipalContact: true,
          },
        ],
        commissions: [],
        createdBy: user._id,
      });
      await rental.save();

      const response = await testApp.request(`/api/short-term-rentals/${rental._id}`, {
        method: 'DELETE',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.message).toContain('deleted successfully');

      // Verify rental is deleted
      const deletedRental = await ShortTermRental.findById(rental._id);
      expect(deletedRental).toBeNull();
    });
  });
});

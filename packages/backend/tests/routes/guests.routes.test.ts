import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import User from '../../models/User';
import Guest from '../../models/Guest';
import { createTestApp, testRequest } from '../helpers/testApp';

describe('Guest Routes', () => {
  const app = createTestApp();
  let testUser: any;
  let authToken: string;

  beforeEach(async () => {
    // Create a test user
    const hashedPassword = await bcrypt.hash('password123', 10);
    testUser = new User({
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'Test User',
    });
    await testUser.save();

    // Generate auth token
    authToken = jwt.sign({ userId: testUser._id }, process.env.JWT_SECRET || 'fallback-secret', { expiresIn: '7d' });
  });

  afterEach(async () => {
    await Guest.deleteMany({});
    await User.deleteMany({});
  });

  describe('POST /api/guests', () => {
    it('should create a local guest successfully', async () => {
      const guestData = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        email: '<EMAIL>',
        documentType: 'National ID',
        documentNumber: '12345678',
        phoneNumber: '+1234567890',
        relationshipWithOwner: 'Friend',
        isForeigner: false,
        cityOfResidency: 'New York',
        cityOfBirth: 'Boston',
        tripMotivation: 'Business trip',
      };

      const response = await testRequest(app, 'POST', '/api/guests', {
        body: guestData,
        token: authToken,
      });

      expect(response.status).toBe(201);
      expect(response.body.firstName).toBe('John');
      expect(response.body.lastName).toBe('Doe');
      expect(response.body.email).toBe('<EMAIL>');
      expect(response.body.isForeigner).toBe(false);
      expect(response.body.cityOfResidency).toBe('New York');
    });

    it('should create a foreign guest successfully', async () => {
      const guestData = {
        firstName: 'Maria',
        lastName: 'Garcia',
        dateOfBirth: '1985-05-15',
        email: '<EMAIL>',
        documentType: 'Passport',
        documentNumber: 'P123456789',
        isForeigner: true,
        nationality: 'Spanish',
        countryOfOrigin: 'Spain',
        nextDestination: 'France',
      };

      const response = await testRequest(app, 'POST', '/api/guests', {
        body: guestData,
        token: authToken,
      });

      expect(response.status).toBe(201);
      expect(response.body.firstName).toBe('Maria');
      expect(response.body.isForeigner).toBe(true);
      expect(response.body.nationality).toBe('Spanish');
      expect(response.body.countryOfOrigin).toBe('Spain');
    });

    it('should require authentication', async () => {
      const guestData = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        email: '<EMAIL>',
        documentType: 'National ID',
        documentNumber: '12345678',
        isForeigner: false,
      };

      const response = await testRequest(app, 'POST', '/api/guests', {
        body: guestData,
      });

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it('should validate required fields', async () => {
      const guestData = {
        firstName: 'John',
        // Missing required fields
      };

      const response = await testRequest(app, 'POST', '/api/guests', {
        body: guestData,
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.name).toBe('ZodError');
    });

    it('should validate age requirement (18+)', async () => {
      const guestData = {
        firstName: 'Young',
        lastName: 'Person',
        dateOfBirth: '2010-01-01', // Too young
        email: '<EMAIL>',
        documentType: 'National ID',
        documentNumber: '12345678',
        isForeigner: false,
      };

      const response = await testRequest(app, 'POST', '/api/guests', {
        body: guestData,
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.name).toBe('ZodError');
    });

    it('should validate foreign guest nationality requirement', async () => {
      const guestData = {
        firstName: 'Foreign',
        lastName: 'Guest',
        dateOfBirth: '1990-01-01',
        email: '<EMAIL>',
        documentType: 'Passport',
        documentNumber: 'P123456789',
        isForeigner: true,
        // Missing nationality and countryOfOrigin
      };

      const response = await testRequest(app, 'POST', '/api/guests', {
        body: guestData,
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.name).toBe('ZodError');
    });

    it('should prevent duplicate email', async () => {
      const guestData = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        email: '<EMAIL>',
        documentType: 'National ID',
        documentNumber: '12345678',
        isForeigner: false,
      };

      // Create first guest
      await testRequest(app, 'POST', '/api/guests', {
        body: guestData,
        token: authToken,
      });

      // Try to create second guest with same email
      const duplicateData = {
        ...guestData,
        documentNumber: '87654321', // Different document
      };

      const response = await testRequest(app, 'POST', '/api/guests', {
        body: duplicateData,
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('email already exists');
    });

    it('should prevent duplicate document number', async () => {
      const guestData = {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: '1990-01-01',
        email: '<EMAIL>',
        documentType: 'National ID',
        documentNumber: '12345678',
        isForeigner: false,
      };

      // Create first guest
      await testRequest(app, 'POST', '/api/guests', {
        body: guestData,
        token: authToken,
      });

      // Try to create second guest with same document number
      const duplicateData = {
        ...guestData,
        email: '<EMAIL>', // Different email
      };

      const response = await testRequest(app, 'POST', '/api/guests', {
        body: duplicateData,
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('document number already exists');
    });
  });

  describe('GET /api/guests', () => {
    beforeEach(async () => {
      // Create test guests
      const guests = [
        {
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: new Date('1990-01-01'),
          email: '<EMAIL>',
          documentType: 'National ID',
          documentNumber: '12345678',
          isForeigner: false,
          createdBy: testUser._id,
        },
        {
          firstName: 'Jane',
          lastName: 'Smith',
          dateOfBirth: new Date('1985-05-15'),
          email: '<EMAIL>',
          documentType: 'Passport',
          documentNumber: 'P987654321',
          isForeigner: true,
          nationality: 'Canadian',
          countryOfOrigin: 'Canada',
          createdBy: testUser._id,
        },
      ];

      await Guest.insertMany(guests);
    });

    it('should get all guests for authenticated user', async () => {
      const response = await testRequest(app, 'GET', '/api/guests', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
      expect(response.body[0].firstName).toBeDefined();
      expect(response.body[1].firstName).toBeDefined();
    });

    it('should require authentication', async () => {
      const response = await testRequest(app, 'GET', '/api/guests');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it('should only return guests for the authenticated user', async () => {
      // Create another user and guest
      const otherUser = new User({
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        fullName: 'Other User',
      });
      await otherUser.save();

      const otherGuest = new Guest({
        firstName: 'Other',
        lastName: 'Guest',
        dateOfBirth: new Date('1990-01-01'),
        email: '<EMAIL>',
        documentType: 'National ID',
        documentNumber: '99999999',
        isForeigner: false,
        createdBy: otherUser._id,
      });
      await otherGuest.save();

      const response = await testRequest(app, 'GET', '/api/guests', {
        token: authToken,
      });

      // Should only return 2 guests (not the other user's guest)
      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
    });
  });

  describe('GET /api/guests/:id', () => {
    let testGuest: any;

    beforeEach(async () => {
      testGuest = new Guest({
        firstName: 'Test',
        lastName: 'Guest',
        dateOfBirth: new Date('1990-01-01'),
        email: '<EMAIL>',
        documentType: 'National ID',
        documentNumber: '12345678',
        isForeigner: false,
        createdBy: testUser._id,
      });
      await testGuest.save();
    });

    it('should get guest by id', async () => {
      const response = await testRequest(app, 'GET', `/api/guests/${testGuest._id}`, {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.firstName).toBe('Test');
      expect(response.body.lastName).toBe('Guest');
      expect(response.body.email).toBe('<EMAIL>');
    });

    it('should return 404 for non-existent guest', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await testRequest(app, 'GET', `/api/guests/${fakeId}`, {
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Guest not found');
    });

    it('should require authentication', async () => {
      const response = await testRequest(app, 'GET', `/api/guests/${testGuest._id}`);

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });
  });

  describe('PUT /api/guests/:id', () => {
    let testGuest: any;

    beforeEach(async () => {
      testGuest = new Guest({
        firstName: 'Test',
        lastName: 'Guest',
        dateOfBirth: new Date('1990-01-01'),
        email: '<EMAIL>',
        documentType: 'National ID',
        documentNumber: '12345678',
        isForeigner: false,
        createdBy: testUser._id,
      });
      await testGuest.save();
    });

    it('should update guest successfully', async () => {
      const updateData = {
        firstName: 'Updated',
        lastName: 'Name',
        phoneNumber: '+9876543210',
      };

      const response = await testRequest(app, 'PUT', `/api/guests/${testGuest._id}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.firstName).toBe('Updated');
      expect(response.body.lastName).toBe('Name');
      expect(response.body.phoneNumber).toBe('+9876543210');
    });

    it('should return 404 for non-existent guest', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const updateData = { firstName: 'Updated' };

      const response = await testRequest(app, 'PUT', `/api/guests/${fakeId}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Guest not found');
    });

    it('should require authentication', async () => {
      const updateData = { firstName: 'Updated' };

      const response = await testRequest(app, 'PUT', `/api/guests/${testGuest._id}`, {
        body: updateData,
      });

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });
  });

  describe('DELETE /api/guests/:id', () => {
    let testGuest: any;

    beforeEach(async () => {
      testGuest = new Guest({
        firstName: 'Test',
        lastName: 'Guest',
        dateOfBirth: new Date('1990-01-01'),
        email: '<EMAIL>',
        documentType: 'National ID',
        documentNumber: '12345678',
        isForeigner: false,
        createdBy: testUser._id,
      });
      await testGuest.save();
    });

    it('should delete guest successfully', async () => {
      const response = await testRequest(app, 'DELETE', `/api/guests/${testGuest._id}`, {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Guest deleted successfully');

      // Verify guest was deleted
      const deletedGuest = await Guest.findById(testGuest._id);
      expect(deletedGuest).toBeNull();
    });

    it('should return 404 for non-existent guest', async () => {
      const fakeId = '507f1f77bcf86cd799439011';

      const response = await testRequest(app, 'DELETE', `/api/guests/${fakeId}`, {
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Guest not found');
    });

    it('should require authentication', async () => {
      const response = await testRequest(app, 'DELETE', `/api/guests/${testGuest._id}`);

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });
  });

  describe('GET /api/guests/search/:query', () => {
    beforeEach(async () => {
      const guests = [
        {
          firstName: 'John',
          lastName: 'Doe',
          dateOfBirth: new Date('1990-01-01'),
          email: '<EMAIL>',
          documentType: 'National ID',
          documentNumber: '12345678',
          isForeigner: false,
          createdBy: testUser._id,
        },
        {
          firstName: 'Jane',
          lastName: 'Smith',
          dateOfBirth: new Date('1985-05-15'),
          email: '<EMAIL>',
          documentType: 'Passport',
          documentNumber: 'P987654321',
          isForeigner: true,
          nationality: 'Canadian',
          countryOfOrigin: 'Canada',
          createdBy: testUser._id,
        },
      ];

      await Guest.insertMany(guests);
    });

    it('should search guests by first name', async () => {
      const response = await testRequest(app, 'GET', '/api/guests/search/John', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(1);
      expect(response.body[0].firstName).toBe('John');
    });

    it('should search guests by email', async () => {
      const response = await testRequest(app, 'GET', '/api/guests/search/jane.smith', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(1);
      expect(response.body[0].email).toBe('<EMAIL>');
    });

    it('should search guests by document number', async () => {
      const response = await testRequest(app, 'GET', '/api/guests/search/P987', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(1);
      expect(response.body[0].documentNumber).toBe('P987654321');
    });

    it('should return empty array for short query', async () => {
      const response = await testRequest(app, 'GET', '/api/guests/search/a', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(0);
    });

    it('should limit results to 10', async () => {
      const response = await testRequest(app, 'GET', '/api/guests/search/example', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.length).toBeLessThanOrEqual(10);
    });

    it('should require authentication', async () => {
      const response = await testRequest(app, 'GET', '/api/guests/search/John');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });
  });
});

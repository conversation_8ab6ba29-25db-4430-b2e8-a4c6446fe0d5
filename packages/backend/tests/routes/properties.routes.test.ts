import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import User from '../../models/User';
import Partner from '../../models/Partner';
import Property from '../../models/Property';
import { createTestApp, testRequest } from '../helpers/testApp';

describe('Properties Routes', () => {
  const app = createTestApp();
  let testUser: any;
  let testPartner: any;
  let authToken: string;

  beforeEach(async () => {
    // Create a test user
    const hashedPassword = await bcrypt.hash('password123', 10);
    testUser = new User({
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'Test User',
    });
    await testUser.save();

    // Create a test partner
    testPartner = new Partner({
      email: '<EMAIL>',
      name: 'Test Partner',
      createdBy: testUser._id,
    });
    await testPartner.save();

    // Generate auth token
    authToken = jwt.sign({ userId: testUser._id }, process.env.JWT_SECRET || 'fallback-secret', { expiresIn: '7d' });
  });

  afterEach(async () => {
    await Property.deleteMany({});
    await Partner.deleteMany({});
    await User.deleteMany({});
  });

  describe('GET /api/properties', () => {
    beforeEach(async () => {
      // Create test properties
      const properties = [
        {
          name: 'Property One',
          address: '123 Test Street',
          propertyType: 'residential',
          createdBy: testUser._id,
          owners: [
            {
              ownerType: 'User',
              owner: testUser._id,
              ownershipPercentage: 100,
            },
          ],
        },
        {
          name: 'Property Two',
          address: '456 Test Avenue',
          propertyType: 'commercial',
          createdBy: testUser._id,
          owners: [
            {
              ownerType: 'Partner',
              owner: testPartner._id,
              ownershipPercentage: 50,
            },
            {
              ownerType: 'User',
              owner: testUser._id,
              ownershipPercentage: 50,
            },
          ],
        },
      ];

      await Property.insertMany(properties);
    });

    it('should get all properties for authenticated user', async () => {
      const response = await testRequest(app, 'GET', '/api/properties', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
      expect(response.body[0].name).toBeDefined();
      expect(response.body[0].owners).toBeDefined();
    });

    it('should populate owner and creator information', async () => {
      const response = await testRequest(app, 'GET', '/api/properties', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body[0].createdBy).toBeDefined();
      expect(response.body[0].createdBy.fullName).toBe('Test User');
    });

    it('should require authentication', async () => {
      const response = await testRequest(app, 'GET', '/api/properties');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it('should only return properties for the authenticated user', async () => {
      // Create another user and property
      const otherUser = new User({
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        fullName: 'Other User',
      });
      await otherUser.save();

      const otherProperty = new Property({
        name: 'Other Property',
        createdBy: otherUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: otherUser._id,
            ownershipPercentage: 100,
          },
        ],
      });
      await otherProperty.save();

      const response = await testRequest(app, 'GET', '/api/properties', {
        token: authToken,
      });

      // Should only return 2 properties (not the other user's property)
      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(2);
    });
  });

  describe('POST /api/properties', () => {
    it('should create a property with user owner successfully', async () => {
      const propertyData = {
        name: 'New Property',
        address: '789 New Street',
        description: 'A new test property',
        propertyType: 'residential',
        isRented: false,
        owners: [
          {
            ownerType: 'User',
            email: '<EMAIL>',
            name: 'Test User',
            ownershipPercentage: 100,
          },
        ],
      };

      const response = await testRequest(app, 'POST', '/api/properties', {
        body: propertyData,
        token: authToken,
      });

      expect(response.status).toBe(201);
      expect(response.body.name).toBe('New Property');
      expect(response.body.address).toBe('789 New Street');
      expect(response.body.owners).toHaveLength(1);
      expect(response.body.owners[0].ownershipPercentage).toBe(100);
    });

    it('should create a property with partner owner successfully', async () => {
      const propertyData = {
        name: 'Partner Property',
        address: '456 Partner Avenue',
        propertyType: 'commercial',
        isRented: false,
        owners: [
          {
            ownerType: 'Partner',
            email: '<EMAIL>',
            name: 'Test Partner',
            ownershipPercentage: 100,
          },
        ],
      };

      const response = await testRequest(app, 'POST', '/api/properties', {
        body: propertyData,
        token: authToken,
      });

      expect(response.status).toBe(201);
      expect(response.body.name).toBe('Partner Property');
      expect(response.body.owners).toHaveLength(1);
      expect(response.body.owners[0].ownerType).toBe('Partner');
    });

    it('should create a property with mixed owners', async () => {
      const propertyData = {
        name: 'Mixed Property',
        address: '123 Mixed Street',
        propertyType: 'residential',
        isRented: false,
        owners: [
          {
            ownerType: 'User',
            email: '<EMAIL>',
            name: 'Test User',
            ownershipPercentage: 60,
          },
          {
            ownerType: 'Partner',
            email: '<EMAIL>',
            name: 'Test Partner',
            ownershipPercentage: 40,
          },
        ],
      };

      const response = await testRequest(app, 'POST', '/api/properties', {
        body: propertyData,
        token: authToken,
      });

      expect(response.status).toBe(201);
      expect(response.body.owners).toHaveLength(2);
      expect(response.body.owners[0].ownershipPercentage).toBe(60);
      expect(response.body.owners[1].ownershipPercentage).toBe(40);
    });

    it('should validate ownership percentages sum to 100', async () => {
      const propertyData = {
        name: 'Invalid Property',
        address: '123 Invalid Street',
        propertyType: 'residential',
        isRented: false,
        owners: [
          {
            ownerType: 'User',
            email: '<EMAIL>',
            name: 'Test User',
            ownershipPercentage: 60, // Only 60%, should be 100%
          },
        ],
      };

      const response = await testRequest(app, 'POST', '/api/properties', {
        body: propertyData,
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Ownership percentages must sum to 100%');
    });

    it('should create new user if not exists', async () => {
      const propertyData = {
        name: 'New User Property',
        address: '123 New User Street',
        propertyType: 'residential',
        isRented: false,
        owners: [
          {
            ownerType: 'User',
            email: '<EMAIL>',
            name: 'New User',
            ownershipPercentage: 100,
          },
        ],
      };

      const response = await testRequest(app, 'POST', '/api/properties', {
        body: propertyData,
        token: authToken,
      });

      expect(response.status).toBe(201);

      // Verify new user was created
      const newUser = await User.findOne({ email: '<EMAIL>' });
      expect(newUser).toBeTruthy();
      expect(newUser?.fullName).toBe('New User');
    });

    it('should create new partner if not exists', async () => {
      const propertyData = {
        name: 'New Partner Property',
        address: '123 New Partner Street',
        propertyType: 'residential',
        isRented: false,
        owners: [
          {
            ownerType: 'Partner',
            email: '<EMAIL>',
            name: 'New Partner',
            ownershipPercentage: 100,
          },
        ],
      };

      const response = await testRequest(app, 'POST', '/api/properties', {
        body: propertyData,
        token: authToken,
      });

      expect(response.status).toBe(201);

      // Verify new partner was created
      const newPartner = await Partner.findOne({
        email: '<EMAIL>',
      });
      expect(newPartner).toBeTruthy();
      expect(newPartner?.name).toBe('New Partner');
    });

    it('should require authentication', async () => {
      const propertyData = {
        name: 'Test Property',
        address: '123 Test Street',
        propertyType: 'residential',
        isRented: false,
        owners: [
          {
            ownerType: 'User',
            email: '<EMAIL>',
            name: 'Test User',
            ownershipPercentage: 100,
          },
        ],
      };

      const response = await testRequest(app, 'POST', '/api/properties', {
        body: propertyData,
      });

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it('should validate required fields', async () => {
      const propertyData = {
        // Missing name and owners
        address: '123 Test Street',
        propertyType: 'residential',
        isRented: false,
      };

      const response = await testRequest(app, 'POST', '/api/properties', {
        body: propertyData,
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.name).toBe('ZodError');
    });
  });

  describe('GET /api/properties/:id', () => {
    let testProperty: any;

    beforeEach(async () => {
      testProperty = new Property({
        name: 'Test Property',
        address: '123 Test Street',
        propertyType: 'residential',
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 100,
          },
        ],
      });
      await testProperty.save();
    });

    it('should get property by id', async () => {
      const response = await testRequest(app, 'GET', `/api/properties/${testProperty._id}`, {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.name).toBe('Test Property');
      expect(response.body.address).toBe('123 Test Street');
      expect(response.body.owners).toHaveLength(1);
    });

    it('should populate owner and creator information', async () => {
      const response = await testRequest(app, 'GET', `/api/properties/${testProperty._id}`, {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.createdBy).toBeDefined();
      expect(response.body.createdBy.fullName).toBe('Test User');
    });

    it('should return 404 for non-existent property', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await testRequest(app, 'GET', `/api/properties/${fakeId}`, {
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Property not found');
    });

    it('should require authentication', async () => {
      const response = await testRequest(app, 'GET', `/api/properties/${testProperty._id}`);

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it("should not allow access to other user's properties", async () => {
      // Create another user and their property
      const otherUser = new User({
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        fullName: 'Other User',
      });
      await otherUser.save();

      const otherProperty = new Property({
        name: 'Other Property',
        createdBy: otherUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: otherUser._id,
            ownershipPercentage: 100,
          },
        ],
      });
      await otherProperty.save();

      const response = await testRequest(app, 'GET', `/api/properties/${otherProperty._id}`, {
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Property not found');
    });
  });

  describe('PUT /api/properties/:id', () => {
    let testProperty: any;

    beforeEach(async () => {
      testProperty = new Property({
        name: 'Test Property',
        address: '123 Test Street',
        description: 'Original description',
        propertyType: 'residential',
        isRented: false,
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 100,
          },
        ],
      });
      await testProperty.save();
    });

    it('should update property successfully', async () => {
      const updateData = {
        name: 'Updated Property',
        address: '456 Updated Street',
        description: 'Updated description',
        propertyType: 'commercial',
        isRented: true,
      };

      const response = await testRequest(app, 'PUT', `/api/properties/${testProperty._id}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.name).toBe('Updated Property');
      expect(response.body.address).toBe('456 Updated Street');
      expect(response.body.description).toBe('Updated description');
      expect(response.body.propertyType).toBe('commercial');
      expect(response.body.isRented).toBe(true);
    });

    it('should update only provided fields', async () => {
      const updateData = {
        name: 'Partially Updated Property',
        // Other fields should remain unchanged
      };

      const response = await testRequest(app, 'PUT', `/api/properties/${testProperty._id}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.name).toBe('Partially Updated Property');
      expect(response.body.address).toBe('123 Test Street'); // Unchanged
      expect(response.body.propertyType).toBe('residential'); // Unchanged
    });

    it('should return 404 for non-existent property', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const updateData = { name: 'Updated' };

      const response = await testRequest(app, 'PUT', `/api/properties/${fakeId}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Property not found or access denied');
    });

    it('should require authentication', async () => {
      const updateData = { name: 'Updated' };

      const response = await testRequest(app, 'PUT', `/api/properties/${testProperty._id}`, {
        body: updateData,
      });

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it('should update property owners successfully', async () => {
      // Create a partner for testing
      const testPartner = new Partner({
        email: '<EMAIL>',
        name: 'Test Partner',
        createdBy: testUser._id,
      });
      await testPartner.save();

      const updateData = {
        owners: [
          {
            ownerType: 'User',
            email: testUser.email,
            name: testUser.fullName,
            ownershipPercentage: 60,
          },
          {
            ownerType: 'Partner',
            email: testPartner.email,
            name: testPartner.name,
            ownershipPercentage: 40,
          },
        ],
      };

      const response = await testRequest(app, 'PUT', `/api/properties/${testProperty._id}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.owners).toHaveLength(2);
      expect(response.body.owners[0].ownershipPercentage).toBe(60);
      expect(response.body.owners[1].ownershipPercentage).toBe(40);
      expect(response.body.owners[0].ownerType).toBe('User');
      expect(response.body.owners[1].ownerType).toBe('Partner');
    });

    it('should validate ownership percentages sum to 100 when updating owners', async () => {
      const updateData = {
        owners: [
          {
            ownerType: 'User',
            email: testUser.email,
            name: testUser.fullName,
            ownershipPercentage: 60,
          },
          {
            ownerType: 'Partner',
            email: '<EMAIL>',
            name: 'Test Partner',
            ownershipPercentage: 30, // Total = 90%, should fail
          },
        ],
      };

      const response = await testRequest(app, 'PUT', `/api/properties/${testProperty._id}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('Ownership percentages must sum to 100%');
    });

    it('should create new partners when updating owners', async () => {
      const updateData = {
        owners: [
          {
            ownerType: 'User',
            email: testUser.email,
            name: testUser.fullName,
            ownershipPercentage: 50,
          },
          {
            ownerType: 'Partner',
            email: '<EMAIL>',
            name: 'New Partner',
            ownershipPercentage: 50,
          },
        ],
      };

      const response = await testRequest(app, 'PUT', `/api/properties/${testProperty._id}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.owners).toHaveLength(2);

      // Verify new partner was created
      const newPartner = await Partner.findOne({
        email: '<EMAIL>',
      });
      expect(newPartner).toBeTruthy();
      expect(newPartner?.name).toBe('New Partner');
    });

    it("should not allow updating other user's properties", async () => {
      // Create another user and their property
      const otherUser = new User({
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        fullName: 'Other User',
      });
      await otherUser.save();

      const otherProperty = new Property({
        name: 'Other Property',
        createdBy: otherUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: otherUser._id,
            ownershipPercentage: 100,
          },
        ],
      });
      await otherProperty.save();

      const updateData = { name: 'Hacked Property' };

      const response = await testRequest(app, 'PUT', `/api/properties/${otherProperty._id}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Property not found or access denied');

      // Verify property was not updated
      const unchangedProperty = await Property.findById(otherProperty._id);
      expect(unchangedProperty?.name).toBe('Other Property');
    });
  });
});

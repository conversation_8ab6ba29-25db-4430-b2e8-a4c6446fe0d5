import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import User from '../../models/User';
import Partner from '../../models/Partner';
import Property from '../../models/Property';
import { createTestApp, testRequest } from '../helpers/testApp';

describe('Partners Routes', () => {
  const app = createTestApp();
  let testUser: any;
  let authToken: string;

  beforeEach(async () => {
    // Create a test user
    const hashedPassword = await bcrypt.hash('password123', 10);
    testUser = new User({
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: 'Test User',
    });
    await testUser.save();

    // Generate auth token
    authToken = jwt.sign({ userId: testUser._id }, process.env.JWT_SECRET || 'fallback-secret', { expiresIn: '7d' });
  });

  afterEach(async () => {
    await Property.deleteMany({});
    await Partner.deleteMany({});
    await User.deleteMany({});
  });

  describe('GET /api/partners', () => {
    beforeEach(async () => {
      // Create test partners
      const partners = [
        {
          email: '<EMAIL>',
          name: 'Partner One',
          createdBy: testUser._id,
        },
        {
          email: '<EMAIL>',
          name: 'Partner Two',
          createdBy: testUser._id,
        },
        {
          email: '<EMAIL>',
          name: 'Business Partner',
          createdBy: testUser._id,
        },
      ];

      await Partner.insertMany(partners);
    });

    it('should get all partners for authenticated user', async () => {
      const response = await testRequest(app, 'GET', '/api/partners', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(3);
      expect(response.body[0].name).toBeDefined();
      expect(response.body[0].email).toBeDefined();
    });

    it('should return partners sorted by name', async () => {
      const response = await testRequest(app, 'GET', '/api/partners', {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body[0].name).toBe('Business Partner');
      expect(response.body[1].name).toBe('Partner One');
      expect(response.body[2].name).toBe('Partner Two');
    });

    it('should require authentication', async () => {
      const response = await testRequest(app, 'GET', '/api/partners');

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it('should only return partners for the authenticated user', async () => {
      // Create another user and partner
      const otherUser = new User({
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        fullName: 'Other User',
      });
      await otherUser.save();

      const otherPartner = new Partner({
        email: '<EMAIL>',
        name: 'Other Partner',
        createdBy: otherUser._id,
      });
      await otherPartner.save();

      const response = await testRequest(app, 'GET', '/api/partners', {
        token: authToken,
      });

      // Should only return 3 partners (not the other user's partner)
      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(3);
    });
  });

  describe('POST /api/partners', () => {
    it('should create a partner successfully', async () => {
      const partnerData = {
        email: '<EMAIL>',
        name: 'New Partner',
      };

      const response = await testRequest(app, 'POST', '/api/partners', {
        body: partnerData,
        token: authToken,
      });

      expect(response.status).toBe(201);
      expect(response.body.email).toBe('<EMAIL>');
      expect(response.body.name).toBe('New Partner');
      expect(response.body.createdBy).toBe(testUser._id.toString());

      // Verify partner was created in database
      const createdPartner = await Partner.findOne({
        email: '<EMAIL>',
      });
      expect(createdPartner).toBeTruthy();
      expect(createdPartner?.name).toBe('New Partner');
    });

    it('should convert email to lowercase', async () => {
      const partnerData = {
        email: '<EMAIL>',
        name: 'Test Partner',
      };

      const response = await testRequest(app, 'POST', '/api/partners', {
        body: partnerData,
        token: authToken,
      });

      expect(response.status).toBe(201);
      expect(response.body.email).toBe('<EMAIL>');
    });

    it('should trim name', async () => {
      const partnerData = {
        email: '<EMAIL>',
        name: '  Trimmed Name  ',
      };

      const response = await testRequest(app, 'POST', '/api/partners', {
        body: partnerData,
        token: authToken,
      });

      expect(response.status).toBe(201);
      expect(response.body.name).toBe('Trimmed Name');
    });

    it('should prevent duplicate email for same user', async () => {
      const partnerData = {
        email: '<EMAIL>',
        name: 'First Partner',
      };

      // Create first partner
      await testRequest(app, 'POST', '/api/partners', {
        body: partnerData,
        token: authToken,
      });

      // Try to create second partner with same email
      const duplicateData = {
        email: '<EMAIL>',
        name: 'Second Partner',
      };

      const response = await testRequest(app, 'POST', '/api/partners', {
        body: duplicateData,
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('A partner with this email already exists');
    });

    it('should allow same email for different users', async () => {
      // Create another user
      const otherUser = new User({
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        fullName: 'Other User',
      });
      await otherUser.save();

      const otherToken = jwt.sign({ userId: otherUser._id }, process.env.JWT_SECRET || 'fallback-secret', { expiresIn: '7d' });

      const partnerData = {
        email: '<EMAIL>',
        name: 'Shared Email Partner',
      };

      // Create partner for first user
      await testRequest(app, 'POST', '/api/partners', {
        body: partnerData,
        token: authToken,
      });

      // Create partner with same email for second user (should be allowed)
      const response = await testRequest(app, 'POST', '/api/partners', {
        body: partnerData,
        token: otherToken,
      });

      expect(response.status).toBe(201);
      expect(response.body.email).toBe('<EMAIL>');
    });

    it('should require authentication', async () => {
      const partnerData = {
        email: '<EMAIL>',
        name: 'Test Partner',
      };

      const response = await testRequest(app, 'POST', '/api/partners', {
        body: partnerData,
      });

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it('should validate required fields', async () => {
      const partnerData = {
        email: '<EMAIL>',
        // Missing name
      };

      const response = await testRequest(app, 'POST', '/api/partners', {
        body: partnerData,
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.error.name).toBe('ZodError');
    });
  });

  describe('GET /api/partners/:id', () => {
    let testPartner: any;

    beforeEach(async () => {
      testPartner = new Partner({
        email: '<EMAIL>',
        name: 'Test Partner',
        createdBy: testUser._id,
      });
      await testPartner.save();
    });

    it('should get partner by id', async () => {
      const response = await testRequest(app, 'GET', `/api/partners/${testPartner._id}`, {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.email).toBe('<EMAIL>');
      expect(response.body.name).toBe('Test Partner');
    });

    it('should return 404 for non-existent partner', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await testRequest(app, 'GET', `/api/partners/${fakeId}`, {
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Partner not found');
    });

    it('should require authentication', async () => {
      const response = await testRequest(app, 'GET', `/api/partners/${testPartner._id}`);

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it("should not allow access to other user's partners", async () => {
      // Create another user and their partner
      const otherUser = new User({
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        fullName: 'Other User',
      });
      await otherUser.save();

      const otherPartner = new Partner({
        email: '<EMAIL>',
        name: 'Other Partner',
        createdBy: otherUser._id,
      });
      await otherPartner.save();

      const response = await testRequest(app, 'GET', `/api/partners/${otherPartner._id}`, {
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Partner not found');
    });
  });

  describe('PUT /api/partners/:id', () => {
    let testPartner: any;

    beforeEach(async () => {
      testPartner = new Partner({
        email: '<EMAIL>',
        name: 'Test Partner',
        createdBy: testUser._id,
      });
      await testPartner.save();
    });

    it('should update partner successfully', async () => {
      const updateData = {
        email: '<EMAIL>',
        name: 'Updated Partner',
      };

      const response = await testRequest(app, 'PUT', `/api/partners/${testPartner._id}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.email).toBe('<EMAIL>');
      expect(response.body.name).toBe('Updated Partner');
    });

    it('should prevent duplicate email when updating', async () => {
      // Create another partner
      const anotherPartner = new Partner({
        email: '<EMAIL>',
        name: 'Another Partner',
        createdBy: testUser._id,
      });
      await anotherPartner.save();

      // Try to update first partner with second partner's email
      const updateData = {
        email: '<EMAIL>',
      };

      const response = await testRequest(app, 'PUT', `/api/partners/${testPartner._id}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toBe('A partner with this email already exists');
    });

    it('should allow updating with same email', async () => {
      const updateData = {
        email: '<EMAIL>', // Same email
        name: 'Updated Name Only',
      };

      const response = await testRequest(app, 'PUT', `/api/partners/${testPartner._id}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.name).toBe('Updated Name Only');
    });

    it('should return 404 for non-existent partner', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const updateData = { name: 'Updated' };

      const response = await testRequest(app, 'PUT', `/api/partners/${fakeId}`, {
        body: updateData,
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Partner not found');
    });

    it('should require authentication', async () => {
      const updateData = { name: 'Updated' };

      const response = await testRequest(app, 'PUT', `/api/partners/${testPartner._id}`, {
        body: updateData,
      });

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });
  });

  describe('DELETE /api/partners/:id', () => {
    let testPartner: any;

    beforeEach(async () => {
      testPartner = new Partner({
        email: '<EMAIL>',
        name: 'Test Partner',
        createdBy: testUser._id,
      });
      await testPartner.save();
    });

    it('should delete partner successfully when not used in properties', async () => {
      const response = await testRequest(app, 'DELETE', `/api/partners/${testPartner._id}`, {
        token: authToken,
      });

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Partner deleted successfully');

      // Verify partner was deleted
      const deletedPartner = await Partner.findById(testPartner._id);
      expect(deletedPartner).toBeNull();
    });

    it('should prevent deletion when partner is used in properties', async () => {
      // Create a property that uses this partner
      const property = new Property({
        name: 'Test Property',
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'Partner',
            owner: testPartner._id,
            ownershipPercentage: 100,
          },
        ],
      });
      await property.save();

      const response = await testRequest(app, 'DELETE', `/api/partners/${testPartner._id}`, {
        token: authToken,
      });

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Cannot delete partner');
      expect(response.body.error).toContain('associated with existing properties');

      // Verify partner was not deleted
      const existingPartner = await Partner.findById(testPartner._id);
      expect(existingPartner).toBeTruthy();
    });

    it('should return 404 for non-existent partner', async () => {
      const fakeId = '507f1f77bcf86cd799439011';

      const response = await testRequest(app, 'DELETE', `/api/partners/${fakeId}`, {
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Partner not found');
    });

    it('should require authentication', async () => {
      const response = await testRequest(app, 'DELETE', `/api/partners/${testPartner._id}`);

      expect(response.status).toBe(401);
      expect(response.body.error).toBe('Access denied. No token provided.');
    });

    it("should not allow deletion of other user's partners", async () => {
      // Create another user and their partner
      const otherUser = new User({
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10),
        fullName: 'Other User',
      });
      await otherUser.save();

      const otherPartner = new Partner({
        email: '<EMAIL>',
        name: 'Other Partner',
        createdBy: otherUser._id,
      });
      await otherPartner.save();

      const response = await testRequest(app, 'DELETE', `/api/partners/${otherPartner._id}`, {
        token: authToken,
      });

      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Partner not found');

      // Verify other partner was not deleted
      const existingPartner = await Partner.findById(otherPartner._id);
      expect(existingPartner).toBeTruthy();
    });
  });
});

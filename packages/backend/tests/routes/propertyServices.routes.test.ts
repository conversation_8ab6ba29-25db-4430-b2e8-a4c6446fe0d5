import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import PropertyService from '../../models/PropertyService';
import Property from '../../models/Property';
import User from '../../models/User';

describe('Property Services Integration - US-PS-002: View Property Services', () => {
  let testUser: any;
  let testProperty: any;

  beforeEach(async () => {
    // Create test user
    testUser = new User({
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Test User',
    });
    await testUser.save();

    // Create test property
    testProperty = new Property({
      name: 'Test Property',
      address: '123 Test St',
      description: 'A test property',
      propertyType: 'residential',
      isRented: false,
      createdBy: testUser._id,
      owners: [
        {
          ownerType: 'User',
          owner: testUser._id,
          ownershipPercentage: 100,
        },
      ],
    });
    await testProperty.save();
  });

  afterEach(async () => {
    await PropertyService.deleteMany({});
    await Property.deleteMany({});
    await User.deleteMany({});
  });

  describe('AC1: View all services for a property', () => {
    it('should retrieve all services for a property', async () => {
      // Create test services
      const services = [
        {
          propertyId: testProperty._id,
          name: 'Internet',
          cost: 50.0,
          frequency: 'monthly',
          status: 'active',
        },
        {
          propertyId: testProperty._id,
          name: 'Insurance',
          cost: 600.0,
          frequency: 'quarterly',
          status: 'inactive',
        },
      ];
      await PropertyService.insertMany(services);

      // Retrieve services
      const retrievedServices = await PropertyService.find({
        propertyId: testProperty._id,
      });

      expect(retrievedServices).toHaveLength(2);
      expect(retrievedServices[0].name).toBe('Internet');
      expect(retrievedServices[1].name).toBe('Insurance');
    });

    it('should return services with all required fields', async () => {
      const serviceData = {
        propertyId: testProperty._id,
        name: 'Test Service',
        cost: 75.5,
        frequency: 'monthly',
        status: 'active',
        mandatory: true,
        paidByUser: true,
        paidByPartner: false,
      };

      const service = new PropertyService(serviceData);
      await service.save();

      const retrievedService = await PropertyService.findById(service._id);

      expect(retrievedService).toBeTruthy();
      expect(retrievedService!.name).toBe('Test Service');
      expect(retrievedService!.cost).toBe(75.5);
      expect(retrievedService!.frequency).toBe('monthly');
      expect(retrievedService!.status).toBe('active');
      expect(retrievedService!.mandatory).toBe(true);
      expect(retrievedService!.paidByUser).toBe(true);
      expect(retrievedService!.paidByPartner).toBe(false);
    });
  });

  describe('AC3: Services grouped by status', () => {
    beforeEach(async () => {
      const services = [
        {
          propertyId: testProperty._id,
          name: 'Active Service 1',
          cost: 50.0,
          frequency: 'monthly',
          status: 'active',
        },
        {
          propertyId: testProperty._id,
          name: 'Active Service 2',
          cost: 100.0,
          frequency: 'monthly',
          status: 'active',
        },
        {
          propertyId: testProperty._id,
          name: 'Inactive Service',
          cost: 25.0,
          frequency: 'monthly',
          status: 'inactive',
        },
      ];
      await PropertyService.insertMany(services);
    });

    it('should be able to filter services by status', async () => {
      const activeServices = await PropertyService.find({
        propertyId: testProperty._id,
        status: 'active',
      });

      const inactiveServices = await PropertyService.find({
        propertyId: testProperty._id,
        status: 'inactive',
      });

      expect(activeServices).toHaveLength(2);
      expect(inactiveServices).toHaveLength(1);
      expect(activeServices[0].status).toBe('active');
      expect(inactiveServices[0].status).toBe('inactive');
    });
  });

  describe('AC4: Total monthly cost equivalent for active services', () => {
    it('should calculate cost summary with monthly equivalents', async () => {
      // Create test services with different frequencies
      const services = [
        {
          propertyId: testProperty._id,
          name: 'Internet',
          cost: 50.0,
          frequency: 'monthly',
          status: 'active',
          mandatory: true,
          paidByUser: true,
          paidByPartner: false,
        },
        {
          propertyId: testProperty._id,
          name: 'Insurance',
          cost: 600.0,
          frequency: 'quarterly',
          status: 'active',
          mandatory: true,
          paidByUser: false,
          paidByPartner: true,
        },
        {
          propertyId: testProperty._id,
          name: 'Inactive Service',
          cost: 100.0,
          frequency: 'monthly',
          status: 'inactive',
          mandatory: false,
          paidByUser: true,
          paidByPartner: false,
        },
      ];
      await PropertyService.insertMany(services);

      const summary = await PropertyService.calculateCostSummary(testProperty._id);

      // Only active services should be included in totals
      expect(summary.totalMonthlyCost).toBe(250.0); // 50 + 200 (quarterly/3)
      expect(summary.userMonthlyCost).toBe(50.0);
      expect(summary.partnerMonthlyCost).toBe(200.0);
      expect(summary.mandatoryMonthlyCost).toBe(250.0);
      expect(summary.activeServicesCount).toBe(2);
      expect(summary.totalServicesCount).toBe(3); // Includes inactive
    });

    it('should handle yearly frequency conversion', async () => {
      const yearlyService = {
        propertyId: testProperty._id,
        name: 'Yearly Service',
        cost: 1200.0,
        frequency: 'yearly',
        status: 'active',
        mandatory: false,
        paidByUser: true,
        paidByPartner: false,
      };

      await PropertyService.create(yearlyService);
      const summary = await PropertyService.calculateCostSummary(testProperty._id);

      expect(summary.totalMonthlyCost).toBe(100.0); // 1200/12
      expect(summary.userMonthlyCost).toBe(100.0);
    });
  });

  describe('AC6: Search services by name', () => {
    beforeEach(async () => {
      const services = [
        {
          propertyId: testProperty._id,
          name: 'Internet Service',
          cost: 50.0,
          frequency: 'monthly',
          status: 'active',
        },
        {
          propertyId: testProperty._id,
          name: 'Insurance Premium',
          cost: 600.0,
          frequency: 'quarterly',
          status: 'active',
        },
        {
          propertyId: testProperty._id,
          name: 'Water Bill',
          cost: 30.0,
          frequency: 'monthly',
          status: 'active',
        },
      ];
      await PropertyService.insertMany(services);
    });

    it('should support case-insensitive name search', async () => {
      const internetServices = await PropertyService.find({
        propertyId: testProperty._id,
        name: { $regex: 'internet', $options: 'i' },
      });

      expect(internetServices).toHaveLength(1);
      expect(internetServices[0].name).toBe('Internet Service');
    });

    it('should support partial name matching', async () => {
      const servicesWithService = await PropertyService.find({
        propertyId: testProperty._id,
        name: { $regex: 'service', $options: 'i' },
      });

      expect(servicesWithService).toHaveLength(1);
      expect(servicesWithService[0].name).toBe('Internet Service');
    });
  });
});

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import mongoose from 'mongoose';
import Partner from '../models/Partner';
import User from '../models/User';

describe('Partner Model', () => {
  let testUser: any;

  beforeEach(async () => {
    testUser = new User({
      email: '<EMAIL>',
      password: 'hashedpassword',
      fullName: 'Test User',
    });
    await testUser.save();
  });

  afterEach(async () => {
    await Partner.deleteMany({});
    await User.deleteMany({});
  });

  describe('Partner Creation', () => {
    it('should create a valid partner', async () => {
      const partnerData = {
        email: '<EMAIL>',
        name: 'John Partner',
        createdBy: testUser._id,
      };

      const partner = new Partner(partnerData);
      const savedPartner = await partner.save();

      expect(savedPartner.email).toBe('<EMAIL>');
      expect(savedPartner.name).toBe('John Partner');
      expect((savedPartner.createdBy as any).toString()).toBe(testUser._id.toString());
      expect(savedPartner.createdAt).toBeDefined();
      expect(savedPartner.updatedAt).toBeDefined();
    });

    it('should enforce required fields', async () => {
      const partner = new Partner({});

      await expect(partner.save()).rejects.toThrow();
    });

    it('should require email field', async () => {
      const partnerData = {
        name: 'John Partner',
        createdBy: testUser._id,
      };

      const partner = new Partner(partnerData);

      await expect(partner.save()).rejects.toThrow(/email.*required/i);
    });

    it('should require name field', async () => {
      const partnerData = {
        email: '<EMAIL>',
        createdBy: testUser._id,
      };

      const partner = new Partner(partnerData);

      await expect(partner.save()).rejects.toThrow(/name.*required/i);
    });

    it('should require createdBy field', async () => {
      const partnerData = {
        email: '<EMAIL>',
        name: 'John Partner',
      };

      const partner = new Partner(partnerData);

      await expect(partner.save()).rejects.toThrow(/createdBy.*required/i);
    });

    it('should convert email to lowercase and trim', async () => {
      const partnerData = {
        email: '  <EMAIL>  ',
        name: '  John Partner  ',
        createdBy: testUser._id,
      };

      const partner = new Partner(partnerData);
      const savedPartner = await partner.save();

      expect(savedPartner.email).toBe('<EMAIL>');
      expect(savedPartner.name).toBe('John Partner');
    });

    it('should allow same email for different users', async () => {
      // Create another user
      const user2 = new User({
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'User Two',
      });
      await user2.save();

      const partnerData = {
        email: '<EMAIL>',
        name: 'John Partner',
      };

      // Create partner for first user
      const partner1 = new Partner({
        ...partnerData,
        createdBy: testUser._id,
      });
      await partner1.save();

      // Create partner with same email for second user (should be allowed)
      const partner2 = new Partner({
        ...partnerData,
        name: 'Jane Partner',
        createdBy: user2._id,
      });
      const savedPartner2 = await partner2.save();

      expect(savedPartner2.email).toBe('<EMAIL>');
      expect(savedPartner2.name).toBe('Jane Partner');
    });
  });

  describe('Partner Updates', () => {
    let testPartner: any;

    beforeEach(async () => {
      testPartner = new Partner({
        email: '<EMAIL>',
        name: 'John Partner',
        createdBy: testUser._id,
      });
      await testPartner.save();
    });

    it('should update partner fields', async () => {
      const originalCreatedAt = testPartner.createdAt;

      // Add a small delay to ensure updatedAt is different
      await new Promise((resolve) => setTimeout(resolve, 10));

      testPartner.name = 'Updated Partner Name';
      testPartner.email = '<EMAIL>';
      const updatedPartner = await testPartner.save();

      expect(updatedPartner.name).toBe('Updated Partner Name');
      expect(updatedPartner.email).toBe('<EMAIL>');
      expect(updatedPartner.updatedAt.getTime()).toBeGreaterThan(originalCreatedAt.getTime());
    });
  });

  describe('Partner Queries', () => {
    beforeEach(async () => {
      const partners = [
        {
          email: '<EMAIL>',
          name: 'Partner One',
          createdBy: testUser._id,
        },
        {
          email: '<EMAIL>',
          name: 'Partner Two',
          createdBy: testUser._id,
        },
        {
          email: '<EMAIL>',
          name: 'Business Partner',
          createdBy: testUser._id,
        },
      ];

      await Partner.insertMany(partners);
    });

    it('should find partners by user', async () => {
      const partners = await Partner.find({ createdBy: testUser._id });

      expect(partners).toHaveLength(3);
    });

    it('should find partner by email and user', async () => {
      const partner = await Partner.findOne({
        email: '<EMAIL>',
        createdBy: testUser._id,
      });

      expect(partner).toBeTruthy();
      expect(partner?.name).toBe('Partner One');
    });

    it('should find partners by name pattern', async () => {
      const partners = await Partner.find({
        name: { $regex: 'Partner', $options: 'i' },
        createdBy: testUser._id,
      });

      expect(partners).toHaveLength(3);
    });

    it('should not find partners from other users', async () => {
      // Create another user
      const user2 = new User({
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'User Two',
      });
      await user2.save();

      const partners = await Partner.find({ createdBy: user2._id });

      expect(partners).toHaveLength(0);
    });
  });

  describe('Partner Indexes', () => {
    it('should have proper indexes', async () => {
      const indexes = await Partner.collection.getIndexes();
      const indexNames = Object.keys(indexes);

      // Check that we have some indexes (at least the default _id index)
      expect(indexNames.length).toBeGreaterThan(0);

      // Check that _id index exists (this is always present)
      expect(indexNames).toContain('_id_');
    });
  });
});

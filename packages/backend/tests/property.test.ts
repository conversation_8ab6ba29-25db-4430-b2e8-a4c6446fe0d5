import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import mongoose from 'mongoose';
import Property from '../models/Property';
import User from '../models/User';
import Partner from '../models/Partner';

describe('Property Model', () => {
  let testUser: any;
  let testPartner: any;

  beforeEach(async () => {
    testUser = new User({
      email: '<EMAIL>',
      password: 'hashedpassword',
      fullName: 'Test User',
    });
    await testUser.save();

    testPartner = new Partner({
      email: '<EMAIL>',
      name: 'Test Partner',
      createdBy: testUser._id,
    });
    await testPartner.save();
  });

  afterEach(async () => {
    await Property.deleteMany({});
    await Partner.deleteMany({});
    await User.deleteMany({});
  });

  describe('Property Creation', () => {
    it('should create a valid property with user owner', async () => {
      const propertyData = {
        name: 'Test Property',
        address: '123 Test Street',
        description: 'A test property',
        propertyType: 'residential' as const,
        isRented: false,
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 100,
          },
        ],
      };

      const property = new Property(propertyData);
      const savedProperty = await property.save();

      expect(savedProperty.name).toBe('Test Property');
      expect(savedProperty.address).toBe('123 Test Street');
      expect(savedProperty.description).toBe('A test property');
      expect(savedProperty.propertyType).toBe('residential');
      expect(savedProperty.isRented).toBe(false);
      expect(savedProperty.owners).toHaveLength(1);
      expect(savedProperty.owners[0].ownerType).toBe('User');
      expect(savedProperty.owners[0].ownershipPercentage).toBe(100);
    });

    it('should create a valid property with partner owner', async () => {
      const propertyData = {
        name: 'Partner Property',
        address: '456 Partner Avenue',
        propertyType: 'commercial' as const,
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'Partner',
            owner: testPartner._id,
            ownershipPercentage: 50,
          },
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 50,
          },
        ],
      };

      const property = new Property(propertyData);
      const savedProperty = await property.save();

      expect(savedProperty.name).toBe('Partner Property');
      expect(savedProperty.owners).toHaveLength(2);
      expect(savedProperty.owners[0].ownerType).toBe('Partner');
      expect(savedProperty.owners[1].ownerType).toBe('User');
    });

    it('should enforce required fields', async () => {
      const property = new Property({});

      await expect(property.save()).rejects.toThrow();
    });

    it('should require name field', async () => {
      const propertyData = {
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 100,
          },
        ],
      };

      const property = new Property(propertyData);

      await expect(property.save()).rejects.toThrow(/name.*required/i);
    });

    it('should require createdBy field', async () => {
      const propertyData = {
        name: 'Test Property',
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 100,
          },
        ],
      };

      const property = new Property(propertyData);

      await expect(property.save()).rejects.toThrow(/createdBy.*required/i);
    });

    it('should default propertyType to residential', async () => {
      const propertyData = {
        name: 'Default Property',
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 100,
          },
        ],
      };

      const property = new Property(propertyData);
      const savedProperty = await property.save();

      expect(savedProperty.propertyType).toBe('residential');
    });

    it('should default isRented to false', async () => {
      const propertyData = {
        name: 'Default Property',
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 100,
          },
        ],
      };

      const property = new Property(propertyData);
      const savedProperty = await property.save();

      expect(savedProperty.isRented).toBe(false);
    });

    it('should validate propertyType enum', async () => {
      const propertyData = {
        name: 'Invalid Property',
        propertyType: 'invalid' as any,
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 100,
          },
        ],
      };

      const property = new Property(propertyData);

      await expect(property.save()).rejects.toThrow();
    });

    it('should validate ownership percentage range', async () => {
      const propertyData = {
        name: 'Invalid Ownership',
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 150, // Invalid: > 100
          },
        ],
      };

      const property = new Property(propertyData);

      await expect(property.save()).rejects.toThrow();
    });

    it('should validate negative ownership percentage', async () => {
      const propertyData = {
        name: 'Negative Ownership',
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: -10, // Invalid: < 0
          },
        ],
      };

      const property = new Property(propertyData);

      await expect(property.save()).rejects.toThrow();
    });
  });

  describe('Property Updates', () => {
    let testProperty: any;

    beforeEach(async () => {
      testProperty = new Property({
        name: 'Test Property',
        address: '123 Test Street',
        propertyType: 'residential',
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 100,
          },
        ],
      });
      await testProperty.save();
    });

    it('should update property fields', async () => {
      const originalCreatedAt = testProperty.createdAt;

      // Add a small delay to ensure updatedAt is different
      await new Promise((resolve) => setTimeout(resolve, 10));

      testProperty.name = 'Updated Property';
      testProperty.isRented = true;
      testProperty.propertyType = 'commercial';
      const updatedProperty = await testProperty.save();

      expect(updatedProperty.name).toBe('Updated Property');
      expect(updatedProperty.isRented).toBe(true);
      expect(updatedProperty.propertyType).toBe('commercial');
      expect(updatedProperty.updatedAt.getTime()).toBeGreaterThan(originalCreatedAt.getTime());
    });

    it('should update ownership structure', async () => {
      testProperty.owners = [
        {
          ownerType: 'User',
          owner: testUser._id,
          ownershipPercentage: 60,
        },
        {
          ownerType: 'Partner',
          owner: testPartner._id,
          ownershipPercentage: 40,
        },
      ];
      const updatedProperty = await testProperty.save();

      expect(updatedProperty.owners).toHaveLength(2);
      expect(updatedProperty.owners[0].ownershipPercentage).toBe(60);
      expect(updatedProperty.owners[1].ownershipPercentage).toBe(40);
    });
  });

  describe('Property Queries', () => {
    beforeEach(async () => {
      const properties = [
        {
          name: 'Residential Property',
          propertyType: 'residential',
          isRented: false,
          createdBy: testUser._id,
          owners: [
            {
              ownerType: 'User',
              owner: testUser._id,
              ownershipPercentage: 100,
            },
          ],
        },
        {
          name: 'Commercial Property',
          propertyType: 'commercial',
          isRented: true,
          createdBy: testUser._id,
          owners: [
            {
              ownerType: 'Partner',
              owner: testPartner._id,
              ownershipPercentage: 100,
            },
          ],
        },
        {
          name: 'Land Property',
          propertyType: 'land',
          isRented: false,
          createdBy: testUser._id,
          owners: [
            {
              ownerType: 'User',
              owner: testUser._id,
              ownershipPercentage: 50,
            },
            {
              ownerType: 'Partner',
              owner: testPartner._id,
              ownershipPercentage: 50,
            },
          ],
        },
      ];

      await Property.insertMany(properties);
    });

    it('should find properties by user', async () => {
      const properties = await Property.find({ createdBy: testUser._id });

      expect(properties).toHaveLength(3);
    });

    it('should find properties by type', async () => {
      const residentialProperties = await Property.find({
        propertyType: 'residential',
        createdBy: testUser._id,
      });

      expect(residentialProperties).toHaveLength(1);
      expect(residentialProperties[0].name).toBe('Residential Property');
    });

    it('should find rented properties', async () => {
      const rentedProperties = await Property.find({
        isRented: true,
        createdBy: testUser._id,
      });

      expect(rentedProperties).toHaveLength(1);
      expect(rentedProperties[0].name).toBe('Commercial Property');
    });

    it('should find properties by name pattern', async () => {
      const properties = await Property.find({
        name: { $regex: 'Property', $options: 'i' },
        createdBy: testUser._id,
      });

      expect(properties).toHaveLength(3);
    });
  });

  describe('Property Methods', () => {
    let testProperty: any;

    beforeEach(async () => {
      testProperty = new Property({
        name: 'Test Property',
        createdBy: testUser._id,
        owners: [
          {
            ownerType: 'User',
            owner: testUser._id,
            ownershipPercentage: 60,
          },
          {
            ownerType: 'Partner',
            owner: testPartner._id,
            ownershipPercentage: 40,
          },
        ],
      });
      await testProperty.save();
    });

    it('should have populateOwners method', async () => {
      expect(typeof testProperty.populateOwners).toBe('function');
    });
  });
});

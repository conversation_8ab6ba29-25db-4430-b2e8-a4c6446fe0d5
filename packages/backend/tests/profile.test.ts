import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import mongoose from 'mongoose';
import Profile from '../models/Profile';
import User from '../models/User';

describe('Profile Model', () => {
  let testUser: any;

  beforeEach(async () => {
    testUser = new User({
      email: '<EMAIL>',
      password: 'hashedpassword',
      fullName: 'Test User',
    });
    await testUser.save();
  });

  afterEach(async () => {
    await Profile.deleteMany({});
    await User.deleteMany({});
  });

  describe('Profile Creation', () => {
    it('should create a valid profile with minimal data', async () => {
      const profileData = {
        userId: testUser._id,
      };

      const profile = new Profile(profileData);
      const savedProfile = await profile.save();

      expect((savedProfile.userId as any).toString()).toBe(testUser._id.toString());
      expect(savedProfile.savingsAmount).toBe(0); // default value
      expect(savedProfile.investmentRiskLevel).toBe('medium'); // default value
      expect(savedProfile.notificationSettings.emailNotifications).toBe(true);
      expect(savedProfile.createdAt).toBeDefined();
      expect(savedProfile.updatedAt).toBeDefined();
    });

    it('should create a profile with all fields', async () => {
      const profileData = {
        userId: testUser._id,
        age: 30,
        salary: 75000,
        savingsAmount: 25000,
        investmentRiskLevel: 'high' as const,
        notificationSettings: {
          emailNotifications: false,
          pushNotifications: true,
          weeklyReports: false,
          monthlyReports: true,
          budgetAlerts: false,
          investmentUpdates: true,
        },
      };

      const profile = new Profile(profileData);
      const savedProfile = await profile.save();

      expect(savedProfile.age).toBe(30);
      expect(savedProfile.salary).toBe(75000);
      expect(savedProfile.savingsAmount).toBe(25000);
      expect(savedProfile.investmentRiskLevel).toBe('high');
      expect(savedProfile.notificationSettings.emailNotifications).toBe(false);
      expect(savedProfile.notificationSettings.investmentUpdates).toBe(true);
    });

    it('should enforce required userId field', async () => {
      const profile = new Profile({});

      await expect(profile.save()).rejects.toThrow(/userId.*required/i);
    });

    it('should enforce unique userId constraint', async () => {
      const profileData = {
        userId: testUser._id,
        age: 25,
      };

      // Create first profile
      const profile1 = new Profile(profileData);
      await profile1.save();

      // Try to create second profile with same userId
      const profile2 = new Profile(profileData);

      await expect(profile2.save()).rejects.toThrow();
    });

    it('should validate age minimum', async () => {
      const profileData = {
        userId: testUser._id,
        age: 17, // Below minimum
      };

      const profile = new Profile(profileData);

      await expect(profile.save()).rejects.toThrow();
    });

    it('should validate age maximum', async () => {
      const profileData = {
        userId: testUser._id,
        age: 121, // Above maximum
      };

      const profile = new Profile(profileData);

      await expect(profile.save()).rejects.toThrow();
    });

    it('should validate salary minimum', async () => {
      const profileData = {
        userId: testUser._id,
        salary: -1000, // Negative salary
      };

      const profile = new Profile(profileData);

      await expect(profile.save()).rejects.toThrow();
    });

    it('should validate savingsAmount minimum', async () => {
      const profileData = {
        userId: testUser._id,
        savingsAmount: -500, // Negative savings
      };

      const profile = new Profile(profileData);

      await expect(profile.save()).rejects.toThrow();
    });

    it('should validate investmentRiskLevel enum', async () => {
      const profileData = {
        userId: testUser._id,
        investmentRiskLevel: 'invalid' as any,
      };

      const profile = new Profile(profileData);

      await expect(profile.save()).rejects.toThrow();
    });

    it('should set default notification settings', async () => {
      const profileData = {
        userId: testUser._id,
      };

      const profile = new Profile(profileData);
      const savedProfile = await profile.save();

      expect(savedProfile.notificationSettings.emailNotifications).toBe(true);
      expect(savedProfile.notificationSettings.pushNotifications).toBe(true);
      expect(savedProfile.notificationSettings.weeklyReports).toBe(true);
      expect(savedProfile.notificationSettings.monthlyReports).toBe(true);
      expect(savedProfile.notificationSettings.budgetAlerts).toBe(true);
      expect(savedProfile.notificationSettings.investmentUpdates).toBe(false);
    });
  });

  describe('Profile Updates', () => {
    let testProfile: any;

    beforeEach(async () => {
      testProfile = new Profile({
        userId: testUser._id,
        age: 25,
        salary: 50000,
        savingsAmount: 10000,
        investmentRiskLevel: 'low',
      });
      await testProfile.save();
    });

    it('should update profile fields', async () => {
      const originalCreatedAt = testProfile.createdAt;

      // Add a small delay to ensure updatedAt is different
      await new Promise((resolve) => setTimeout(resolve, 10));

      testProfile.age = 26;
      testProfile.salary = 55000;
      testProfile.investmentRiskLevel = 'medium';
      const updatedProfile = await testProfile.save();

      expect(updatedProfile.age).toBe(26);
      expect(updatedProfile.salary).toBe(55000);
      expect(updatedProfile.investmentRiskLevel).toBe('medium');
      expect(updatedProfile.updatedAt.getTime()).toBeGreaterThan(originalCreatedAt.getTime());
    });

    it('should update notification settings', async () => {
      testProfile.notificationSettings.emailNotifications = false;
      testProfile.notificationSettings.investmentUpdates = true;
      const updatedProfile = await testProfile.save();

      expect(updatedProfile.notificationSettings.emailNotifications).toBe(false);
      expect(updatedProfile.notificationSettings.investmentUpdates).toBe(true);
    });

    it('should allow partial notification settings update', async () => {
      testProfile.notificationSettings.weeklyReports = false;
      const updatedProfile = await testProfile.save();

      expect(updatedProfile.notificationSettings.weeklyReports).toBe(false);
      // Other settings should remain unchanged
      expect(updatedProfile.notificationSettings.emailNotifications).toBe(true);
    });
  });

  describe('Profile Queries', () => {
    beforeEach(async () => {
      // Create additional users for testing
      const user2 = new User({
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'User Two',
      });
      await user2.save();

      const user3 = new User({
        email: '<EMAIL>',
        password: 'password123',
        fullName: 'User Three',
      });
      await user3.save();

      const profiles = [
        {
          userId: testUser._id,
          age: 25,
          salary: 50000,
          savingsAmount: 10000,
          investmentRiskLevel: 'low',
        },
        {
          userId: user2._id,
          age: 35,
          salary: 80000,
          savingsAmount: 50000,
          investmentRiskLevel: 'high',
        },
        {
          userId: user3._id,
          age: 28,
          salary: 60000,
          savingsAmount: 20000,
          investmentRiskLevel: 'medium',
        },
      ];

      await Profile.insertMany(profiles);
    });

    it('should find profile by userId', async () => {
      const profile = await Profile.findOne({ userId: testUser._id });

      expect(profile).toBeTruthy();
      expect(profile?.age).toBe(25);
      expect(profile?.investmentRiskLevel).toBe('low');
    });

    it('should find profiles by risk level', async () => {
      const highRiskProfiles = await Profile.find({
        investmentRiskLevel: 'high',
      });

      expect(highRiskProfiles).toHaveLength(1);
      expect(highRiskProfiles[0].age).toBe(35);
    });

    it('should find profiles by age range', async () => {
      const youngProfiles = await Profile.find({
        age: { $lt: 30 },
      });

      expect(youngProfiles).toHaveLength(2);
    });

    it('should find profiles by savings range', async () => {
      const highSavingsProfiles = await Profile.find({
        savingsAmount: { $gte: 20000 },
      });

      expect(highSavingsProfiles).toHaveLength(2);
    });
  });

  describe('Profile Virtual Fields', () => {
    let testProfile: any;

    beforeEach(async () => {
      testProfile = new Profile({
        userId: testUser._id,
        age: 30,
      });
      await testProfile.save();
    });

    it('should have user virtual field configured', async () => {
      // The virtual field should be configured but we can't easily test population
      // in unit tests without more complex setup
      expect(testProfile.schema.virtuals.user).toBeDefined();
    });

    it('should include virtuals in JSON serialization', async () => {
      const profileJSON = testProfile.toJSON();
      expect(profileJSON.id).toBeDefined(); // Virtual id field
    });
  });

  describe('Profile Indexes', () => {
    it('should have userId index', async () => {
      const indexes = await Profile.collection.getIndexes();
      const indexNames = Object.keys(indexes);

      // Check that we have some indexes (at least the default _id index)
      expect(indexNames.length).toBeGreaterThan(0);

      // Check that _id index exists (this is always present)
      expect(indexNames).toContain('_id_');
    });
  });
});

# Backend Testing Setup

This document describes the testing setup for the Alom Rentals backend using Vitest.

## Overview

The backend uses **Vitest** as the testing framework with **MongoDB Memory Server** for database testing. This setup provides:

- Fast, isolated tests with in-memory MongoDB
- TypeScript support out of the box
- Watch mode for development
- Coverage reporting
- Comprehensive test utilities

## Test Configuration

### Files

- `vitest.config.ts` - Vitest configuration
- `tests/setup.ts` - Global test setup (MongoDB Memory Server)
- `tests/*.test.ts` - Test files

### Key Features

- **30-second timeouts** for MongoDB setup
- **Automatic database cleanup** between tests
- **In-memory MongoDB** for fast, isolated tests
- **Coverage reporting** with v8 provider

## Running Tests

### Available Commands

```bash
# Run tests once
yarn workspace @alom-rentals/backend test --run

# Run tests in watch mode (development)
yarn workspace @alom-rentals/backend test

# Run tests with coverage
yarn workspace @alom-rentals/backend test:coverage

# From project root
yarn test:backend
```

### Test Structure

Tests are organized by feature/model:

```
tests/
├── setup.ts           # Global test setup
├── guest.test.ts       # Guest model tests
└── [feature].test.ts   # Additional feature tests
```

## Writing Tests

### Example Test Structure

```typescript
import { describe, it, expect, beforeEach, afterEach } from "vitest";
import mongoose from "mongoose";
import YourModel from "../models/YourModel";
import User from "../models/User";

describe("Your Model", () => {
  let testUser: any;

  beforeEach(async () => {
    // Create test data
    testUser = new User({
      email: "<EMAIL>",
      password: "hashedpassword",
      fullName: "Test User",
    });
    await testUser.save();
  });

  afterEach(async () => {
    // Clean up test data (optional - setup.ts handles global cleanup)
    await YourModel.deleteMany({});
    await User.deleteMany({});
  });

  it("should create a valid record", async () => {
    const data = {
      // your test data
      createdBy: testUser._id,
    };

    const record = new YourModel(data);
    const savedRecord = await record.save();

    expect(savedRecord.someField).toBe("expectedValue");
  });
});
```

### Best Practices

1. **Use descriptive test names** that explain what is being tested
2. **Group related tests** using `describe` blocks
3. **Clean up test data** in `afterEach` or rely on global cleanup
4. **Test both success and failure cases**
5. **Use meaningful assertions** with clear expected values
6. **Test validation rules** and business logic

## Current Test Coverage

### Guest Model Tests (12 tests)

- ✅ **Guest Creation** (8 tests)

  - Valid local guest creation
  - Valid foreign guest creation
  - Age validation (18+ requirement)
  - Foreign guest nationality validation
  - Unique email constraint
  - Unique document number constraint
  - Email format validation
  - Document number format validation

- ✅ **Guest Virtual Fields** (1 test)

  - Full name generation

- ✅ **Guest Search** (3 tests)
  - Search by first name
  - Search by email
  - Search by document number

## MongoDB Memory Server

The test setup uses MongoDB Memory Server which:

- **Downloads MongoDB binary** on first run (may take time)
- **Starts in-memory instance** for each test run
- **Provides isolated database** for each test suite
- **Automatically cleans up** after tests complete

### First Run

The first test run will download MongoDB (~65MB) which may take several minutes. Subsequent runs will be much faster.

## Troubleshooting

### Common Issues

1. **Timeout errors**: Increase timeout in `vitest.config.ts` if needed
2. **MongoDB download fails**: Check internet connection and try again
3. **Port conflicts**: MongoDB Memory Server handles port allocation automatically
4. **Memory issues**: Close other applications if running low on memory

### Debug Tips

- Use `console.log` in tests for debugging
- Check test output for detailed error messages
- Verify test data setup in `beforeEach` hooks
- Ensure proper async/await usage

## Future Enhancements

- Add API route testing with supertest
- Implement test factories for common data creation
- Add integration tests for complete workflows
- Set up CI/CD pipeline with automated testing

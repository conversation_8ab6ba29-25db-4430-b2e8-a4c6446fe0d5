{"name": "@alom-rentals/backend", "version": "1.0.0", "description": "Backend API for Alom Rentals app", "main": "dist/server.js", "type": "module", "scripts": {"dev": "nodemon --exec tsx ./server.ts", "build": "tsc", "start": "node dist/server.js", "clean": "rm -rf dist", "test": "vitest --run", "test:watch": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@alom-rentals/shared": "1.0.0", "@hono/node-server": "^1.16.0", "@hono/zod-validator": "^0.4.1", "bcryptjs": "^2.4.3", "dotenv": "^16.4.5", "hono": "^4.6.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.7.0", "react-hot-toast": "^2.5.2", "zod": "^3.23.8"}, "devDependencies": {"@hono/node-server": "^1.13.1", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.7.4", "@types/supertest": "^6.0.2", "@vitest/coverage-v8": "^2.1.8", "mongodb-memory-server": "^10.1.2", "nodemon": "^3.1.7", "supertest": "^7.0.0", "tsx": "^4.19.1", "typescript": "^5.6.2", "vitest": "^2.1.8"}}
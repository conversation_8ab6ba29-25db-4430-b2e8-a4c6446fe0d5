import mongoose from 'mongoose';
import User from '../models/User';
import { AuditLog } from '../models/AuditLog';

/**
 * Migration: Add role field to existing users
 *
 * This migration:
 * 1. Adds the role field to all existing users (default: CUSTOMER)
 * 2. Creates the first admin user if no admin exists
 * 3. Logs the migration in audit logs
 */

export async function up() {
  console.log('Starting migration: Add user roles...');

  try {
    // Step 1: Add role field to all existing users who don't have it
    const usersWithoutRole = await User.find({ role: { $exists: false } });
    console.log(`Found ${usersWithoutRole.length} users without role field`);

    if (usersWithoutRole.length > 0) {
      await User.updateMany({ role: { $exists: false } }, { $set: { role: 'CUSTOMER' } });
      console.log(`Updated ${usersWithoutRole.length} users with CUSTOMER role`);
    }

    // Step 2: Check if any admin users exist
    const adminCount = await User.countDocuments({ role: 'ADMIN' });
    console.log(`Found ${adminCount} admin users`);

    // Step 3: If no admin exists, promote the first user to admin
    if (adminCount === 0) {
      const firstUser = await User.findOne().sort({ createdAt: 1 });

      if (firstUser) {
        firstUser.role = 'ADMIN';
        await firstUser.save();
        console.log(`Promoted user ${firstUser.email} to ADMIN role`);

        // Log this action
        await AuditLog.create({
          userId: firstUser._id,
          changedBy: firstUser._id, // Self-promotion during migration
          action: 'ROLE_CHANGE',
          resourceType: 'USER',
          resourceId: (firstUser._id as string).toString(),
          oldValues: { role: 'CUSTOMER' },
          newValues: { role: 'ADMIN' },
          ipAddress: 'migration',
          userAgent: 'database-migration',
        });
      } else {
        console.log('No users found to promote to admin');
      }
    }

    // Step 4: Create indexes for role-based queries
    await User.collection.createIndex({ role: 1 });
    await User.collection.createIndex({ role: 1, createdAt: -1 });
    console.log('Created indexes for role-based queries');

    console.log('Migration completed successfully');
    return { success: true, message: 'User roles migration completed' };
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

export async function down() {
  console.log('Starting rollback: Remove user roles...');

  try {
    // Remove role field from all users
    await User.updateMany({}, { $unset: { role: 1 } });
    console.log('Removed role field from all users');

    // Remove role-related audit logs
    await AuditLog.deleteMany({ action: 'ROLE_CHANGE' });
    console.log('Removed role change audit logs');

    // Drop role indexes
    try {
      await User.collection.dropIndex('{ role: 1 }');
      await User.collection.dropIndex('{ role: 1, createdAt: -1 }');
      console.log('Dropped role indexes');
    } catch (error) {
      console.log('Indexes may not exist, skipping...');
    }

    console.log('Rollback completed successfully');
    return { success: true, message: 'User roles rollback completed' };
  } catch (error) {
    console.error('Rollback failed:', error);
    throw error;
  }
}

// Migration runner (can be called directly)
export async function runMigration() {
  if (!mongoose.connection.readyState) {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/property-finance');
  }

  try {
    await up();
    console.log('Migration executed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
  }
}

// Run migration if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMigration();
}

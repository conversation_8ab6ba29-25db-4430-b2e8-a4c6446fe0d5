import { <PERSON>o } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import Guest from '../models/Guest';
import { auth } from '../middleware/auth';
import { guestCreateSchema, guestUpdateSchema, guestParamsSchema } from '@alom-rentals/shared';

const guestRoutes = new Hono();

// Get all guests for the authenticated user (or all guests for admin)
guestRoutes.get('/', auth, async (c) => {
  try {
    const user = c.get('user');
    const targetUserId = c.req.query('userId'); // Admin can filter by specific user

    let query: any = {};

    if (user.role === 'ADMIN') {
      // Admin can see all guests, optionally filtered by user
      if (targetUserId) {
        query = { createdBy: targetUserId };
      }
      // If no targetUserId, admin sees all guests (no additional filtering)
    } else {
      // Non-admin users see only their own guests
      query = { createdBy: user._id };
    }

    const guests = await Guest.find(query).populate('createdBy', 'fullName email').sort({ createdAt: -1 });

    return c.json(guests);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get a specific guest by ID
guestRoutes.get('/:id', auth, zValidator('param', guestParamsSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');

    let query: any = { _id: id };

    if (user.role !== 'ADMIN') {
      // Non-admin users can only access their own guests
      query.createdBy = user._id;
    }
    // Admin can access any guest (no additional filtering)

    const guest = await Guest.findOne(query).populate('createdBy', 'fullName email');

    if (!guest) {
      return c.json({ error: 'Guest not found' }, 404);
    }

    return c.json(guest);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Create a new guest
guestRoutes.post('/', auth, zValidator('json', guestCreateSchema), async (c) => {
  try {
    const user = c.get('user');
    const guestData = c.req.valid('json');

    // Check for duplicate email
    const existingGuestByEmail = await Guest.findOne({
      email: guestData.email,
      createdBy: user._id,
    });

    if (existingGuestByEmail) {
      return c.json({ error: 'A guest with this email already exists' }, 400);
    }

    // Check for duplicate document number
    const existingGuestByDocument = await Guest.findOne({
      documentNumber: guestData.documentNumber,
      createdBy: user._id,
    });

    if (existingGuestByDocument) {
      return c.json({ error: 'A guest with this document number already exists' }, 400);
    }

    // Convert date string to Date object if needed
    if (typeof guestData.dateOfBirth === 'string') {
      guestData.dateOfBirth = new Date(guestData.dateOfBirth);
    }

    const guest = new Guest({
      ...guestData,
      createdBy: user._id,
    });

    await guest.save();

    return c.json(guest, 201);
  } catch (error: any) {
    if (error.code === 11000) {
      // Handle duplicate key error
      if (error.keyPattern?.email) {
        return c.json({ error: 'A guest with this email already exists' }, 400);
      }
      if (error.keyPattern?.documentNumber) {
        return c.json({ error: 'A guest with this document number already exists' }, 400);
      }
    }

    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return c.json({ error: validationErrors.join(', ') }, 400);
    }

    return c.json({ error: error.message }, 500);
  }
});

// Update a guest
guestRoutes.put('/:id', auth, zValidator('param', guestParamsSchema), zValidator('json', guestUpdateSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');
    const updateData = c.req.valid('json');

    // Convert date string to Date object if needed
    if (updateData.dateOfBirth && typeof updateData.dateOfBirth === 'string') {
      updateData.dateOfBirth = new Date(updateData.dateOfBirth);
    }

    const guest = await Guest.findOneAndUpdate({ _id: id, createdBy: user._id }, updateData, { new: true, runValidators: true });

    if (!guest) {
      return c.json({ error: 'Guest not found' }, 404);
    }

    return c.json(guest);
  } catch (error: any) {
    if (error.code === 11000) {
      // Handle duplicate key error
      if (error.keyPattern?.email) {
        return c.json({ error: 'A guest with this email already exists' }, 400);
      }
      if (error.keyPattern?.documentNumber) {
        return c.json({ error: 'A guest with this document number already exists' }, 400);
      }
    }

    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return c.json({ error: validationErrors.join(', ') }, 400);
    }

    return c.json({ error: error.message }, 500);
  }
});

// Delete a guest
guestRoutes.delete('/:id', auth, zValidator('param', guestParamsSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');

    const guest = await Guest.findOneAndDelete({
      _id: id,
      createdBy: user._id,
    });

    if (!guest) {
      return c.json({ error: 'Guest not found' }, 404);
    }

    return c.json({ message: 'Guest deleted successfully' });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Search guests by name, email, or document number
guestRoutes.get('/search/:query', auth, async (c) => {
  try {
    const user = c.get('user');
    const query = c.req.param('query');

    if (!query || query.length < 2) {
      return c.json([]);
    }

    const guests = await Guest.find({
      createdBy: user._id,
      $or: [
        { firstName: { $regex: query, $options: 'i' } },
        { lastName: { $regex: query, $options: 'i' } },
        { email: { $regex: query, $options: 'i' } },
        { documentNumber: { $regex: query, $options: 'i' } },
      ],
    })
      .select('firstName lastName email documentType documentNumber phoneNumber relationshipWithOwner isForeigner')
      .limit(10);

    return c.json(guests);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Bulk delete guests (must be before /:id route)
const bulkDeleteSchema = z.object({
  guestIds: z.array(z.string()).min(1, 'At least one guest ID is required'),
});

guestRoutes.delete('/bulk', auth, zValidator('json', bulkDeleteSchema), async (c) => {
  try {
    const user = c.get('user');
    const { guestIds } = c.req.valid('json');

    // Find guests where user is creator
    const guests = await Guest.find({
      _id: { $in: guestIds },
      createdBy: user._id,
    });

    if (guests.length === 0) {
      return c.json({ error: 'No guests found or access denied' }, 404);
    }

    // Delete the guests
    const guestIdsToDelete = guests.map((g) => g._id);
    await Guest.deleteMany({ _id: { $in: guestIdsToDelete } });

    return c.json({
      message: `${guests.length} guests deleted successfully`,
      deletedCount: guests.length,
      deletedIds: guestIdsToDelete,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

export default guestRoutes;

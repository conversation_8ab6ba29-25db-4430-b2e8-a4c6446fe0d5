import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import Partner from '../models/Partner';
import Property from '../models/Property';
import { auth } from '../middleware/auth';
import { partnerCreateSchema, partnerUpdateSchema, partnerParamsSchema } from '@alom-rentals/shared';

const partnersRoutes = new Hono();

// Get all partners for the authenticated user (or all partners for admin)
partnersRoutes.get('/', auth, async (c) => {
  try {
    const user = c.get('user');
    const targetUserId = c.req.query('userId'); // Admin can filter by specific user

    let query: any = {};

    if (user.role === 'ADMIN') {
      // Admin can see all partners, optionally filtered by user
      if (targetUserId) {
        query = { createdBy: targetUserId };
      }
      // If no targetUserId, admin sees all partners (no additional filtering)
    } else {
      // Non-admin users see only their own partners
      query = { createdBy: user._id };
    }

    const partners = await Partner.find(query).populate('createdBy', 'fullName email').sort({ name: 1 });

    return c.json(partners);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get a specific partner
partnersRoutes.get('/:id', auth, zValidator('param', partnerParamsSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');

    const partner = await Partner.findOne({
      _id: id,
      createdBy: user._id,
    });

    if (!partner) {
      return c.json({ error: 'Partner not found' }, 404);
    }

    return c.json(partner);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Create a new partner
partnersRoutes.post('/', auth, zValidator('json', partnerCreateSchema), async (c) => {
  try {
    const user = c.get('user');
    const { email, name } = c.req.valid('json');

    // Check if partner with this email already exists for this user
    const existingPartner = await Partner.findOne({
      email: email.toLowerCase(),
      createdBy: user._id,
    });

    if (existingPartner) {
      return c.json(
        {
          error: 'A partner with this email already exists',
        },
        400
      );
    }

    const partner = new Partner({
      email: email.toLowerCase(),
      name: name.trim(),
      createdBy: user._id,
    });

    await partner.save();
    return c.json(partner, 201);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Update a partner
partnersRoutes.put('/:id', auth, zValidator('param', partnerParamsSchema), zValidator('json', partnerUpdateSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');
    const { email, name } = c.req.valid('json');

    const partner = await Partner.findOne({
      _id: id,
      createdBy: user._id,
    });

    if (!partner) {
      return c.json({ error: 'Partner not found' }, 404);
    }

    // Check if email is being changed and if new email already exists
    if (email && email.toLowerCase() !== partner.email) {
      const existingPartner = await Partner.findOne({
        email: email.toLowerCase(),
        createdBy: user._id,
        _id: { $ne: id },
      });

      if (existingPartner) {
        return c.json(
          {
            error: 'A partner with this email already exists',
          },
          400
        );
      }
    }

    // Update fields
    if (email) partner.email = email.toLowerCase();
    if (name) partner.name = name.trim();

    await partner.save();
    return c.json(partner);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Delete a partner
partnersRoutes.delete('/:id', auth, zValidator('param', partnerParamsSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');

    const partner = await Partner.findOne({
      _id: id,
      createdBy: user._id,
    });

    if (!partner) {
      return c.json({ error: 'Partner not found' }, 404);
    }

    // Check if partner is being used in any properties
    const propertiesUsingPartner = await Property.find({
      'owners.owner': partner._id,
      'owners.ownerType': 'Partner',
    });

    if (propertiesUsingPartner.length > 0) {
      return c.json(
        {
          error: 'Cannot delete partner. Partner is associated with existing properties.',
        },
        400
      );
    }

    await Partner.findByIdAndDelete(id);
    return c.json({ message: 'Partner deleted successfully' });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Bulk delete partners (must be before /:id route)
const bulkDeleteSchema = z.object({
  partnerIds: z.array(z.string()).min(1, 'At least one partner ID is required'),
});

partnersRoutes.delete('/bulk', auth, zValidator('json', bulkDeleteSchema), async (c) => {
  try {
    const user = c.get('user');
    const { partnerIds } = c.req.valid('json');

    // Find partners where user is creator
    const partners = await Partner.find({
      _id: { $in: partnerIds },
      createdBy: user._id,
    });

    if (partners.length === 0) {
      return c.json({ error: 'No partners found or access denied' }, 404);
    }

    // Check if any partners are being used in properties
    const propertiesUsingPartners = await Property.find({
      'owners.owner': { $in: partnerIds },
    });

    if (propertiesUsingPartners.length > 0) {
      return c.json(
        {
          error: 'Cannot delete partners that are associated with properties. Please remove them from properties first.',
          affectedProperties: propertiesUsingPartners.length,
        },
        400
      );
    }

    // Delete the partners
    const partnerIdsToDelete = partners.map((p) => p._id);
    await Partner.deleteMany({ _id: { $in: partnerIdsToDelete } });

    return c.json({
      message: `${partners.length} partners deleted successfully`,
      deletedCount: partners.length,
      deletedIds: partnerIdsToDelete,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

export default partnersRoutes;

import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import Transaction from '../models/Transaction';
import Property from '../models/Property';
import { auth, buildDataAccessQuery } from '../middleware/auth';
import { IUser, transactionCreateSchema, transactionUpdateSchema } from '@alom-rentals/shared';
import { z } from 'zod';

const transactionsRoutes = new Hono();

// Get all transactions for the authenticated user
transactionsRoutes.get('/', auth, async (c) => {
  try {
    const user = c.get('user');
    const targetUserId = c.req.query('userId');
    const propertyId = c.req.query('propertyId');
    const type = c.req.query('type');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '20');

    // Build property access query to get accessible properties
    const propertyQuery = await buildDataAccessQuery(
      user,
      {},
      {
        createdByField: 'createdBy',
        ownersField: 'owners',
        targetUserId,
      }
    );

    const accessibleProperties = await Property.find(propertyQuery).select('_id');
    const propertyIds = accessibleProperties.map((p) => p._id);

    // Build transaction query
    let transactionQuery: any = {
      propertyId: { $in: propertyIds },
      status: 'active',
    };

    if (propertyId) {
      transactionQuery.propertyId = propertyId;
    }

    if (type) {
      transactionQuery.type = type;
    }

    const transactions = await Transaction.find(transactionQuery)
      .populate('propertyId', 'name address')
      .populate('createdBy', 'fullName email')
      .populate('modifiedBy', 'fullName email')
      .populate('ownerId')
      .populate('rentalId')
      .sort({ date: -1 })
      .skip((page - 1) * limit)
      .limit(limit);

    const total = await Transaction.countDocuments(transactionQuery);

    return c.json({
      transactions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    return c.json({ error: 'Failed to fetch transactions' }, 500);
  }
});

// Create transaction
transactionsRoutes.post('/', auth, zValidator('json', transactionCreateSchema), async (c) => {
  try {
    const user = c.get('user');
    const data = c.req.valid('json');

    // Verify user can access the property
    const property = await Property.findById(data.propertyId);
    if (!property) {
      return c.json({ error: 'Property not found' }, 404);
    }

    const transaction = new Transaction({
      ...data,
      createdBy: user._id,
    });

    await transaction.save();
    await transaction.populate(['propertyId', 'createdBy', 'ownerId', 'rentalId']);

    return c.json(transaction, 201);
  } catch (error) {
    return c.json({ error: 'Failed to create transaction' }, 500);
  }
});

// Update transaction
transactionsRoutes.put('/:id', auth, zValidator('json', transactionUpdateSchema), async (c) => {
  try {
    const user = c.get('user');
    const id = c.req.param('id');
    const data = c.req.valid('json');

    const transaction = await Transaction.findById(id);
    if (!transaction || transaction.status === 'deleted') {
      return c.json({ error: 'Transaction not found' }, 404);
    }

    Object.assign(transaction, data, {
      modifiedBy: user._id,
      modifiedDate: new Date(),
    });

    await transaction.save();
    await transaction.populate(['propertyId', 'createdBy', 'modifiedBy', 'ownerId', 'rentalId']);

    return c.json(transaction);
  } catch (error) {
    return c.json({ error: 'Failed to update transaction' }, 500);
  }
});

// Soft delete transaction
transactionsRoutes.delete('/:id', auth, async (c) => {
  try {
    const user = c.get('user');
    const id = c.req.param('id');

    const transaction = await Transaction.findById(id);
    if (!transaction || transaction.status === 'deleted') {
      return c.json({ error: 'Transaction not found' }, 404);
    }

    transaction.status = 'deleted';
    transaction.deletedBy = user._id as IUser;
    transaction.deletedDate = new Date();

    await transaction.save();

    return c.json({ message: 'Transaction deleted successfully' });
  } catch (error) {
    return c.json({ error: 'Failed to delete transaction' }, 500);
  }
});

// Get transaction summary for a property
transactionsRoutes.get('/summary/:propertyId', auth, async (c) => {
  try {
    const propertyId = c.req.param('propertyId');

    const summary = await Transaction.aggregate([
      { $match: { propertyId: propertyId, status: 'active' } },
      {
        $group: {
          _id: null,
          totalIncome: {
            $sum: { $cond: [{ $gt: ['$amount', 0] }, '$amount', 0] },
          },
          totalExpenses: {
            $sum: { $cond: [{ $lt: ['$amount', 0] }, { $abs: '$amount' }, 0] },
          },
          transactionCount: { $sum: 1 },
        },
      },
      {
        $addFields: {
          netIncome: { $subtract: ['$totalIncome', '$totalExpenses'] },
        },
      },
    ]);

    return c.json(
      summary[0] || {
        totalIncome: 0,
        totalExpenses: 0,
        netIncome: 0,
        transactionCount: 0,
      }
    );
  } catch (error) {
    return c.json({ error: 'Failed to get transaction summary' }, 500);
  }
});

export default transactionsRoutes;

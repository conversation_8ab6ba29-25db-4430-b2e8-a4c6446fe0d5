import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import User from '../models/User';
import Currency from '../models/Currency';
import { auth } from '../middleware/auth';
import { loginSchema, registerSchema, AuthResponse } from '@alom-rentals/shared';

const authRoutes = new Hono();

// Register
authRoutes.post('/register', zValidator('json', registerSchema), async (c) => {
  try {
    const { email, password, fullName } = c.req.valid('json');

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return c.json({ error: 'User already exists' }, 400);
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user
    const user = new User({
      email,
      password: hashedPassword,
      fullName,
    });

    await user.save();

    // Create default currencies for the new user
    try {
      await Currency.createDefaultCurrencies((user._id as any).toString());
    } catch (error) {
      console.error('Failed to create default currencies:', error);
      // Don't fail registration if currency creation fails
    }

    // Generate token with role information
    const token = jwt.sign(
      {
        userId: user._id,
        role: user.role,
      },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: '7d' }
    );

    const response: AuthResponse = {
      token,
      user: {
        id: (user._id as any).toString(),
        email: user.email,
        fullName: user.fullName,
        role: user.role,
        accountStatus: user.accountStatus,
        lastLoginAt: user.lastLoginAt,
        loginCount: user.loginCount,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    };

    return c.json(response, 201);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Login
authRoutes.post('/login', zValidator('json', loginSchema), async (c) => {
  try {
    const { email, password } = c.req.valid('json');

    // Find user
    const user = await User.findOne({ email });
    if (!user) {
      return c.json({ error: 'Invalid credentials' }, 400);
    }

    // Check if account is active
    if (user.accountStatus !== 'ACTIVE') {
      return c.json({ error: 'Account is deactivated. Please contact an administrator.' }, 403);
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return c.json({ error: 'Invalid credentials' }, 400);
    }

    // Update login tracking
    user.lastLoginAt = new Date();
    user.loginCount = (user.loginCount || 0) + 1;
    await user.save();

    // Generate token with role information
    const token = jwt.sign(
      {
        userId: user._id,
        role: user.role,
      },
      process.env.JWT_SECRET || 'fallback-secret',
      { expiresIn: '7d' }
    );

    const response: AuthResponse = {
      token,
      user: {
        id: (user._id as any).toString(),
        email: user.email,
        fullName: user.fullName,
        role: user.role,
        accountStatus: user.accountStatus,
        lastLoginAt: user.lastLoginAt,
        loginCount: user.loginCount,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    };

    return c.json(response);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get current user
authRoutes.get('/me', auth, async (c) => {
  const user = c.get('user');
  return c.json({
    id: user._id,
    email: user.email,
    fullName: user.fullName,
    role: user.role,
  });
});

// Search users
authRoutes.get('/search', auth, async (c) => {
  try {
    const query = c.req.query('q');

    if (!query || query.length < 2) {
      return c.json([]);
    }

    const users = await User.find({
      $or: [{ email: { $regex: query, $options: 'i' } }, { fullName: { $regex: query, $options: 'i' } }],
    })
      .select('email fullName')
      .limit(10);

    return c.json(users);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

export default authRoutes;

import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import Currency from '../models/Currency';
import Property from '../models/Property';
import { auth } from '../middleware/auth';
import { currencyCreateSchema, currencyUpdateSchema, currencyParamsSchema } from '@alom-rentals/shared';

const app = new Hono();

// Apply auth middleware to all routes
app.use('*', auth);

// GET /currencies - Get all currencies for the authenticated user
app.get('/', async (c) => {
  try {
    const user = c.get('user');

    const currencies = await Currency.find({ createdBy: user._id }).sort({
      createdAt: -1,
    });

    return c.json(currencies);
  } catch (error) {
    console.error('Error fetching currencies:', error);
    return c.json({ error: 'Failed to fetch currencies' }, 500);
  }
});

// GET /currencies/:id - Get a specific currency
app.get('/:id', zValidator('param', currencyParamsSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');

    const currency = await Currency.findOne({
      _id: id,
      createdBy: user._id,
    });

    if (!currency) {
      return c.json({ error: 'Currency not found' }, 404);
    }

    return c.json(currency);
  } catch (error) {
    console.error('Error fetching currency:', error);
    return c.json({ error: 'Failed to fetch currency' }, 500);
  }
});

// POST /currencies - Create a new currency
app.post('/', zValidator('json', currencyCreateSchema), async (c) => {
  try {
    const user = c.get('user');
    const currencyData = c.req.valid('json');

    const currency = new Currency({
      ...currencyData,
      createdBy: user._id,
    });

    await currency.save();

    return c.json(currency, 201);
  } catch (error: any) {
    console.error('Error creating currency:', error);

    // Handle duplicate key errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      let message = 'Currency already exists';

      if (field === 'name') {
        message = 'A currency with this name already exists';
      } else if (field === 'code') {
        message = 'A currency with this code already exists';
      }

      return c.json({ error: message }, 400);
    }

    return c.json({ error: 'Failed to create currency' }, 500);
  }
});

// PUT /currencies/:id - Update a currency
app.put('/:id', zValidator('param', currencyParamsSchema), zValidator('json', currencyUpdateSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');
    const updateData = c.req.valid('json');

    const currency = await Currency.findOneAndUpdate({ _id: id, createdBy: user._id }, updateData, { new: true, runValidators: true });

    if (!currency) {
      return c.json({ error: 'Currency not found' }, 404);
    }

    return c.json(currency);
  } catch (error: any) {
    console.error('Error updating currency:', error);

    // Handle duplicate key errors
    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      let message = 'Currency already exists';

      if (field === 'name') {
        message = 'A currency with this name already exists';
      } else if (field === 'code') {
        message = 'A currency with this code already exists';
      }

      return c.json({ error: message }, 400);
    }

    return c.json({ error: 'Failed to update currency' }, 500);
  }
});

// DELETE /currencies/:id - Delete a currency
app.delete('/:id', zValidator('param', currencyParamsSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');

    const currency = await Currency.findOne({
      _id: id,
      createdBy: user._id,
    });

    if (!currency) {
      return c.json({ error: 'Currency not found' }, 404);
    }

    // Check if currency is being used by any properties
    const isInUse = await currency.isInUse();
    if (isInUse) {
      return c.json({ error: 'Cannot delete currency that is being used by properties' }, 400);
    }

    await Currency.findByIdAndDelete(id);

    return c.json({ message: 'Currency deleted successfully' });
  } catch (error) {
    console.error('Error deleting currency:', error);
    return c.json({ error: 'Failed to delete currency' }, 500);
  }
});

// Bulk delete currencies (must be before /:id route)
const bulkDeleteSchema = z.object({
  currencyIds: z.array(z.string()).min(1, 'At least one currency ID is required'),
});

app.delete('/bulk', auth, zValidator('json', bulkDeleteSchema), async (c) => {
  try {
    const user = c.get('user');
    const { currencyIds } = c.req.valid('json');

    // Find currencies where user is creator
    const currencies = await Currency.find({
      _id: { $in: currencyIds },
      createdBy: user._id,
    });

    if (currencies.length === 0) {
      return c.json({ error: 'No currencies found or access denied' }, 404);
    }

    // Check if any currencies are being used in properties
    const propertiesUsingCurrencies = await Property.find({
      currencyId: { $in: currencyIds },
    });

    if (propertiesUsingCurrencies.length > 0) {
      return c.json(
        {
          error: 'Cannot delete currencies that are associated with properties. Please remove them from properties first.',
          affectedProperties: propertiesUsingCurrencies.length,
        },
        400
      );
    }

    // Delete the currencies
    const currencyIdsToDelete = currencies.map((c) => c._id);
    await Currency.deleteMany({ _id: { $in: currencyIdsToDelete } });

    return c.json({
      message: `${currencies.length} currencies deleted successfully`,
      deletedCount: currencies.length,
      deletedIds: currencyIdsToDelete,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

export default app;

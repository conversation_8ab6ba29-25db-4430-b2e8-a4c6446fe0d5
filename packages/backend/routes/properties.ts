import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import bcrypt from 'bcryptjs';
import Property from '../models/Property';
import PropertyService from '../models/PropertyService';
import Partner from '../models/Partner';
import User from '../models/User';
import { auth, buildDataAccessQuery, canAccessResource, canOperatorAccessCustomer, getOperatorPermissions } from '../middleware/auth';
import { propertyCreateSchema, propertyUpdateSchema, propertyParamsSchema, ownerSchema } from '@alom-rentals/shared';
import { z } from 'zod';

const propertiesRoutes = new Hono();

// Get all properties for the authenticated user (or all properties for admin)
propertiesRoutes.get('/', auth, async (c) => {
  try {
    const user = c.get('user');
    const targetUserId = c.req.query('userId'); // Admin can filter by specific user

    // Use the enhanced buildDataAccessQuery that supports operator filtering
    const query = await buildDataAccessQuery(
      user,
      {},
      {
        createdByField: 'createdBy',
        ownersField: 'owners',
        targetUserId,
      }
    );

    const properties = await Property.find(query)
      .populate('owners.owner', 'name email fullName')
      .populate('createdBy', 'fullName email')
      .populate('currencyId', 'name symbol code')
      .sort({ createdAt: -1 });

    // Add cost information to each property
    const propertiesWithCosts = await Promise.all(
      properties.map(async (property) => {
        try {
          const costSummary = await PropertyService.calculateCostSummary(property.id.toString());
          return {
            ...property.toObject(),
            totalMonthlyCost: costSummary.totalMonthlyCost,
            userMonthlyCost: costSummary.userMonthlyCost,
            partnerMonthlyCost: costSummary.partnerMonthlyCost,
            activeServicesCount: costSummary.activeServicesCount,
          };
        } catch (error) {
          // If cost calculation fails, return property without cost info
          return {
            ...property.toObject(),
            totalMonthlyCost: 0,
            userMonthlyCost: 0,
            partnerMonthlyCost: 0,
            activeServicesCount: 0,
          };
        }
      })
    );

    return c.json(propertiesWithCosts);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Create a new property
propertiesRoutes.post('/', auth, zValidator('json', propertyCreateSchema), async (c) => {
  try {
    const user = c.get('user');
    const { name, address, description, propertyType, isRented, owners, currencyId } = c.req.valid('json');
    const customerId = c.req.query('customerId'); // For operators to specify which customer

    // For operators, validate customer access and permissions
    if (user.role === 'OPERATOR') {
      if (!customerId) {
        return c.json({ error: 'Customer ID is required for operators' }, 400);
      }

      const hasAccess = await canOperatorAccessCustomer((user._id as any).toString(), customerId);
      if (!hasAccess) {
        return c.json({ error: 'Access denied. You are not assigned to this customer.' }, 403);
      }

      const permissions = await getOperatorPermissions((user._id as any).toString(), customerId);
      if (!permissions?.canCreateProperties) {
        return c.json({ error: 'You do not have permission to create properties for this customer.' }, 403);
      }
    }

    // Validate ownership percentages sum to 100
    const totalPercentage = owners.reduce((sum, owner) => sum + owner.ownershipPercentage, 0);
    if (Math.abs(totalPercentage - 100) > 0.01) {
      return c.json({ error: 'Ownership percentages must sum to 100%' }, 400);
    }

    // Process owners and find/create users or partners
    const processedOwners = [];
    for (const owner of owners) {
      let ownerId: string;

      if (owner.ownerType === 'User') {
        // For users, find by email or create if needed
        let userOwner = await User.findOne({ email: owner.email });
        if (!userOwner) {
          // Create placeholder user (you might want to handle this differently)
          const salt = await bcrypt.genSalt(10);
          const tempPassword = Math.random().toString(36).slice(-8);
          const hashedPassword = await bcrypt.hash(tempPassword, salt);

          userOwner = new User({
            email: owner.email,
            password: hashedPassword,
            fullName: owner.name,
          });
          await userOwner.save();
        }
        ownerId = (userOwner._id as string).toString();
      } else {
        // For partners, find or create
        let partner = await Partner.findOne({
          email: owner.email,
          createdBy: user._id,
        });

        if (!partner) {
          partner = new Partner({
            email: owner.email,
            name: owner.name,
            createdBy: user._id,
          });
          await partner.save();
        }
        ownerId = (partner._id as string).toString();
      }

      processedOwners.push({
        ownerType: owner.ownerType,
        owner: ownerId,
        ownershipPercentage: owner.ownershipPercentage,
      });
    }

    // Determine the correct createdBy value
    let createdByUserId = user._id;
    if (user.role === 'OPERATOR' && customerId) {
      createdByUserId = customerId;
    }

    const property = new Property({
      name,
      address,
      description,
      propertyType,
      isRented,
      createdBy: createdByUserId,
      owners: processedOwners,
      currencyId,
    });

    await property.save();
    await property.populate('owners.owner', 'name email fullName');
    await property.populate('createdBy', 'fullName email');
    await property.populate('currencyId', 'name symbol code');

    return c.json(property, 201);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get a specific property
propertiesRoutes.get('/:id', auth, zValidator('param', propertyParamsSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');

    let query: any = { _id: id };

    if (user.role !== 'ADMIN') {
      // Non-admin users can only access their own properties
      const userEmail = user.email;
      const userPartners = await Partner.find({ email: userEmail }).select('_id');

      query = {
        _id: id,
        $or: [{ createdBy: user._id }, { 'owners.owner': user._id }, { 'owners.owner': { $in: userPartners } }],
      };
    }
    // Admin can access any property (no additional filtering)

    const property = await Property.findOne(query)
      .populate('owners.owner', 'name email fullName')
      .populate('createdBy', 'fullName email')
      .populate('currencyId', 'name symbol code');

    if (!property) {
      return c.json({ error: 'Property not found' }, 404);
    }

    return c.json(property);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Update a property
propertiesRoutes.put('/:id', auth, zValidator('param', propertyParamsSchema), async (c) => {
  // Custom validation for the request body to handle the owners field properly
  const rawBody = await c.req.json();

  // Validate basic fields manually due to Zod stripping the owners field
  if (rawBody.name !== undefined && (typeof rawBody.name !== 'string' || rawBody.name.length === 0)) {
    return c.json({ error: 'Property name must be a non-empty string' }, 400);
  }
  if (rawBody.address !== undefined && (typeof rawBody.address !== 'string' || rawBody.address.length === 0)) {
    return c.json({ error: 'Address must be a non-empty string' }, 400);
  }
  if (rawBody.propertyType !== undefined && !['residential', 'commercial', 'land', 'other'].includes(rawBody.propertyType)) {
    return c.json({ error: 'Invalid property type' }, 400);
  }
  if (rawBody.isRented !== undefined && typeof rawBody.isRented !== 'boolean') {
    return c.json({ error: 'isRented must be a boolean' }, 400);
  }

  // Handle owners separately if provided
  let validatedOwners;
  if (rawBody.owners) {
    const ownersValidation = z.array(ownerSchema).min(1, 'At least one owner is required').safeParse(rawBody.owners);
    if (!ownersValidation.success) {
      return c.json(
        {
          error: 'Owners validation failed',
          details: ownersValidation.error.errors,
        },
        400
      );
    }
    validatedOwners = ownersValidation.data;
  }
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');
    const { name, address, description, propertyType, isRented, currencyId } = rawBody;
    const owners = validatedOwners;

    // Check if user can access this property using enhanced access control
    const hasAccess = await canAccessResource(user, id, Property, {
      createdByField: 'createdBy',
      ownersField: 'owners',
    });

    if (!hasAccess) {
      return c.json({ error: 'Property not found or access denied' }, 404);
    }

    const property = await Property.findById(id);
    if (!property) {
      return c.json({ error: 'Property not found' }, 404);
    }

    // For operators, check edit permissions
    if (user.role === 'OPERATOR') {
      const customerId = (property.createdBy as any).toString();
      const permissions = await getOperatorPermissions((user._id as any).toString(), customerId);
      if (!permissions?.canEditProperties) {
        return c.json({ error: 'You do not have permission to edit properties for this customer.' }, 403);
      }
    }

    // Update basic fields
    if (name !== undefined) property.name = name;
    if (address !== undefined) property.address = address;
    if (description !== undefined) property.description = description;
    if (propertyType !== undefined) property.propertyType = propertyType;
    if (isRented !== undefined) property.isRented = isRented;
    if (currencyId !== undefined) property.currencyId = currencyId;

    // Update owners if provided
    if (owners !== undefined) {
      // Validate ownership percentages sum to 100
      const totalPercentage = owners.reduce((sum: number, owner: any) => sum + owner.ownershipPercentage, 0);
      if (Math.abs(totalPercentage - 100) > 0.01) {
        return c.json({ error: 'Ownership percentages must sum to 100%' }, 400);
      }

      // Process owners and find/create users or partners
      const processedOwners = [];
      for (const owner of owners) {
        let ownerId: string;

        if (owner.ownerType === 'User') {
          // Find existing user by email
          const existingUser = await User.findOne({ email: owner.email });
          if (existingUser) {
            ownerId = (existingUser._id as any).toString();
          } else {
            // Create a new partner if user doesn't exist
            const newPartner = new Partner({
              email: owner.email,
              name: owner.name,
              createdBy: user._id, // Add the required createdBy field
            });
            await newPartner.save();
            ownerId = (newPartner._id as any).toString();
            owner.ownerType = 'Partner'; // Change to Partner since user doesn't exist
          }
        } else {
          // Partner type
          const existingPartner = await Partner.findOne({
            email: owner.email,
          });
          if (existingPartner) {
            ownerId = (existingPartner._id as any).toString();
          } else {
            const newPartner = new Partner({
              email: owner.email,
              name: owner.name,
              createdBy: user._id, // Add the required createdBy field
            });
            await newPartner.save();
            ownerId = (newPartner._id as any).toString();
          }
        }

        processedOwners.push({
          ownerType: owner.ownerType,
          owner: ownerId,
          ownershipPercentage: owner.ownershipPercentage,
        });
      }

      property.owners = processedOwners;
    }

    await property.save();
    await property.populate('owners.owner', 'name email fullName');
    await property.populate('createdBy', 'fullName email');
    await property.populate('currencyId', 'name symbol code');

    return c.json(property);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Bulk delete properties (must be before /:id route)
const bulkDeleteSchema = z.object({
  propertyIds: z.array(z.string()).min(1, 'At least one property ID is required'),
});

propertiesRoutes.delete('/bulk', auth, zValidator('json', bulkDeleteSchema), async (c) => {
  try {
    const user = c.get('user');
    const { propertyIds } = c.req.valid('json');

    // Use enhanced access control to find accessible properties
    const query = await buildDataAccessQuery(
      user,
      { _id: { $in: propertyIds } },
      {
        createdByField: 'createdBy',
        ownersField: 'owners',
      }
    );

    const properties = await Property.find(query);

    if (properties.length === 0) {
      return c.json({ error: 'No properties found or access denied' }, 404);
    }

    // For operators, check delete permissions for each property
    if (user.role === 'OPERATOR') {
      for (const property of properties) {
        const customerId = (property.createdBy as any).toString();
        const permissions = await getOperatorPermissions((user._id as any).toString(), customerId);
        if (!permissions?.canDeleteProperties) {
          return c.json(
            {
              error: `You do not have permission to delete property "${property.name}" for this customer.`,
            },
            403
          );
        }
      }
    }

    // Delete all found properties and their associated services
    const propertyIdsToDelete = properties.map((p) => p._id);

    // Delete associated property services first
    await PropertyService.deleteMany({ propertyId: { $in: propertyIdsToDelete } });

    // Delete the properties
    await Property.deleteMany({ _id: { $in: propertyIdsToDelete } });

    return c.json({
      message: `${properties.length} properties deleted successfully`,
      deletedCount: properties.length,
      deletedIds: propertyIdsToDelete,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Delete a property
propertiesRoutes.delete('/:id', auth, zValidator('param', propertyParamsSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.valid('param');

    // Check if user can access this property using enhanced access control
    const hasAccess = await canAccessResource(user, id, Property, {
      createdByField: 'createdBy',
      ownersField: 'owners',
    });

    if (!hasAccess) {
      return c.json({ error: 'Property not found or access denied' }, 404);
    }

    const property = await Property.findById(id);
    if (!property) {
      return c.json({ error: 'Property not found' }, 404);
    }

    // For operators, check delete permissions
    if (user.role === 'OPERATOR') {
      const customerId = (property.createdBy as any).toString();
      const permissions = await getOperatorPermissions((user._id as any).toString(), customerId);
      if (!permissions?.canDeleteProperties) {
        return c.json({ error: 'You do not have permission to delete properties for this customer.' }, 403);
      }
    }

    // TODO: Check if property has associated rentals and prevent deletion if so
    // For now, allow deletion

    await Property.findByIdAndDelete(id);

    return c.json({ message: 'Property deleted successfully' });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

export default propertiesRoutes;

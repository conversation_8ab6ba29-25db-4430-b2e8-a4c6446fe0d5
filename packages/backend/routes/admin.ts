import { Hono } from 'hono';
import { auth, requireRole } from '../middleware/auth';
import User from '../models/User';
import { AuditLog } from '../models/AuditLog';
import { zValidator } from '@hono/zod-validator';
import { roleChangeSchema, bulkRoleChangeSchema, userParamsSchema, userCreateSchema, accountStatusChangeSchema } from '@alom-rentals/shared';
import bcrypt from 'bcryptjs';
import Currency from '../models/Currency';

const adminRoutes = new Hono();

// Get all users (admin only)
adminRoutes.get('/users', auth, requireRole(['ADMIN']), async (c) => {
  try {
    const users = await User.find({}, '-password').sort({ createdAt: -1 });
    return c.json({
      success: true,
      data: users,
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return c.json({ error: 'Failed to fetch users' }, 500);
  }
});

// Get user by ID (admin only)
adminRoutes.get('/users/:id', auth, requireRole(['ADMIN']), zValidator('param', userParamsSchema), async (c) => {
  try {
    const { id } = c.req.valid('param');
    const user = await User.findById(id, '-password');

    if (!user) {
      return c.json({ error: 'User not found' }, 404);
    }

    return c.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    return c.json({ error: 'Failed to fetch user' }, 500);
  }
});

// Bulk change user roles (admin only) - Must come before individual user routes
adminRoutes.patch('/users/bulk/role', auth, requireRole(['ADMIN']), zValidator('json', bulkRoleChangeSchema), async (c) => {
  try {
    const { userIds, role } = c.req.valid('json');
    const currentUser = c.get('user');

    // Validate that userIds array is not empty
    if (!userIds || userIds.length === 0) {
      return c.json({ error: 'User IDs array cannot be empty' }, 400);
    }

    // Prevent admin from changing their own role to non-admin
    const currentUserId = (currentUser._id as any).toString();
    if (userIds.includes(currentUserId) && role !== 'ADMIN') {
      return c.json({ error: 'Cannot change your own admin role in bulk operation' }, 400);
    }

    const users = await User.find({ _id: { $in: userIds } });
    const failedUsers: string[] = [];
    let updatedCount = 0;

    for (const user of users) {
      try {
        const oldRole = user.role;
        user.role = role;
        await user.save();

        // Log the role change
        await AuditLog.create({
          userId: user._id,
          changedBy: currentUser._id,
          action: 'BULK_ROLE_CHANGE',
          resourceType: 'User',
          resourceId: user._id.toString(),
          oldValues: { role: oldRole },
          newValues: { role },
          ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip'),
          userAgent: c.req.header('user-agent'),
        });

        updatedCount++;
      } catch (error) {
        failedUsers.push(user._id.toString());
      }
    }

    return c.json({
      success: true,
      message: `Successfully updated ${updatedCount} users`,
      updatedCount,
      failedUsers: failedUsers.length > 0 ? failedUsers : undefined,
    });
  } catch (error) {
    console.error('Error performing bulk role change:', error);
    return c.json({ error: 'Failed to perform bulk role change' }, 500);
  }
});

// Change user role (admin only)
adminRoutes.patch('/users/:id/role', auth, requireRole(['ADMIN']), zValidator('param', userParamsSchema), zValidator('json', roleChangeSchema), async (c) => {
  try {
    const { id } = c.req.valid('param');
    const { role } = c.req.valid('json');
    const currentUser = c.get('user');

    // Prevent admin from changing their own role to non-admin
    if (id === (currentUser._id as any).toString() && role !== 'ADMIN') {
      return c.json({ error: 'Cannot change your own admin role' }, 400);
    }

    const user = await User.findById(id);
    if (!user) {
      return c.json({ error: 'User not found' }, 404);
    }

    const oldRole = user.role;
    user.role = role;
    await user.save();

    // Log the role change
    await AuditLog.create({
      userId: user._id,
      action: 'ROLE_CHANGE',
      resourceType: 'User',
      resourceId: user._id.toString(),
      oldValues: { role: oldRole },
      newValues: { role },
      changedBy: currentUser._id,
      ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip'),
      userAgent: c.req.header('user-agent'),
    });

    return c.json({
      success: true,
      message: `User role changed from ${oldRole} to ${role}`,
      user: {
        id: (user._id as any).toString(),
        email: user.email,
        fullName: user.fullName,
        role: user.role,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error changing user role:', error);
    return c.json({ error: 'Failed to change user role' }, 500);
  }
});

// Get admin statistics
adminRoutes.get('/stats', auth, requireRole(['ADMIN']), async (c) => {
  try {
    const totalUsers = await User.countDocuments();
    const roleDistribution = await User.aggregate([
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 },
        },
      },
    ]);

    const recentUsers = await User.find().sort({ createdAt: -1 }).limit(5).select('email fullName role createdAt');

    const stats = {
      totalUsers,
      roleDistribution: {
        admin: roleDistribution.find((r) => r._id === 'ADMIN')?.count || 0,
        operator: roleDistribution.find((r) => r._id === 'OPERATOR')?.count || 0,
        customer: roleDistribution.find((r) => r._id === 'CUSTOMER')?.count || 0,
      },
      recentUsers,
    };

    return c.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    return c.json({ error: 'Failed to fetch admin statistics' }, 500);
  }
});

// Get audit logs
adminRoutes.get('/audit/logs', auth, requireRole(['ADMIN']), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '50');
    const skip = (page - 1) * limit;

    const logs = await AuditLog.find().populate('userId', 'email fullName').sort({ createdAt: -1 }).skip(skip).limit(limit);

    const total = await AuditLog.countDocuments();

    return c.json({
      logs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching audit logs:', error);
    return c.json({ error: 'Failed to fetch audit logs' }, 500);
  }
});

// Create new user (admin only)
adminRoutes.post('/users', auth, requireRole(['ADMIN']), zValidator('json', userCreateSchema), async (c) => {
  try {
    const { email, password, fullName, role } = c.req.valid('json');
    const currentUser = c.get('user');

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return c.json({ error: 'User with this email already exists' }, 400);
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user
    const user = new User({
      email,
      password: hashedPassword,
      fullName,
      role,
      accountStatus: 'ACTIVE',
    });

    await user.save();

    // Create default currencies for the new user
    try {
      await Currency.createDefaultCurrencies((user._id as any).toString());
    } catch (error) {
      console.error('Failed to create default currencies:', error);
      // Don't fail user creation if currency creation fails
    }

    // Log the user creation
    await AuditLog.create({
      userId: user._id,
      action: 'USER_CREATED',
      details: `User created with role ${role}`,
      performedBy: currentUser._id,
      ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    const userResponse = {
      id: (user._id as any).toString(),
      email: user.email,
      fullName: user.fullName,
      role: user.role,
      accountStatus: user.accountStatus,
      lastLoginAt: user.lastLoginAt,
      loginCount: user.loginCount,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    return c.json(
      {
        success: true,
        message: 'User created successfully',
        user: userResponse,
      },
      201
    );
  } catch (error: any) {
    console.error('Error creating user:', error);
    return c.json({ error: 'Failed to create user' }, 500);
  }
});

// Change user account status (admin only)
adminRoutes.patch(
  '/users/:id/status',
  auth,
  requireRole(['ADMIN']),
  zValidator('param', userParamsSchema),
  zValidator('json', accountStatusChangeSchema),
  async (c) => {
    try {
      const { id } = c.req.valid('param');
      const { status } = c.req.valid('json');
      const currentUser = c.get('user');

      // Prevent admin from deactivating their own account
      if (id === (currentUser._id as any).toString()) {
        return c.json({ error: 'Cannot change your own account status' }, 400);
      }

      const user = await User.findById(id);
      if (!user) {
        return c.json({ error: 'User not found' }, 404);
      }

      const oldStatus = user.accountStatus;
      user.accountStatus = status;
      await user.save();

      // Log the status change
      await AuditLog.create({
        userId: user._id,
        action: 'ACCOUNT_STATUS_CHANGED',
        details: `Account status changed from ${oldStatus} to ${status}`,
        performedBy: currentUser._id,
        ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
        userAgent: c.req.header('user-agent') || 'unknown',
      });

      const userResponse = {
        id: (user._id as any).toString(),
        email: user.email,
        fullName: user.fullName,
        role: user.role,
        accountStatus: user.accountStatus,
        lastLoginAt: user.lastLoginAt,
        loginCount: user.loginCount,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      };

      return c.json({
        success: true,
        message: `User account ${status.toLowerCase()} successfully`,
        user: userResponse,
      });
    } catch (error: any) {
      console.error('Error changing account status:', error);
      return c.json({ error: 'Failed to change account status' }, 500);
    }
  }
);

// Delete user (admin only)
adminRoutes.delete('/users/:id', auth, requireRole(['ADMIN']), zValidator('param', userParamsSchema), async (c) => {
  try {
    const { id } = c.req.valid('param');
    const currentUser = c.get('user');

    // Prevent admin from deleting their own account
    if (id === (currentUser._id as any).toString()) {
      return c.json({ error: 'Cannot delete your own account' }, 400);
    }

    const user = await User.findById(id);
    if (!user) {
      return c.json({ error: 'User not found' }, 404);
    }

    // Log the user deletion before deleting
    await AuditLog.create({
      userId: user._id,
      action: 'USER_DELETED',
      details: `User ${user.email} (${user.fullName}) deleted`,
      performedBy: currentUser._id,
      ipAddress: c.req.header('x-forwarded-for') || c.req.header('x-real-ip') || 'unknown',
      userAgent: c.req.header('user-agent') || 'unknown',
    });

    // Delete the user
    await User.findByIdAndDelete(id);

    return c.json({
      success: true,
      message: 'User deleted successfully',
    });
  } catch (error: any) {
    console.error('Error deleting user:', error);
    return c.json({ error: 'Failed to delete user' }, 500);
  }
});

// Get role change audit logs (admin only)
adminRoutes.get('/audit/role-changes', auth, requireRole(['ADMIN']), async (c) => {
  try {
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const skip = (page - 1) * limit;

    const auditLogs = await AuditLog.find({
      action: { $in: ['ROLE_CHANGE', 'BULK_ROLE_CHANGE'] },
    })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await AuditLog.countDocuments({
      action: { $in: ['ROLE_CHANGE', 'BULK_ROLE_CHANGE'] },
    });

    return c.json({
      success: true,
      data: auditLogs,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error: any) {
    console.error('Error fetching audit logs:', error);
    return c.json({ error: 'Failed to fetch audit logs' }, 500);
  }
});

export default adminRoutes;

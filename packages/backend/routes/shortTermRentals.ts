import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import { shortTermRentalCreateSchema, shortTermRentalUpdateSchema, CostCalculationResponse, ShortTermRentalsResponse } from '@alom-rentals/shared';
import ShortTermRental from '../models/ShortTermRental';
import Commission from '../models/Commission';
import Guest from '../models/Guest';
import Property from '../models/Property';
import PropertyService from '../models/PropertyService';
import { auth } from '../middleware/auth';

const app = new Hono();

// Apply auth middleware to all routes
app.use('*', auth);

// Get all short-term rentals for the authenticated user (or all rentals for admin)
app.get('/', async (c) => {
  try {
    const user = c.get('user');
    const page = parseInt(c.req.query('page') || '1');
    const limit = parseInt(c.req.query('limit') || '10');
    const status = c.req.query('status');
    const propertyId = c.req.query('propertyId');
    const targetUserId = c.req.query('userId'); // Admin can filter by specific user

    const skip = (page - 1) * limit;

    // Build query
    let query: any = {};

    if (user.role === 'ADMIN') {
      // Admin can see all rentals, optionally filtered by user
      if (targetUserId) {
        query.createdBy = targetUserId;
      }
      // If no targetUserId, admin sees all rentals (no additional filtering)
    } else {
      // Non-admin users see only their own rentals
      query.createdBy = user._id;
    }

    if (status) query.status = status;
    if (propertyId) query.propertyId = propertyId;

    const rentals = await ShortTermRental.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate([
        {
          path: 'property',
          select: 'name address propertyType',
          populate: {
            path: 'currencyId',
            select: 'name symbol code',
          },
        },
        {
          path: 'guests.guest',
          select: 'firstName lastName email documentType documentNumber phoneNumber fullName',
        },
        {
          path: 'commissions',
          populate: {
            path: 'receiver',
            select: 'name email fullName',
          },
        },
      ]);

    const total = await ShortTermRental.countDocuments(query);

    const response: ShortTermRentalsResponse = {
      rentals: rentals as any,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    };

    return c.json(response);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get a specific short-term rental
app.get('/:id', async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.param();

    const rental = await ShortTermRental.findOne({
      _id: id,
      createdBy: user._id,
    }).populate([
      {
        path: 'property',
        select: 'name address propertyType',
        populate: {
          path: 'currencyId',
          select: 'name symbol code',
        },
      },
      {
        path: 'guests.guest',
        select:
          'firstName lastName email documentType documentNumber phoneNumber fullName isForeigner nationality countryOfOrigin nextDestination cityOfResidency cityOfBirth tripMotivation',
      },
      {
        path: 'commissions',
        populate: {
          path: 'receiver',
          select: 'name email fullName',
        },
      },
      {
        path: 'createdBy',
        select: 'fullName email',
      },
    ]);

    if (!rental) {
      return c.json({ error: 'Rental not found' }, 404);
    }

    return c.json(rental);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Calculate cost amount from property services
app.get('/calculate-cost/:propertyId', async (c) => {
  try {
    const user = c.get('user');
    const { propertyId } = c.req.param();

    // Verify property belongs to user
    const property = await Property.findOne({
      _id: propertyId,
      createdBy: user._id,
    });

    if (!property) {
      return c.json({ error: 'Property not found' }, 404);
    }

    // Get active property services
    const services = await PropertyService.find({
      propertyId,
      status: 'active',
    });

    // Calculate total cost (sum of all active services)
    const totalCost = services.reduce((sum, service) => sum + service.cost, 0);

    const response: CostCalculationResponse = {
      costAmount: Math.round(totalCost * 100) / 100,
      services: services.map((s) => ({
        id: (s._id as any).toString(),
        name: s.name,
        cost: s.cost,
        frequency: s.frequency,
        mandatory: s.mandatory,
      })),
    };

    return c.json(response);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Create a new short-term rental
app.post('/', zValidator('json', shortTermRentalCreateSchema), async (c) => {
  try {
    const user = c.get('user');
    const data = c.req.valid('json');

    // Verify property belongs to user
    const property = await Property.findOne({
      _id: data.property,
      createdBy: user._id,
    });

    if (!property) {
      return c.json({ error: 'Property not found' }, 404);
    }

    // Process guests
    const processedGuests = [];
    for (const guestData of data.guests) {
      let guestId;

      if (guestData.guestId) {
        // Use existing guest
        const existingGuest = await Guest.findOne({
          _id: guestData.guestId,
          createdBy: user._id,
        });
        if (!existingGuest) {
          return c.json({ error: 'Guest not found' }, 404);
        }
        guestId = existingGuest._id;
      } else if (guestData.guest) {
        // Create new guest
        const newGuest = new Guest({
          ...guestData.guest,
          createdBy: user._id,
        });
        await newGuest.save();
        guestId = newGuest._id;
      }

      processedGuests.push({
        guest: guestId,
        isPrincipalContact: guestData.isPrincipalContact,
      });
    }

    // Process commissions
    const commissionIds = [];
    if (data.commissions) {
      for (const commissionData of data.commissions) {
        const commission = new Commission({
          ...commissionData,
          receiverId: commissionData.receiverId,
        });
        await commission.save();
        commissionIds.push(commission._id);
      }
    }

    // Create rental
    const rental = new ShortTermRental({
      property: data.property,
      startDate: new Date(data.startDate),
      endDate: new Date(data.endDate),
      rentalAmount: data.rentalAmount,
      costAmount: data.costAmount,
      status: data.status,
      isPaid: data.isPaid,
      paidDate: data.paidDate ? new Date(data.paidDate) : undefined,
      discountAmount: data.discountAmount,
      discountReason: data.discountReason,
      guests: processedGuests,
      commissions: commissionIds,
      createdBy: user._id,
    });

    await rental.save();
    await rental.populateAll();

    return c.json(rental, 201);
  } catch (error: any) {
    // Handle validation errors
    if (
      error.name === 'ValidationError' ||
      error.message.includes('validation failed') ||
      error.message.includes('overlapping') ||
      error.message.includes('overlaps') ||
      error.message.includes('Discount')
    ) {
      return c.json({ error: error.message }, 400);
    }
    return c.json({ error: error.message }, 500);
  }
});

// Update a short-term rental
app.put('/:id', zValidator('json', shortTermRentalUpdateSchema), async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.param();
    const data = c.req.valid('json');

    const rental = await ShortTermRental.findOne({
      _id: id,
      createdBy: user._id,
    });

    if (!rental) {
      return c.json({ error: 'Rental not found' }, 404);
    }

    // Check for overlapping rentals if dates are being updated
    if (data.startDate || data.endDate) {
      const startDate = data.startDate ? new Date(data.startDate) : rental.startDate;
      const endDate = data.endDate ? new Date(data.endDate) : rental.endDate;

      const overlappingRental = await ShortTermRental.findOne({
        _id: { $ne: id },
        property: rental.property,
        status: { $in: ['active'] },
        $or: [
          {
            startDate: { $lte: endDate },
            endDate: { $gte: startDate },
          },
        ],
      });

      if (overlappingRental) {
        return c.json({ error: 'Property has overlapping rental dates' }, 400);
      }
    }

    // Update basic fields
    if (data.startDate) rental.startDate = new Date(data.startDate);
    if (data.endDate) rental.endDate = new Date(data.endDate);
    if (data.rentalAmount !== undefined) rental.rentalAmount = data.rentalAmount;
    if (data.costAmount !== undefined) rental.costAmount = data.costAmount;
    if (data.status) rental.status = data.status;
    if (data.isPaid !== undefined) rental.isPaid = data.isPaid;
    if (data.paidDate) rental.paidDate = new Date(data.paidDate);
    if (data.discountAmount !== undefined) rental.discountAmount = data.discountAmount;
    if (data.discountReason !== undefined) rental.discountReason = data.discountReason;

    // Update guests if provided
    if (data.guests) {
      const processedGuests = [];
      for (const guestData of data.guests) {
        let guestId;

        if (guestData.guestId) {
          // Use existing guest
          const existingGuest = await Guest.findOne({
            _id: guestData.guestId,
            createdBy: user._id,
          });
          if (!existingGuest) {
            return c.json({ error: 'Guest not found' }, 404);
          }
          guestId = existingGuest._id;
        } else if (guestData.guest) {
          // Create new guest
          const newGuest = new Guest({
            ...guestData.guest,
            createdBy: user._id,
          });
          await newGuest.save();
          guestId = newGuest._id;
        }

        processedGuests.push({
          guest: guestId,
          isPrincipalContact: guestData.isPrincipalContact,
        });
      }
      rental.guests = processedGuests;
    }

    // Update commissions if provided
    if (data.commissions) {
      // Delete existing commissions
      await Commission.deleteMany({ _id: { $in: rental.commissions } });

      const commissionIds = [];
      for (const commissionData of data.commissions) {
        const commission = new Commission({
          amount: commissionData.amount,
          type: commissionData.type,
          receiverType: commissionData.receiverType,
          receiver: commissionData.receiverId,
          status: commissionData.status || 'pending',
          paidDate: commissionData.paidDate ? new Date(commissionData.paidDate) : undefined,
          createdBy: user._id,
        });
        await commission.save();
        commissionIds.push(commission._id);
      }
      rental.commissions = commissionIds;
    }

    await rental.save();
    await rental.populateAll();

    return c.json(rental);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Delete a short-term rental
app.delete('/:id', async (c) => {
  try {
    const user = c.get('user');
    const { id } = c.req.param();

    const rental = await ShortTermRental.findOne({
      _id: id,
      createdBy: user._id,
    });

    if (!rental) {
      return c.json({ error: 'Rental not found' }, 404);
    }

    // Delete associated commissions
    await Commission.deleteMany({ _id: { $in: rental.commissions } });

    // Delete the rental
    await ShortTermRental.findByIdAndDelete(id);

    return c.json({ message: 'Rental deleted successfully' });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Bulk delete short-term rentals (must be before /:id route)
const bulkDeleteSchema = z.object({
  rentalIds: z.array(z.string()).min(1, 'At least one rental ID is required'),
});

app.delete('/bulk', zValidator('json', bulkDeleteSchema), async (c) => {
  try {
    const user = c.get('user');
    const { rentalIds } = c.req.valid('json');

    // Find rentals where user is creator
    const rentals = await ShortTermRental.find({
      _id: { $in: rentalIds },
      createdBy: user._id,
    });

    if (rentals.length === 0) {
      return c.json({ error: 'No rentals found or access denied' }, 404);
    }

    // Delete associated commissions first
    await Commission.deleteMany({ rentalId: { $in: rentalIds } });

    // Delete the rentals
    const rentalIdsToDelete = rentals.map((r) => r._id);
    await ShortTermRental.deleteMany({ _id: { $in: rentalIdsToDelete } });

    return c.json({
      message: `${rentals.length} rentals deleted successfully`,
      deletedCount: rentals.length,
      deletedIds: rentalIdsToDelete,
    });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

export default app;

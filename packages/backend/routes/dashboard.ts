import { Hono } from 'hono';
import Property from '../models/Property';
import ShortTermRental from '../models/ShortTermRental';
import { auth } from '../middleware/auth';
import { DashboardStats, MonthlyRentalData } from '@alom-rentals/shared';

const dashboardRoutes = new Hono();

// Get dashboard statistics
dashboardRoutes.get('/stats', auth, async (c) => {
  try {
    const user = c.get('user');

    // Get user's properties
    const userProperties = await Property.find({
      createdBy: user._id,
    });

    const propertyIds = userProperties.map((p) => p._id);

    // Get total properties count
    const totalProperties = userProperties.length;

    // Get all rentals for user's properties to calculate real stats
    const rentals = await ShortTermRental.find({
      propertyId: { $in: propertyIds },
    });

    // Calculate current month's income (rentals that are active this month)
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    let totalMonthlyIncome = 0;
    let totalPendingDebts = 0;
    let totalYearlyRevenue = 0;
    let totalYearlyCosts = 0;
    let currentMonthCosts = 0;

    rentals.forEach((rental) => {
      const startDate = new Date(rental.startDate);
      const endDate = new Date(rental.endDate);

      // Check if rental is active in current month
      const rentalStartMonth = startDate.getMonth();
      const rentalStartYear = startDate.getFullYear();
      const rentalEndMonth = endDate.getMonth();
      const rentalEndYear = endDate.getFullYear();

      // If rental overlaps with current month
      if (
        (rentalStartYear < currentYear || (rentalStartYear === currentYear && rentalStartMonth <= currentMonth)) &&
        (rentalEndYear > currentYear || (rentalEndYear === currentYear && rentalEndMonth >= currentMonth))
      ) {
        totalMonthlyIncome += rental.rentalAmount;
        currentMonthCosts += rental.costAmount;
      }

      // Add to yearly totals if rental is in current year
      if (rentalStartYear === currentYear) {
        totalYearlyRevenue += rental.rentalAmount;
        totalYearlyCosts += rental.costAmount;
      }

      // Consider unpaid active rentals as pending debts (costs that need to be paid)
      if (rental.status === 'active' && !rental.isPaid) {
        totalPendingDebts += rental.costAmount;
      }
    });

    // Net income should be current month's income minus current month's costs
    const netPropertyIncome = totalMonthlyIncome - currentMonthCosts;

    const response: DashboardStats = {
      totalProperties,
      totalMonthlyIncome,
      totalPendingDebts,
      netPropertyIncome,
    };

    return c.json(response);
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

// Get monthly rental statistics
dashboardRoutes.get('/monthly-rentals', auth, async (c) => {
  try {
    const user = c.get('user');

    // Get user's properties
    const userProperties = await Property.find({
      createdBy: user._id,
    });

    const propertyIds = userProperties.map((p) => p._id);

    // Get all rentals for user's properties
    const rentals = await ShortTermRental.find({
      propertyId: { $in: propertyIds },
    }).populate('property');

    // Group rentals by month
    const monthlyData: { [key: string]: MonthlyRentalData } = {};

    rentals.forEach((rental) => {
      const startDate = new Date(rental.startDate);
      const monthKey = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;
      const monthName = startDate.toLocaleString('default', { month: 'long' });
      const year = startDate.getFullYear();

      if (!monthlyData[monthKey]) {
        monthlyData[monthKey] = {
          month: monthKey,
          monthName,
          year,
          totalRentals: 0,
          totalRevenue: 0,
          totalCosts: 0,
          netIncome: 0,
        };
      }

      monthlyData[monthKey].totalRentals += 1;
      monthlyData[monthKey].totalRevenue += rental.rentalAmount;
      monthlyData[monthKey].totalCosts += rental.costAmount;
      monthlyData[monthKey].netIncome += rental.rentalAmount - rental.costAmount;
    });

    // Convert to array and sort by month
    const data = Object.values(monthlyData).sort((a, b) => a.month.localeCompare(b.month));

    return c.json({ data });
  } catch (error: any) {
    return c.json({ error: error.message }, 500);
  }
});

export default dashboardRoutes;

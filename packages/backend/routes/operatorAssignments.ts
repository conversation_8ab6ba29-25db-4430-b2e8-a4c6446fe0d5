import { Hono } from 'hono';
import { auth, requireRole } from '../middleware/auth';
import OperatorAssignment from '../models/OperatorAssignment';
import User from '../models/User';
import { zValidator } from '@hono/zod-validator';
import { operatorAssignmentCreateSchema, bulkOperatorAssignmentCreateSchema, operatorParamsSchema } from '@alom-rentals/shared';

const operatorAssignmentRoutes = new Hono();

// Get all operators with their assignments (admin only)
operatorAssignmentRoutes.get('/operators', auth, requireRole(['ADMIN']), async (c) => {
  try {
    const operators = await User.find({ role: 'OPERATOR' }, '-password');
    const operatorsWithAssignments = [];

    for (const operator of operators) {
      const assignments = await OperatorAssignment.find({
        operatorId: operator._id,
        isActive: true,
      }).populate('customerId', 'email fullName');

      const assignedCustomers = assignments.map((assignment) => ({
        id: assignment.customerId._id.toString(),
        email: (assignment.customerId as any).email,
        fullName: (assignment.customerId as any).fullName,
        permissions: assignment.permissions,
        assignedAt: assignment.assignedAt,
      }));

      operatorsWithAssignments.push({
        id: operator._id.toString(),
        email: operator.email,
        fullName: operator.fullName,
        role: operator.role,
        assignedCustomers,
        totalAssignments: assignedCustomers.length,
      });
    }

    return c.json({ data: operatorsWithAssignments });
  } catch (error) {
    console.error('Error fetching operators with assignments:', error);
    return c.json({ error: 'Failed to fetch operators' }, 500);
  }
});

// Get available customers for assignment (admin only)
operatorAssignmentRoutes.get('/customers', auth, requireRole(['ADMIN']), async (c) => {
  try {
    const customers = await User.find({ role: 'CUSTOMER' }, '-password').sort({ createdAt: -1 });

    const availableCustomers = customers.map((customer) => ({
      id: customer._id.toString(),
      email: customer.email,
      fullName: customer.fullName,
      createdAt: customer.createdAt,
    }));

    return c.json({ data: availableCustomers });
  } catch (error) {
    console.error('Error fetching available customers:', error);
    return c.json({ error: 'Failed to fetch customers' }, 500);
  }
});

// Get assigned customers for an operator
operatorAssignmentRoutes.get('/operator/:operatorId', auth, zValidator('param', operatorParamsSchema), async (c) => {
  try {
    const { operatorId } = c.req.valid('param');
    const currentUser = c.get('user');

    // Only allow admins or the operator themselves to view assignments
    if (currentUser.role !== 'ADMIN' && (currentUser as any)._id.toString() !== operatorId) {
      return c.json({ error: 'Unauthorized' }, 403);
    }

    const assignments = await OperatorAssignment.find({
      operatorId,
      isActive: true,
    }).populate('customerId', 'email fullName');

    const assignedCustomers = assignments.map((assignment) => ({
      id: assignment.customerId._id.toString(),
      email: (assignment.customerId as any).email,
      fullName: (assignment.customerId as any).fullName,
      permissions: assignment.permissions,
      assignedAt: assignment.assignedAt,
    }));

    return c.json({ data: assignedCustomers });
  } catch (error) {
    console.error('Error fetching assigned customers:', error);
    return c.json({ error: 'Failed to fetch assigned customers' }, 500);
  }
});

// Create operator assignment (admin only)
operatorAssignmentRoutes.post('/', auth, requireRole(['ADMIN']), zValidator('json', operatorAssignmentCreateSchema), async (c) => {
  try {
    const { operatorId, customerId, permissions } = c.req.valid('json');
    const currentUser = c.get('user');

    // Check if assignment already exists
    const existingAssignment = await OperatorAssignment.findOne({ operatorId, customerId });
    if (existingAssignment) {
      if (existingAssignment.isActive) {
        return c.json({ error: 'Assignment already exists' }, 400);
      } else {
        // Reactivate existing assignment
        existingAssignment.isActive = true;
        existingAssignment.permissions = permissions;
        existingAssignment.assignedBy = currentUser._id;
        existingAssignment.assignedAt = new Date();
        await existingAssignment.save();

        return c.json({ assignment: existingAssignment }, 200);
      }
    }

    const assignment = new OperatorAssignment({
      operatorId,
      customerId,
      permissions,
      assignedBy: currentUser._id,
    });

    await assignment.save();
    return c.json({ assignment }, 201);
  } catch (error) {
    console.error('Error creating operator assignment:', error);
    return c.json({ error: 'Failed to create assignment' }, 500);
  }
});

// Bulk create operator assignments (admin only)
operatorAssignmentRoutes.post('/bulk', auth, requireRole(['ADMIN']), zValidator('json', bulkOperatorAssignmentCreateSchema), async (c) => {
  try {
    const { operatorId, customerIds, permissions } = c.req.valid('json');
    const currentUser = c.get('user');

    let success = 0;
    let errors = 0;
    const errorDetails: Array<{ customerId: string; error: string }> = [];
    const createdAssignments = [];

    for (const customerId of customerIds) {
      try {
        // Check if assignment already exists
        const existingAssignment = await OperatorAssignment.findOne({ operatorId, customerId });
        if (existingAssignment) {
          if (existingAssignment.isActive) {
            errors++;
            errorDetails.push({ customerId, error: 'Assignment already exists' });
            continue;
          } else {
            // Reactivate existing assignment
            existingAssignment.isActive = true;
            existingAssignment.permissions = permissions;
            existingAssignment.assignedBy = currentUser._id;
            existingAssignment.assignedAt = new Date();
            await existingAssignment.save();

            createdAssignments.push(existingAssignment);
            success++;
            continue;
          }
        }

        const assignment = new OperatorAssignment({
          operatorId,
          customerId,
          permissions,
          assignedBy: currentUser._id,
        });

        await assignment.save();
        createdAssignments.push(assignment);
        success++;
      } catch (error) {
        errors++;
        errorDetails.push({
          customerId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return c.json({
      success,
      errors,
      data: createdAssignments,
      errorDetails: errorDetails.length > 0 ? errorDetails : undefined,
    });
  } catch (error) {
    console.error('Error creating bulk operator assignments:', error);
    return c.json({ error: 'Failed to create bulk assignments' }, 500);
  }
});

// Delete operator assignment (admin only)
operatorAssignmentRoutes.delete('/:id', auth, requireRole(['ADMIN']), async (c) => {
  try {
    const { id } = c.req.param();

    const assignment = await OperatorAssignment.findById(id);
    if (!assignment) {
      return c.json({ error: 'Assignment not found' }, 404);
    }

    assignment.isActive = false;
    await assignment.save();

    return c.json({ message: 'Assignment deleted successfully' });
  } catch (error) {
    console.error('Error deleting operator assignment:', error);
    return c.json({ error: 'Failed to delete assignment' }, 500);
  }
});

export default operatorAssignmentRoutes;

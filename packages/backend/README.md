# alomrentals Server - TypeScript + Hono + Zod

This server has been converted from Express/JavaScript to Hono/TypeScript with Zod validation for better type safety and performance.

## Tech Stack

- **Runtime**: Node.js
- **Framework**: [Hono](https://hono.dev/) - Fast, lightweight web framework
- **Language**: TypeScript
- **Validation**: [Zod](https://zod.dev/) with [@hono/zod-validator](https://github.com/honojs/middleware/tree/main/packages/zod-validator)
- **Database**: MongoDB with Mongoose
- **Authentication**: JWT

## Key Features

### 🔒 Type Safety

- Full TypeScript implementation
- Strongly typed models with interfaces
- Type-safe request/response handling

### ✅ Request Validation

- Zod schemas for all API endpoints
- Automatic validation with `@hono/zod-validator`
- Clear error messages for invalid requests

### ⚡ Performance

- Hono is significantly faster than Express
- Lightweight middleware system
- Optimized for modern JavaScript runtimes

### 🏗️ Architecture

- Clean separation of concerns
- Modular route organization
- Reusable validation schemas

## Project Structure

```
server/
├── models/           # Mongoose models with TypeScript interfaces
├── routes/           # Hono route handlers
├── schemas/          # Zod validation schemas
├── middleware/       # Custom middleware (auth, etc.)
├── server.ts         # Main server file
└── tsconfig.json     # TypeScript configuration
```

## Models

All models are now TypeScript with proper interfaces:

- `User.ts` - User authentication and profile
- `Partner.ts` - Property partners/co-owners
- `Property.ts` - Real estate properties
- `Profile.ts` - Extended user profile information
- `Rental.ts` - Property rental information
- `Debt.ts` - Property-related debts and payments
- `Income.ts` - Property income tracking
- `RecurrentCost.ts` - Recurring property expenses
- `PersonalExpense.ts` - Personal financial expenses
- `PersonalIncome.ts` - Personal income tracking
- `PersonalInvestment.ts` - Personal investment tracking

## API Routes

### Authentication (`/api/auth`)

- `POST /register` - User registration
- `POST /login` - User login
- `GET /me` - Get current user

### Partners (`/api/partners`)

- `GET /` - List all partners
- `GET /:id` - Get specific partner
- `POST /` - Create new partner
- `PUT /:id` - Update partner
- `DELETE /:id` - Delete partner

### Properties (`/api/properties`)

- `GET /` - List all properties
- `GET /:id` - Get specific property
- `POST /` - Create new property
- `PUT /:id` - Update property

### Dashboard (`/api/dashboard`)

- `GET /stats` - Get dashboard statistics

## Validation Schemas

### Auth Schemas (`schemas/auth.ts`)

```typescript
const loginSchema = z.object({
  email: z.string().email("Invalid email format"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});
```

### Partner Schemas (`schemas/partner.ts`)

```typescript
const createPartnerSchema = z.object({
  email: z.string().email("Invalid email format"),
  name: z.string().min(1, "Name is required"),
});
```

### Property Schemas (`schemas/property.ts`)

```typescript
const createPropertySchema = z.object({
  name: z.string().min(1, "Property name is required"),
  address: z.string().optional(),
  // ... more fields
});
```

## Development

### Running the Server

```bash
yarn dev
```

This uses `tsx` to run TypeScript directly with hot reloading via nodemon.

### Code Style

This project follows the monorepo's code style configuration:
- **Prettier** for consistent formatting (configured at root level)
- **Conventional Commits** for commit messages
- **Pre-commit hooks** automatically format code

### Type Checking

```bash
npx tsc --noEmit
```

### Adding New Routes

1. Create Zod schemas in `schemas/`
2. Create route handlers in `routes/`
3. Register routes in `server.ts`

Example:

```typescript
import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { auth } from "../middleware/auth";
import { mySchema } from "../schemas/mySchema";

const myRoutes = new Hono();

myRoutes.post("/", auth, zValidator("json", mySchema), async (c) => {
  const data = c.req.valid("json"); // Fully typed!
  // Handle request...
  return c.json(response);
});

export default myRoutes;
```

## Migration Notes

### From Express to Hono

- `req/res` → `c` (Context)
- `req.body` → `c.req.valid('json')`
- `req.params` → `c.req.valid('param')`
- `res.json()` → `c.json()`
- `res.status().json()` → `c.json(data, status)`

### Authentication

- User is now available via `c.get('user')`
- Middleware uses Hono's context system
- JWT validation remains the same

### Error Handling

- Consistent error responses with proper HTTP status codes
- Zod validation errors are automatically handled
- Type-safe error messages

## Benefits

1. **Type Safety**: Catch errors at compile time
2. **Performance**: Hono is faster than Express
3. **Validation**: Automatic request validation with clear errors
4. **Developer Experience**: Better IntelliSense and debugging
5. **Maintainability**: Cleaner, more organized code structure

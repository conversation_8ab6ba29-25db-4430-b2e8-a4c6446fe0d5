import { serve } from '@hono/node-server';
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import mongoose from 'mongoose';
import 'dotenv/config';

import authRoutes from './routes/auth';
import propertyRoutes from './routes/properties';
import partnersRoutes from './routes/partners';
import dashboardRoutes from './routes/dashboard';
import guestRoutes from './routes/guests';
import currencyRoutes from './routes/currencies';
import shortTermRentalRoutes from './routes/shortTermRentals';
import adminRoutes from './routes/admin';
import operatorAssignmentRoutes from './routes/operatorAssignments';
import transactionsRoutes from './routes/transactions';

const app = new Hono();
const PORT = Number(process.env.SERVER_PORT) || 5000;

// Middleware
app.use('*', cors());

// Connect to MongoDB
mongoose
  .connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/property-finance')
  .then(() => console.log('Connected to MongoDB'))
  .catch((error) => console.error('MongoDB connection error:', error));

// Routes
app.route('/api/auth', authRoutes);
app.route('/api/properties', propertyRoutes);
app.route('/api/partners', partnersRoutes);
app.route('/api/dashboard', dashboardRoutes);
app.route('/api/guests', guestRoutes);
app.route('/api/currencies', currencyRoutes);
app.route('/api/short-term-rentals', shortTermRentalRoutes);
app.route('/api/admin', adminRoutes);
app.route('/api/operator-assignments', operatorAssignmentRoutes);
app.route('/api/transactions', transactionsRoutes);

// Health check
app.get('/api/health', (c) => {
  return c.json({ status: 'OK', timestamp: new Date().toISOString() });
});

console.log(`Server running on port ${PORT}`);

serve({
  fetch: app.fetch,
  port: PORT,
});

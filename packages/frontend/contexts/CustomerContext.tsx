import React, { useState, useEffect, ReactNode } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useAssignedCustomers } from '../hooks/useOperatorAssignments';
import { CustomerContext, CustomerContextType } from './CustomerContextDefinition';
import { IAssignedCustomer } from '@alom-rentals/shared';

interface CustomerProviderProps {
  children: ReactNode;
}

export const CustomerProvider: React.FC<CustomerProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);

  // Only fetch assigned customers for operators
  const { data: assignedCustomersData, isLoading, error } = useAssignedCustomers(user?.role === 'OPERATOR' ? user.id : undefined);

  const assignedCustomers = React.useMemo(() => assignedCustomersData?.data || [], [assignedCustomersData?.data]);

  // Auto-select first customer if none selected and customers are available
  useEffect(() => {
    if (user?.role === 'OPERATOR' && assignedCustomers.length > 0 && !selectedCustomer) {
      setSelectedCustomer(assignedCustomers[0].id);
    }
  }, [assignedCustomers, selectedCustomer, user?.role]);

  // Clear selected customer when user changes or logs out
  useEffect(() => {
    if (!user || user.role !== 'OPERATOR') {
      setSelectedCustomer(null);
    }
  }, [user]);

  // Get permissions for the selected customer
  const customerPermissions = React.useMemo(() => {
    if (!selectedCustomer || user?.role !== 'OPERATOR') {
      return null;
    }

    const customer = assignedCustomers.find((c: IAssignedCustomer) => c.id === selectedCustomer);
    return customer?.permissions || null;
  }, [selectedCustomer, assignedCustomers, user?.role]);

  const contextValue: CustomerContextType = {
    selectedCustomer,
    setSelectedCustomer,
    assignedCustomers,
    isLoading,
    error,
    customerPermissions,
  };

  return <CustomerContext.Provider value={contextValue}>{children}</CustomerContext.Provider>;
};

// Note: Utility hooks moved to hooks/useCustomerUtils.ts to avoid Fast Refresh warnings

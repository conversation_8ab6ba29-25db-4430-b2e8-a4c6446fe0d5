import { createContext } from 'react';
import { IAssignedCustomer } from '@alom-rentals/shared';

export interface CustomerContextType {
  selectedCustomer: string | null;
  setSelectedCustomer: (customerId: string | null) => void;
  assignedCustomers: IAssignedCustomer[];
  isLoading: boolean;
  error: Error | null;
  customerPermissions: {
    canCreateProperties: boolean;
    canEditProperties: boolean;
    canDeleteProperties: boolean;
    canManageRentals: boolean;
    canViewFinancials: boolean;
  } | null;
}

export const CustomerContext = createContext<CustomerContextType | null>(null);

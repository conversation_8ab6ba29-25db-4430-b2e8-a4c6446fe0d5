import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { resetUserPassword, forcePasswordChange, generatePasswordResetToken, PasswordResetRequest, ForcePasswordChangeRequest } from '../api/admin';
import { useAuth } from './useAuth';

// Hook for resetting user password
export const useResetUserPassword = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, passwordData }: { userId: string; passwordData: PasswordResetRequest }) => resetUserPassword(userId, passwordData),
    onSuccess: (data) => {
      toast.success(data.message);
      // Invalidate user queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: (error: Error & { response?: { data?: { error?: string } } }) => {
      const message = error.response?.data?.error || 'Failed to reset password';
      toast.error(message);
    },
  });
};

// Hook for forcing password change
export const useForcePasswordChange = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ userId, forceChangeData }: { userId: string; forceChangeData: ForcePasswordChangeRequest }) => forcePasswordChange(userId, forceChangeData),
    onSuccess: (data) => {
      toast.success(data.message);
      // Invalidate user queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['admin-users'] });
      queryClient.invalidateQueries({ queryKey: ['users'] });
    },
    onError: (error: Error & { response?: { data?: { error?: string } } }) => {
      const message = error.response?.data?.error || 'Failed to update force password change setting';
      toast.error(message);
    },
  });
};

// Hook for generating password reset token
export const useGeneratePasswordResetToken = () => {
  return useMutation({
    mutationFn: (userId: string) => generatePasswordResetToken(userId),
    onSuccess: (data) => {
      toast.success(data.message);
      // In a real application, you might want to show the token in a modal
      // or send it via email instead of showing a toast
    },
    onError: (error: Error & { response?: { data?: { error?: string } } }) => {
      const message = error.response?.data?.error || 'Failed to generate reset token';
      toast.error(message);
    },
  });
};

// Combined hook for all password management operations
export const usePasswordManagement = () => {
  const resetPassword = useResetUserPassword();
  const forceChange = useForcePasswordChange();
  const generateToken = useGeneratePasswordResetToken();

  return {
    resetPassword,
    forceChange,
    generateToken,
    isLoading: resetPassword.isPending || forceChange.isPending || generateToken.isPending,
  };
};

// Hook for password validation
export const usePasswordValidation = () => {
  const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  };

  const generateSecurePassword = (length: number = 12): string => {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    const allChars = uppercase + lowercase + numbers + symbols;

    let password = '';

    // Ensure at least one character from each category
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];

    // Fill the rest randomly
    for (let i = 4; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // Shuffle the password
    return password
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('');
  };

  return {
    validatePassword,
    generateSecurePassword,
  };
};

// Hook for checking password management permissions
export const usePasswordManagementPermissions = () => {
  const { user } = useAuth();

  return {
    canResetPasswords: user?.role === 'ADMIN',
    canForcePasswordChange: user?.role === 'ADMIN',
    canGenerateResetTokens: user?.role === 'ADMIN',
    canManageAllPasswords: user?.role === 'ADMIN',
    currentUserRole: user?.role,
    currentUserId: user?.id,
  };
};

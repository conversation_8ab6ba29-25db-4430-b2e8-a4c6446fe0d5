import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { transactionsAPI } from '../lib/api';
import { ITransactionCreateData, ITransactionUpdateData, TransactionSummary } from '@alom-rentals/shared';

export interface UseTransactionsParams {
  page?: number;
  limit?: number;
  propertyId?: string;
  type?: string;
}

export const useTransactions = (params?: UseTransactionsParams) => {
  const queryClient = useQueryClient();

  const {
    data: transactionsData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['transactions', params],
    queryFn: async () => {
      const response = await transactionsAPI.getAll(params);
      return response.data;
    },
  });

  const createTransactionMutation = useMutation({
    mutationFn: (data: ITransactionCreateData) => transactionsAPI.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
      queryClient.invalidateQueries({ queryKey: ['transaction-summary'] });
    },
  });

  const updateTransactionMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: ITransactionUpdateData }) =>
      transactionsAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
      queryClient.invalidateQueries({ queryKey: ['transaction-summary'] });
    },
  });

  const deleteTransactionMutation = useMutation({
    mutationFn: (id: string) => transactionsAPI.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['transactions'] });
      queryClient.invalidateQueries({ queryKey: ['transaction-summary'] });
    },
  });

  return {
    transactions: transactionsData?.transactions || [],
    pagination: transactionsData?.pagination,
    isLoading,
    error,
    refetch,
    createTransaction: createTransactionMutation.mutate,
    updateTransaction: updateTransactionMutation.mutate,
    deleteTransaction: deleteTransactionMutation.mutate,
    isCreating: createTransactionMutation.isPending,
    isUpdating: updateTransactionMutation.isPending,
    isDeleting: deleteTransactionMutation.isPending,
  };
};

export const useTransactionSummary = (propertyId: string) => {
  return useQuery<TransactionSummary>({
    queryKey: ['transaction-summary', propertyId],
    queryFn: async () => {
      const response = await transactionsAPI.getSummary(propertyId);
      return response.data;
    },
    enabled: !!propertyId,
  });
};
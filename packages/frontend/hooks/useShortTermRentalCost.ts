import { useQuery } from '@tanstack/react-query';
import { shortTermRentalsAPI } from '../lib/api';
import { CostCalculationResponse } from '@alom-rentals/shared';

export const useShortTermRentalCost = (propertyId: string) => {
  console.log('use Short Rental Cost', propertyId);
  return useQuery<CostCalculationResponse>({
    queryKey: ['shortTermRentalCost', propertyId],
    queryFn: async () => {
      const response = await shortTermRentalsAPI.calculateCost(propertyId);
      return response.data;
    },
    enabled: !!propertyId,
  });
};

export default useShortTermRentalCost;

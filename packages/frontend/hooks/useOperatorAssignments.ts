import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { api } from '../lib/api';
import { OperatorAssignmentCreateInput } from '@alom-rentals/shared';

// Define the bulk type locally since it's not exported from shared
interface BulkOperatorAssignmentCreateInput {
  operatorId: string;
  customerIds: string[];
  permissions: {
    canCreateProperties: boolean;
    canEditProperties: boolean;
    canDeleteProperties: boolean;
    canManageRentals: boolean;
    canViewFinancials: boolean;
  };
}

// Hook to get assigned customers for an operator
export const useAssignedCustomers = (operatorId?: string) => {
  return useQuery({
    queryKey: ['operator-assignments', 'assigned-customers', operatorId],
    queryFn: async () => {
      if (!operatorId) return { data: [] };
      const response = await api.get(`/operator-assignments/operator/${operatorId}`);
      return response.data;
    },
    enabled: !!operatorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to get operators with their assignments (admin only)
export const useOperatorsWithAssignments = () => {
  return useQuery({
    queryKey: ['operator-assignments', 'operators'],
    queryFn: async () => {
      const response = await api.get('/operator-assignments/operators');
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to get available customers for assignment (admin only)
export const useAvailableCustomers = () => {
  return useQuery({
    queryKey: ['operator-assignments', 'customers'],
    queryFn: async () => {
      const response = await api.get('/operator-assignments/customers');
      return response.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to create a new operator assignment
export const useCreateOperatorAssignment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: OperatorAssignmentCreateInput) => {
      const response = await api.post('/operator-assignments', data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['operator-assignments'] });
      toast.success('Assignment created successfully');
    },
    onError: (error: Error & { response?: { data?: { error?: string } } }) => {
      const message = error.response?.data?.error || 'Failed to create assignment';
      toast.error(message);
    },
  });
};

// Hook to create multiple operator assignments
export const useBulkCreateOperatorAssignments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: BulkOperatorAssignmentCreateInput) => {
      const response = await api.post('/operator-assignments/bulk', data);
      return response.data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['operator-assignments'] });
      if (data.success > 0) {
        toast.success(`${data.success} assignments created successfully`);
      }
      if (data.errors > 0) {
        toast.error(`${data.errors} assignments failed to create`);
      }
    },
    onError: (error: Error & { response?: { data?: { error?: string } } }) => {
      const message = error.response?.data?.error || 'Failed to create bulk assignments';
      toast.error(message);
    },
  });
};

// Hook to delete an operator assignment
export const useDeleteOperatorAssignment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (assignmentId: string) => {
      const response = await api.delete(`/operator-assignments/${assignmentId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['operator-assignments'] });
      toast.success('Assignment deleted successfully');
    },
    onError: (error: Error & { response?: { data?: { error?: string } } }) => {
      const message = error.response?.data?.error || 'Failed to delete assignment';
      toast.error(message);
    },
  });
};

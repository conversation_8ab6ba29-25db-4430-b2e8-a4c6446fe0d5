import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { propertiesAPI } from '../lib/api';
import { IProperty, IPropertyCreateData, IPropertyUpdateData } from '@alom-rentals/shared';
import { getErrorMessage } from '../lib/errorUtils';
import { useCurrentCustomerId } from './useCustomerUtils';
import { AxiosResponse } from 'axios';

export function useProperties() {
  const queryClient = useQueryClient();
  const currentCustomerId = useCurrentCustomerId();

  // Get all properties
  const {
    data: properties = [],
    isLoading,
    error,
  } = useQuery({
    queryKey: ['properties', currentCustomerId],
    queryFn: async () => {
      const response = await propertiesAPI.getAll();
      return response.data as IProperty[];
    },
  });

  // Create property mutation
  const createPropertyMutation = useMutation<AxiosResponse<IProperty>, Error, IPropertyCreateData>({
    mutationFn: (data: IPropertyCreateData) => propertiesAPI.create(data, currentCustomerId || undefined),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });

  // Update property mutation
  const updatePropertyMutation = useMutation<AxiosResponse<IProperty>, Error, { id: string; data: IPropertyUpdateData }>({
    mutationFn: ({ id, data }: { id: string; data: IPropertyUpdateData }) => propertiesAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });

  // Delete property mutation
  const deletePropertyMutation = useMutation<AxiosResponse<{ message: string }>, Error, string>({
    mutationFn: (id: string) => propertiesAPI.delete(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });

  // Bulk delete properties mutation
  const bulkDeletePropertiesMutation = useMutation<AxiosResponse<{ message: string; deletedCount: number; deletedIds: string[] }>, Error, string[]>({
    mutationFn: (propertyIds: string[]) => propertiesAPI.bulkDelete(propertyIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['properties'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard'] });
    },
  });

  const createProperty = async (data: IPropertyCreateData): Promise<{ data: IProperty | null; error: { message: string } | null }> => {
    try {
      const result = await createPropertyMutation.mutateAsync(data);
      return { data: result.data, error: null };
    } catch (error: unknown) {
      return {
        data: null,
        error: {
          message: getErrorMessage(error, 'Failed to create property'),
        },
      };
    }
  };

  const updateProperty = async (id: string, data: IPropertyUpdateData): Promise<{ data: IProperty | null; error: { message: string } | null }> => {
    try {
      const result = await updatePropertyMutation.mutateAsync({ id, data });
      return { data: result.data, error: null };
    } catch (error: unknown) {
      return {
        data: null,
        error: {
          message: getErrorMessage(error, 'Failed to update property'),
        },
      };
    }
  };

  const deleteProperty = async (id: string): Promise<{ success: boolean; error: { message: string } | null }> => {
    try {
      await deletePropertyMutation.mutateAsync(id);
      return { success: true, error: null };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message: getErrorMessage(error, 'Failed to delete property'),
        },
      };
    }
  };

  const bulkDeleteProperties = async (propertyIds: string[]): Promise<{ success: boolean; error: { message: string } | null; deletedCount?: number }> => {
    try {
      const response = await bulkDeletePropertiesMutation.mutateAsync(propertyIds);
      return { success: true, error: null, deletedCount: response.data.deletedCount };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message: getErrorMessage(error, 'Failed to delete properties'),
        },
      };
    }
  };

  return {
    properties,
    isLoading,
    error,
    createProperty,
    updateProperty,
    deleteProperty,
    bulkDeleteProperties,
    isCreating: createPropertyMutation.isPending,
    isUpdating: updatePropertyMutation.isPending,
    isDeleting: deletePropertyMutation.isPending,
    isBulkDeleting: bulkDeletePropertiesMutation.isPending,
  };
}

export function useProperty(id: string) {
  return useQuery<IProperty>({
    queryKey: ['property', id],
    queryFn: async () => {
      const response = await propertiesAPI.getById(id);
      return response.data as IProperty;
    },
    enabled: !!id,
  });
}

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { toast } from 'react-hot-toast';
import {
  getAllUsers,
  getUserById,
  changeUserRole,
  bulkChangeUserRoles,
  getRoleChangeAuditLogs,
  getAdminStats,
  validateRoleChange,
  validateBulkRoleChange,
} from '../api/admin';
import { RoleChangeInput, BulkRoleChangeInput, UserRole } from '@alom-rentals/shared';
import { useAuth } from './useAuth';

// Hook to get all users (admin only)
export const useUsers = () => {
  return useQuery({
    queryKey: ['admin', 'users'],
    queryFn: getAllUsers,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to get user by ID (admin only)
export const useUser = (userId: string) => {
  return useQuery({
    queryKey: ['admin', 'users', userId],
    queryFn: () => getUserById(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook to change user role
export const useChangeUserRole = () => {
  const queryClient = useQueryClient();
  const { user: currentUser } = useAuth();

  return useMutation({
    mutationFn: async ({ userId, roleData }: { userId: string; roleData: RoleChangeInput }) => {
      // Validate the role change
      const validation = validateRoleChange(currentUser?.role as UserRole, userId, currentUser?.id || '', roleData.role);

      if (!validation.valid) {
        throw new Error(validation.error);
      }

      return changeUserRole(userId, roleData);
    },
    onSuccess: (data) => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });

      // Update the specific user in cache
      queryClient.setQueryData(['admin', 'users', data.user.id], data.user);

      // Show success message
      toast.success(data.message);
    },
    onError: (error: Error & { response?: { data?: { error?: string } }; message?: string }) => {
      const message = error.response?.data?.error || error.message || 'Failed to change user role';
      toast.error(message);
    },
  });
};

// Hook for bulk role changes
export const useBulkChangeUserRoles = () => {
  const queryClient = useQueryClient();
  const { user: currentUser } = useAuth();

  return useMutation({
    mutationFn: async (bulkData: BulkRoleChangeInput) => {
      // Validate the bulk role change
      const validation = validateBulkRoleChange(currentUser?.role as UserRole, bulkData.userIds, currentUser?.id || '', bulkData.role);

      if (!validation.valid) {
        throw new Error(validation.error);
      }

      return bulkChangeUserRoles(bulkData);
    },
    onSuccess: (data) => {
      // Invalidate and refetch users list
      queryClient.invalidateQueries({ queryKey: ['admin', 'users'] });

      // Show success message
      toast.success(data.message);

      // Show additional info if some users failed
      if (data.failedUsers && data.failedUsers.length > 0) {
        toast.error(`Failed to update ${data.failedUsers.length} users`);
      }
    },
    onError: (error: Error & { response?: { data?: { error?: string } }; message?: string }) => {
      const message = error.response?.data?.error || error.message || 'Failed to perform bulk role change';
      toast.error(message);
    },
  });
};

// Hook to get role change audit logs
export const useRoleChangeAuditLogs = (page: number = 1, limit: number = 50) => {
  return useQuery({
    queryKey: ['admin', 'audit', 'role-changes', page, limit],
    queryFn: () => getRoleChangeAuditLogs(page, limit),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Hook to get admin statistics
export const useAdminStats = () => {
  return useQuery({
    queryKey: ['admin', 'stats'],
    queryFn: getAdminStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};

// Hook to check if current user can manage roles
export const useRolePermissions = () => {
  const { user } = useAuth();

  return {
    canManageRoles: user?.role === 'ADMIN',
    canViewAuditLogs: user?.role === 'ADMIN',
    canViewAdminStats: user?.role === 'ADMIN',
    canAccessAdminDashboard: user?.role === 'ADMIN',
    currentUserRole: user?.role as UserRole,
    currentUserId: user?.id,
  };
};

// Hook for role-based UI logic
export const useRoleBasedUI = () => {
  const { user } = useAuth();
  const currentRole = user?.role as UserRole;

  const isAdmin = currentRole === 'ADMIN';
  const isOperator = currentRole === 'OPERATOR';
  const isCustomer = currentRole === 'CUSTOMER';

  const canManageUsers = isAdmin;
  const canManageProperties = isAdmin || isOperator;
  const canViewAllData = isAdmin;
  const hasReadOnlyAccess = isCustomer;

  return {
    currentRole,
    isAdmin,
    isOperator,
    isCustomer,
    canManageUsers,
    canManageProperties,
    canViewAllData,
    hasReadOnlyAccess,
  };
};

// Hook for user selection in admin interfaces
export const useUserSelection = () => {
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);

  const selectUser = (userId: string) => {
    setSelectedUsers((prev) => [...prev, userId]);
  };

  const deselectUser = (userId: string) => {
    setSelectedUsers((prev) => prev.filter((id) => id !== userId));
  };

  const toggleUser = (userId: string) => {
    setSelectedUsers((prev) => (prev.includes(userId) ? prev.filter((id) => id !== userId) : [...prev, userId]));
  };

  const selectAll = (userIds: string[]) => {
    setSelectedUsers(userIds);
  };

  const deselectAll = () => {
    setSelectedUsers([]);
  };

  const isSelected = (userId: string) => {
    return selectedUsers.includes(userId);
  };

  return {
    selectedUsers,
    selectUser,
    deselectUser,
    toggleUser,
    selectAll,
    deselectAll,
    isSelected,
    selectedCount: selectedUsers.length,
    hasSelection: selectedUsers.length > 0,
  };
};

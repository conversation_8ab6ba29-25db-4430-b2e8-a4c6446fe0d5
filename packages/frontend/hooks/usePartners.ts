import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { partnersAPI } from '../lib/api';
import { IPartner, IPartnerCreateData, IPartnerUpdateData } from '@alom-rentals/shared';
import { getErrorMessage } from '../lib/errorUtils';
import { AxiosResponse } from 'axios';

export function usePartners() {
  const queryClient = useQueryClient();

  // Get all partners
  const {
    data: partners = [],
    isLoading,
    error,
  } = useQuery<IPartner[]>({
    queryKey: ['partners'],
    queryFn: async () => {
      const response = await partnersAPI.getAll();
      return response.data as IPartner[];
    },
  });

  // Get partner by ID
  const useGetPartner = (id: string) => {
    return useQuery<IPartner>({
      queryKey: ['partners', id],
      queryFn: async () => {
        const response = await partnersAPI.getById(id);
        return response.data as IPartner;
      },
      enabled: !!id,
    });
  };

  // Create partner mutation
  const createPartnerMutation = useMutation<AxiosResponse<IPartner>, Error, IPartnerCreateData>({
    mutationFn: partnersAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['partners'] });
    },
  });

  // Update partner mutation
  const updatePartnerMutation = useMutation<AxiosResponse<IPartner>, Error, { id: string; data: IPartnerUpdateData }>({
    mutationFn: ({ id, data }: { id: string; data: IPartnerUpdateData }) => partnersAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['partners'] });
    },
  });

  // Delete partner mutation
  const deletePartnerMutation = useMutation<AxiosResponse<void>, Error, string>({
    mutationFn: partnersAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['partners'] });
    },
  });

  // Helper functions
  const createPartner = async (data: IPartnerCreateData): Promise<{ data: IPartner | null; error: { message: string } | null }> => {
    try {
      const result = await createPartnerMutation.mutateAsync(data);
      return { data: result.data, error: null };
    } catch (error: unknown) {
      const errorMessage = getErrorMessage(error, 'Failed to create partner');
      return { data: null, error: { message: errorMessage } };
    }
  };

  const updatePartner = async (id: string, data: IPartnerUpdateData): Promise<{ data: IPartner | null; error: { message: string } | null }> => {
    try {
      const result = await updatePartnerMutation.mutateAsync({ id, data });
      return { data: result.data, error: null };
    } catch (error: unknown) {
      const errorMessage = getErrorMessage(error, 'Failed to update partner');
      return { data: null, error: { message: errorMessage } };
    }
  };

  const deletePartner = async (id: string): Promise<{ data: null; error: { message: string } | null }> => {
    try {
      await deletePartnerMutation.mutateAsync(id);
      return { data: null, error: null };
    } catch (error: unknown) {
      const errorMessage = getErrorMessage(error, 'Failed to delete partner');
      return { data: null, error: { message: errorMessage } };
    }
  };

  // Bulk delete partners mutation
  const bulkDeletePartnersMutation = useMutation<AxiosResponse<{ message: string; deletedCount: number; deletedIds: string[] }>, Error, string[]>({
    mutationFn: (partnerIds: string[]) => partnersAPI.bulkDelete(partnerIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['partners'] });
    },
  });

  const bulkDeletePartners = async (partnerIds: string[]): Promise<{ success: boolean; error: { message: string } | null; deletedCount?: number }> => {
    try {
      const response = await bulkDeletePartnersMutation.mutateAsync(partnerIds);
      return { success: true, error: null, deletedCount: response.data.deletedCount };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message: getErrorMessage(error, 'Failed to delete partners'),
        },
      };
    }
  };

  return {
    // Data
    partners,
    isLoading,
    error,

    // Queries
    useGetPartner,

    // Mutations
    createPartner,
    updatePartner,
    deletePartner,
    bulkDeletePartners,

    // Loading states
    isCreating: createPartnerMutation.isPending,
    isUpdating: updatePartnerMutation.isPending,
    isDeleting: deletePartnerMutation.isPending,
    isBulkDeleting: bulkDeletePartnersMutation.isPending,
  };
}

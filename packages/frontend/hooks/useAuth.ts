import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { authAPI } from '../lib/api';
import { AuthResponse, IUserPublic } from '@alom-rentals/shared';
import { getErrorMessage } from '../lib/errorUtils';
import { AxiosResponse } from 'axios';

// User interface moved to shared types

export function useAuth() {
  const [token, setToken] = useState<string | null>(localStorage.getItem('token'));
  const queryClient = useQueryClient();

  // Get current user query
  const { data: user, isLoading } = useQuery<IUserPublic>({
    queryKey: ['user'],
    queryFn: async () => {
      const response = await authAPI.getCurrentUser();
      return response.data as IUserPublic;
    },
    enabled: !!token,
    retry: false,
  });

  // Login mutation
  const loginMutation = useMutation<AxiosResponse<AuthResponse>, Error, { email: string; password: string }>({
    mutationFn: ({ email, password }: { email: string; password: string }) => authAPI.login({ email, password }),
    onSuccess: (response) => {
      const { token, user } = response.data;
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));
      setToken(token);
      queryClient.setQueryData(['user'], user);
    },
  });

  // Register mutation
  const registerMutation = useMutation<AxiosResponse<AuthResponse>, Error, { email: string; password: string; fullName: string }>({
    mutationFn: ({ email, password, fullName }: { email: string; password: string; fullName: string }) => authAPI.register({ email, password, fullName }),
    onSuccess: (response) => {
      const { token, user } = response.data;
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify(user));
      setToken(token);
      queryClient.setQueryData(['user'], user);
    },
  });

  const signIn = async (email: string, password: string): Promise<{ error: { message: string } | null }> => {
    try {
      await loginMutation.mutateAsync({ email, password });
      return { error: null };
    } catch (error: unknown) {
      return {
        error: { message: getErrorMessage(error, 'Login failed') },
      };
    }
  };

  const signUp = async (email: string, password: string, fullName: string): Promise<{ data: AuthResponse | null; error: { message: string } | null }> => {
    try {
      const result = await registerMutation.mutateAsync({
        email,
        password,
        fullName,
      });
      return { data: result.data, error: null };
    } catch (error: unknown) {
      return {
        data: null,
        error: {
          message: getErrorMessage(error, 'Registration failed'),
        },
      };
    }
  };

  const signOut = async () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setToken(null);
    queryClient.clear();
    return { error: null };
  };

  return {
    user,
    loading: isLoading,
    signIn,
    signUp,
    signOut,
  };
}

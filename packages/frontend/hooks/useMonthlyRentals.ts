import { useQuery } from '@tanstack/react-query';
import { dashboardAPI } from '../lib/api';
import { MonthlyRentalData } from '@alom-rentals/shared';

export function useMonthlyRentals() {
  const { data, isLoading, error } = useQuery<{ data: MonthlyRentalData[] }>({
    queryKey: ['dashboard', 'monthly-rentals'],
    queryFn: async () => {
      const response = await dashboardAPI.getMonthlyRentals();
      return response.data;
    },
  });

  return {
    monthlyData: data?.data || [],
    isLoading,
    error,
  };
}

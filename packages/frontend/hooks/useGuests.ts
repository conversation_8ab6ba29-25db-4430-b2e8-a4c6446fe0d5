import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { guestsAPI } from '../lib/api';
import { IGuest, IGuestCreateData, IGuestUpdateData } from '@alom-rentals/shared';
import { getErrorMessage } from '../lib/errorUtils';
import { AxiosResponse } from 'axios';

export type Guest = IGuest;

export function useGuests() {
  const queryClient = useQueryClient();

  // Get all guests
  const { data: guests = [], isLoading } = useQuery<IGuest[]>({
    queryKey: ['guests'],
    queryFn: async () => {
      const response = await guestsAPI.getAll();
      return response.data as IGuest[];
    },
  });

  // Create guest mutation
  const createGuestMutation = useMutation<AxiosResponse<IGuest>, Error, IGuestCreateData>({
    mutationFn: guestsAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['guests'] });
    },
  });

  const createGuest = async (guestData: IGuestCreateData): Promise<{ data: IGuest | null; error: { message: string } | null }> => {
    try {
      const response = await createGuestMutation.mutateAsync(guestData);
      return { data: response.data, error: null };
    } catch (error: unknown) {
      const errorMessage = getErrorMessage(error, 'Failed to create guest');
      console.error('Error creating guest:', error);
      return { data: null, error: { message: errorMessage } };
    }
  };

  // Update guest mutation
  const updateGuestMutation = useMutation<AxiosResponse<IGuest>, Error, { id: string; data: IGuestUpdateData }>({
    mutationFn: ({ id, data }: { id: string; data: IGuestUpdateData }) => guestsAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['guests'] });
    },
  });

  const updateGuest = async (id: string, guestData: IGuestUpdateData): Promise<{ data: IGuest | null; error: { message: string } | null }> => {
    try {
      const response = await updateGuestMutation.mutateAsync({
        id,
        data: guestData,
      });
      return { data: response.data, error: null };
    } catch (error: unknown) {
      const errorMessage = getErrorMessage(error, 'Failed to update guest');
      console.error('Error updating guest:', error);
      return { data: null, error: { message: errorMessage } };
    }
  };

  // Delete guest mutation
  const deleteGuestMutation = useMutation<AxiosResponse<void>, Error, string>({
    mutationFn: guestsAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['guests'] });
    },
  });

  const deleteGuest = async (id: string): Promise<{ error: { message: string } | null }> => {
    try {
      await deleteGuestMutation.mutateAsync(id);
      return { error: null };
    } catch (error: unknown) {
      const errorMessage = getErrorMessage(error, 'Failed to delete guest');
      console.error('Error deleting guest:', error);
      return { error: { message: errorMessage } };
    }
  };

  // Bulk delete guests mutation
  const bulkDeleteGuestsMutation = useMutation<AxiosResponse<{ message: string; deletedCount: number; deletedIds: string[] }>, Error, string[]>({
    mutationFn: (guestIds: string[]) => guestsAPI.bulkDelete(guestIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['guests'] });
    },
  });

  const bulkDeleteGuests = async (guestIds: string[]): Promise<{ success: boolean; error: { message: string } | null; deletedCount?: number }> => {
    try {
      const response = await bulkDeleteGuestsMutation.mutateAsync(guestIds);
      return { success: true, error: null, deletedCount: response.data.deletedCount };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message: getErrorMessage(error, 'Failed to delete guests'),
        },
      };
    }
  };

  const searchGuests = async (query: string): Promise<IGuest[]> => {
    try {
      if (query.length < 2) {
        return [];
      }
      const response = await guestsAPI.search(query);
      return response.data as IGuest[];
    } catch (error) {
      console.error('Error searching guests:', error);
      return [];
    }
  };

  return {
    guests,
    isLoading,
    isCreating: createGuestMutation.isPending,
    isUpdating: updateGuestMutation.isPending,
    isDeleting: deleteGuestMutation.isPending,
    isBulkDeleting: bulkDeleteGuestsMutation.isPending,
    createGuest,
    updateGuest,
    deleteGuest,
    bulkDeleteGuests,
    searchGuests,
    refetch: () => queryClient.invalidateQueries({ queryKey: ['guests'] }),
  };
}

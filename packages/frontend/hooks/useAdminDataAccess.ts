import { useQuery } from '@tanstack/react-query';
import { getAllPropertiesForAdmin, getAllGuestsForAdmin, getAllPartnersForAdmin, getAllRentalsForAdmin, getAllTransactionsForAdmin } from '../api/admin';
import { useAuth } from './useAuth';

// Hook for admin to access all properties with optional user filtering
export const useAdminProperties = (userId?: string, options?: { enabled?: boolean }) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['admin-properties', userId],
    queryFn: () => getAllPropertiesForAdmin(userId),
    enabled: user?.role === 'ADMIN' && options?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for admin to access all guests with optional user filtering
export const useAdminGuests = (userId?: string, options?: { enabled?: boolean }) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['admin-guests', userId],
    queryFn: () => getAllGuestsForAdmin(userId),
    enabled: user?.role === 'ADMIN' && options?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for admin to access all partners with optional user filtering
export const useAdminPartners = (userId?: string, options?: { enabled?: boolean }) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['admin-partners', userId],
    queryFn: () => getAllPartnersForAdmin(userId),
    enabled: user?.role === 'ADMIN' && options?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for admin to access all rentals with optional user filtering
export const useAdminRentals = (userId?: string, options?: { enabled?: boolean }) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['admin-rentals', userId],
    queryFn: () => getAllRentalsForAdmin(userId),
    enabled: user?.role === 'ADMIN' && options?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for admin to access all transactions with optional user filtering and pagination
export const useAdminTransactions = (
  userId?: string,
  options?: {
    page?: number;
    limit?: number;
    type?: string;
    propertyId?: string;
    enabled?: boolean;
  }
) => {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['admin-transactions', userId, options?.page, options?.limit, options?.type, options?.propertyId],
    queryFn: () =>
      getAllTransactionsForAdmin(userId, {
        page: options?.page,
        limit: options?.limit,
        type: options?.type,
        propertyId: options?.propertyId,
      }),
    enabled: user?.role === 'ADMIN' && options?.enabled !== false,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Combined hook for all admin data access
export const useAdminAllData = (userId?: string, options?: { enabled?: boolean }) => {
  const properties = useAdminProperties(userId, options);
  const guests = useAdminGuests(userId, options);
  const partners = useAdminPartners(userId, options);
  const rentals = useAdminRentals(userId, options);
  const transactions = useAdminTransactions(userId, options);

  return {
    properties,
    guests,
    partners,
    rentals,
    transactions,
    isLoading: properties.isLoading || guests.isLoading || partners.isLoading || rentals.isLoading || transactions.isLoading,
    isError: properties.isError || guests.isError || partners.isError || rentals.isError || transactions.isError,
    error: properties.error || guests.error || partners.error || rentals.error || transactions.error,
    refetchAll: () => {
      properties.refetch();
      guests.refetch();
      partners.refetch();
      rentals.refetch();
      transactions.refetch();
    },
  };
};

// Hook for data access with automatic role-based switching
export const useRoleBasedDataAccess = (userId?: string) => {
  const { user } = useAuth();

  // Always call the hook, but conditionally enable it
  const isAdmin = user?.role === 'ADMIN';
  const adminData = useAdminAllData(userId, { enabled: isAdmin });

  // For non-admin users, return empty/disabled state
  if (!isAdmin) {
    return {
      properties: { data: [], isLoading: false, isError: false, error: null, refetch: () => {} },
      guests: { data: [], isLoading: false, isError: false, error: null, refetch: () => {} },
      partners: { data: [], isLoading: false, isError: false, error: null, refetch: () => {} },
      rentals: { data: [], isLoading: false, isError: false, error: null, refetch: () => {} },
      transactions: { data: [], isLoading: false, isError: false, error: null, refetch: () => {} },
      isLoading: false,
      isError: false,
      error: null,
      refetchAll: () => {},
    };
  }

  return adminData;
};

// Hook for checking if user can access cross-user data
export const useCanAccessCrossUserData = () => {
  const { user } = useAuth();
  return user?.role === 'ADMIN';
};

// Hook for getting data access permissions
export const useDataAccessPermissions = () => {
  const { user } = useAuth();

  return {
    canViewAllUsers: user?.role === 'ADMIN',
    canViewAllProperties: user?.role === 'ADMIN',
    canViewAllGuests: user?.role === 'ADMIN',
    canViewAllPartners: user?.role === 'ADMIN',
    canViewAllRentals: user?.role === 'ADMIN',
    canViewAllTransactions: user?.role === 'ADMIN',
    canSwitchUserContext: user?.role === 'ADMIN',
    canPerformCrossUserActions: user?.role === 'ADMIN',
    currentUserRole: user?.role,
    currentUserId: user?.id,
  };
};

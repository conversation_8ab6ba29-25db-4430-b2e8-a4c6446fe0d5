import { useQuery } from '@tanstack/react-query';
import { dashboardAPI } from '../lib/api';
import { DashboardStats } from '@alom-rentals/shared';

export function useDashboard() {
  const {
    data: stats,
    isLoading,
    error,
  } = useQuery<DashboardStats>({
    queryKey: ['dashboard', 'stats'],
    queryFn: async () => {
      const response = await dashboardAPI.getStats();
      return response.data;
    },
  });

  return {
    stats: stats || {
      totalProperties: 0,
      totalMonthlyIncome: 0,
      totalPendingDebts: 0,
      netPropertyIncome: 0,
    },
    isLoading,
    error,
  };
}

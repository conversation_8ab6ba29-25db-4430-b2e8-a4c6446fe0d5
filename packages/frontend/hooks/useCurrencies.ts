import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { currenciesAPI } from '../lib/api';
import { ICurrency, ICurrencyCreateData, ICurrencyUpdateData } from '@alom-rentals/shared';
import { getErrorMessage } from '../lib/errorUtils';
import { AxiosResponse } from 'axios';

export const useCurrencies = () => {
  const queryClient = useQueryClient();

  // Get all currencies
  const {
    data: currencies = [],
    isLoading: loading,
    error,
  } = useQuery<ICurrency[]>({
    queryKey: ['currencies'],
    queryFn: async () => {
      const response = await currenciesAPI.getAll();
      return response.data as ICurrency[];
    },
  });

  // Create currency mutation
  const createCurrencyMutation = useMutation<AxiosResponse<ICurrency>, Error, ICurrencyCreateData>({
    mutationFn: currenciesAPI.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['currencies'] });
    },
  });

  const createCurrency = async (currencyData: ICurrencyCreateData): Promise<ICurrency> => {
    try {
      const response = await createCurrencyMutation.mutateAsync(currencyData);
      return response.data;
    } catch (err: unknown) {
      const errorMessage = getErrorMessage(err, 'Failed to create currency');
      throw new Error(errorMessage);
    }
  };

  // Update currency mutation
  const updateCurrencyMutation = useMutation<AxiosResponse<ICurrency>, Error, { id: string; data: ICurrencyUpdateData }>({
    mutationFn: ({ id, data }: { id: string; data: ICurrencyUpdateData }) => currenciesAPI.update(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['currencies'] });
    },
  });

  const updateCurrency = async (currencyId: string, currencyData: ICurrencyUpdateData): Promise<ICurrency> => {
    try {
      const response = await updateCurrencyMutation.mutateAsync({
        id: currencyId,
        data: currencyData,
      });
      return response.data;
    } catch (err: unknown) {
      const errorMessage = getErrorMessage(err, 'Failed to update currency');
      throw new Error(errorMessage);
    }
  };

  // Delete currency mutation
  const deleteCurrencyMutation = useMutation<AxiosResponse<void>, Error, string>({
    mutationFn: currenciesAPI.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['currencies'] });
    },
  });

  const deleteCurrency = async (currencyId: string): Promise<void> => {
    try {
      await deleteCurrencyMutation.mutateAsync(currencyId);
    } catch (err: unknown) {
      const errorMessage = getErrorMessage(err, 'Failed to delete currency');
      throw new Error(errorMessage);
    }
  };

  // Bulk delete currencies mutation
  const bulkDeleteCurrenciesMutation = useMutation<AxiosResponse<{ message: string; deletedCount: number; deletedIds: string[] }>, Error, string[]>({
    mutationFn: (currencyIds: string[]) => currenciesAPI.bulkDelete(currencyIds),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['currencies'] });
    },
  });

  const bulkDeleteCurrencies = async (currencyIds: string[]): Promise<{ success: boolean; error: { message: string } | null; deletedCount?: number }> => {
    try {
      const response = await bulkDeleteCurrenciesMutation.mutateAsync(currencyIds);
      return { success: true, error: null, deletedCount: response.data.deletedCount };
    } catch (error: unknown) {
      return {
        success: false,
        error: {
          message: getErrorMessage(error, 'Failed to delete currencies'),
        },
      };
    }
  };

  const refetch = () => {
    queryClient.invalidateQueries({ queryKey: ['currencies'] });
  };

  return {
    currencies,
    loading,
    error,
    createCurrency,
    updateCurrency,
    deleteCurrency,
    bulkDeleteCurrencies,
    refetch,
    isCreating: createCurrencyMutation.isPending,
    isUpdating: updateCurrencyMutation.isPending,
    isDeleting: deleteCurrencyMutation.isPending,
    isBulkDeleting: bulkDeleteCurrenciesMutation.isPending,
  };
};

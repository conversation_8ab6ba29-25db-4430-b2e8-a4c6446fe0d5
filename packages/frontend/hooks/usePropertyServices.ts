import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { propertyServicesAPI } from '../lib/api';
import {
  IPropertyService,
  IPropertyServiceCreateData,
  IPropertyServiceUpdateData,
  PropertyServicesResponse,
  PropertyServiceSummary,
} from '@alom-rentals/shared';
import { getErrorMessage } from '../lib/errorUtils';
import { AxiosResponse } from 'axios';

export const usePropertyServices = (propertyId: string) => {
  const queryClient = useQueryClient();

  // Get all services for property
  const {
    data: services = [],
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ['property-services', propertyId],
    queryFn: async () => {
      const response = await propertyServicesAPI.getAll(propertyId);
      const data = response.data as PropertyServicesResponse;
      return data.services;
    },
    enabled: !!propertyId,
  });

  // Get service cost summary
  const { data: summary = null } = useQuery<PropertyServiceSummary>({
    queryKey: ['property-services-summary', propertyId],
    queryFn: async () => {
      const response = await propertyServicesAPI.getSummary(propertyId);
      return response.data as PropertyServiceSummary;
    },
    enabled: !!propertyId,
  });

  // Create service mutation
  const createServiceMutation = useMutation<AxiosResponse<IPropertyService>, Error, IPropertyServiceCreateData>({
    mutationFn: (serviceData: IPropertyServiceCreateData) => propertyServicesAPI.create(propertyId, serviceData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['property-services', propertyId],
      });
      queryClient.invalidateQueries({
        queryKey: ['property-services-summary', propertyId],
      });
    },
  });

  const createService = async (serviceData: IPropertyServiceCreateData): Promise<IPropertyService> => {
    try {
      const response = await createServiceMutation.mutateAsync(serviceData);
      return response.data;
    } catch (err: unknown) {
      const errorMessage = getErrorMessage(err, 'Failed to create service');
      throw new Error(errorMessage);
    }
  };

  // Update service mutation
  const updateServiceMutation = useMutation<AxiosResponse<IPropertyService>, Error, { serviceId: string; serviceData: IPropertyServiceUpdateData }>({
    mutationFn: ({ serviceId, serviceData }: { serviceId: string; serviceData: IPropertyServiceUpdateData }) =>
      propertyServicesAPI.update(propertyId, serviceId, serviceData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['property-services', propertyId],
      });
      queryClient.invalidateQueries({
        queryKey: ['property-services-summary', propertyId],
      });
    },
  });

  const updateService = async (serviceId: string, serviceData: IPropertyServiceUpdateData): Promise<IPropertyService> => {
    try {
      const response = await updateServiceMutation.mutateAsync({
        serviceId,
        serviceData,
      });
      return response.data;
    } catch (err: unknown) {
      const errorMessage = getErrorMessage(err, 'Failed to update service');
      throw new Error(errorMessage);
    }
  };

  // Delete service mutation
  const deleteServiceMutation = useMutation<AxiosResponse<void>, Error, string>({
    mutationFn: (serviceId: string) => propertyServicesAPI.delete(propertyId, serviceId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['property-services', propertyId],
      });
      queryClient.invalidateQueries({
        queryKey: ['property-services-summary', propertyId],
      });
    },
  });

  const deleteService = async (serviceId: string): Promise<void> => {
    try {
      await deleteServiceMutation.mutateAsync(serviceId);
    } catch (err: unknown) {
      const errorMessage = getErrorMessage(err, 'Failed to delete service');
      throw new Error(errorMessage);
    }
  };

  return {
    services,
    summary,
    loading,
    error,
    createService,
    updateService,
    deleteService,
    refetch: () => {
      queryClient.invalidateQueries({
        queryKey: ['property-services', propertyId],
      });
      queryClient.invalidateQueries({
        queryKey: ['property-services-summary', propertyId],
      });
    },
    isCreating: createServiceMutation.isPending,
    isUpdating: updateServiceMutation.isPending,
    isDeleting: deleteServiceMutation.isPending,
  };
};

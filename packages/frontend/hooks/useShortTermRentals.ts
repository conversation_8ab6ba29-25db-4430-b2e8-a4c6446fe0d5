import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { shortTermRentalsAPI } from '../lib/api';
import { IShortTermRental, IShortTermRentalCreateData, IShortTermRentalUpdateData, ShortTermRentalsResponse } from '@alom-rentals/shared';

export interface UseShortTermRentalsParams {
  page?: number;
  limit?: number;
  status?: string;
  propertyId?: string;
}

export const useShortTermRentals = (params?: UseShortTermRentalsParams) => {
  const queryClient = useQueryClient();

  // Get all rentals
  const {
    data: rentalsData,
    isLoading,
    error,
    refetch,
  } = useQuery<ShortTermRentalsResponse>({
    queryKey: ['shortTermRentals', params],
    queryFn: async () => {
      const response = await shortTermRentalsAPI.getAll(params);
      return response.data;
    },
  });

  // Create rental mutation
  const createMutation = useMutation<IShortTermRental, Error, IShortTermRentalCreateData>({
    mutationFn: async (data: IShortTermRentalCreateData) => {
      const response = await shortTermRentalsAPI.create(data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shortTermRentals'] });
      queryClient.invalidateQueries({ queryKey: ['properties'] }); // Update property rental status
    },
  });

  // Update rental mutation
  const updateMutation = useMutation<IShortTermRental, Error, { id: string; data: IShortTermRentalUpdateData }>({
    mutationFn: async ({ id, data }: { id: string; data: IShortTermRentalUpdateData }) => {
      const response = await shortTermRentalsAPI.update(id, data);
      return response.data;
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['shortTermRentals'] });
      queryClient.invalidateQueries({ queryKey: ['shortTermRental', id] });
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
  });

  // Delete rental mutation
  const deleteMutation = useMutation<void, Error, string>({
    mutationFn: async (id: string) => {
      await shortTermRentalsAPI.delete(id);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shortTermRentals'] });
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
  });

  // Bulk delete rentals mutation
  const bulkDeleteMutation = useMutation<{ message: string; deletedCount: number; deletedIds: string[] }, Error, string[]>({
    mutationFn: async (rentalIds: string[]) => {
      const response = await shortTermRentalsAPI.bulkDelete(rentalIds);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['shortTermRentals'] });
      queryClient.invalidateQueries({ queryKey: ['properties'] });
    },
  });

  return {
    // Data
    rentals: rentalsData?.rentals || [],
    pagination: rentalsData?.pagination,

    // Loading states
    loading: isLoading,
    isCreating: createMutation.isPending,
    isUpdating: updateMutation.isPending,
    isDeleting: deleteMutation.isPending,
    isBulkDeleting: bulkDeleteMutation.isPending,

    // Error states
    error,
    createError: createMutation.error,
    updateError: updateMutation.error,
    deleteError: deleteMutation.error,
    bulkDeleteError: bulkDeleteMutation.error,

    // Actions
    refetch,
    createRental: createMutation.mutate,
    updateRental: updateMutation.mutate,
    deleteRental: deleteMutation.mutate,
    bulkDeleteRentals: bulkDeleteMutation.mutate,
  };
};

export default useShortTermRentals;

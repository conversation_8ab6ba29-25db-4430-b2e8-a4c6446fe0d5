import { useContext } from 'react';
import { useAuth } from './useAuth';
import { CustomerContext, CustomerContextType } from '../contexts/CustomerContextDefinition';

// Hook to use the customer context
export const useCustomerContext = (): CustomerContextType => {
  const context = useContext(CustomerContext);
  if (!context) {
    throw new Error('useCustomerContext must be used within a CustomerProvider');
  }
  return context;
};

// Hook to get the current customer ID (works for all roles)
export const useCurrentCustomerId = (): string | null => {
  const { user } = useAuth();
  const context = useContext(CustomerContext);

  // For admins and customers, return their own ID or null
  if (user?.role === 'ADMIN') {
    return null; // Admins can see all data
  }

  if (user?.role === 'CUSTOMER') {
    return user.id; // Customers see their own data
  }

  // For operators, return the selected customer ID
  if (user?.role === 'OPERATOR' && context) {
    return context.selectedCustomer;
  }

  return null;
};

// Hook to check if user has specific permission
export const useCustomerPermission = (permission: keyof CustomerContextType['customerPermissions']) => {
  const { user } = useAuth();
  const { customerPermissions } = useCustomerContext();

  // Admins have all permissions
  if (user?.role === 'ADMIN') {
    return true;
  }

  // Customers have all permissions for their own data
  if (user?.role === 'CUSTOMER') {
    return true;
  }

  // For operators, check the specific permission
  if (user?.role === 'OPERATOR' && customerPermissions) {
    return customerPermissions[permission] || false;
  }

  return false;
};

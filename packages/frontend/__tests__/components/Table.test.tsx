import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { Table, type TableColumn } from '../../components/ui/Table';

describe('Table Component', () => {
  const mockData = [
    { id: '1', name: '<PERSON>', email: '<EMAIL>', status: 'active' },
    { id: '2', name: '<PERSON>', email: '<EMAIL>', status: 'inactive' },
    { id: '3', name: '<PERSON>', email: '<EMAIL>', status: 'active' },
  ];

  const mockColumns: TableColumn<(typeof mockData)[0]>[] = [
    { key: 'name', header: 'Name' },
    { key: 'email', header: 'Email' },
    { key: 'status', header: 'Status' },
  ];

  describe('Basic Functionality', () => {
    it('should render table with data', () => {
      render(<Table data={mockData} columns={mockColumns} />);

      expect(screen.getByText('Name')).toBeInTheDocument();
      expect(screen.getByText('Email')).toBeInTheDocument();
      expect(screen.getByText('Status')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('should render empty message when no data', () => {
      render(<Table data={[]} columns={mockColumns} emptyMessage="No data found" />);

      expect(screen.getByText('No data found')).toBeInTheDocument();
    });
  });

  describe('Selection Functionality', () => {
    it('should render checkboxes when selectable is true', () => {
      const mockOnSelectionChange = vi.fn();

      render(<Table data={mockData} columns={mockColumns} selectable selectedItems={[]} onSelectionChange={mockOnSelectionChange} />);

      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes).toHaveLength(4); // 3 data rows + 1 header
    });

    it('should call onSelectionChange when item is selected', () => {
      const mockOnSelectionChange = vi.fn();

      render(<Table data={mockData} columns={mockColumns} selectable selectedItems={[]} onSelectionChange={mockOnSelectionChange} />);

      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[1]); // First data row checkbox

      expect(mockOnSelectionChange).toHaveBeenCalledWith([mockData[0]]);
    });

    it('should handle header checkbox for select all', () => {
      const mockOnSelectionChange = vi.fn();

      render(<Table data={mockData} columns={mockColumns} selectable selectedItems={[]} onSelectionChange={mockOnSelectionChange} />);

      const headerCheckbox = screen.getAllByRole('checkbox')[0];
      fireEvent.click(headerCheckbox);

      expect(mockOnSelectionChange).toHaveBeenCalledWith(mockData);
    });

    it('should show selected items correctly', () => {
      const mockOnSelectionChange = vi.fn();

      render(<Table data={mockData} columns={mockColumns} selectable selectedItems={[mockData[0], mockData[1]]} onSelectionChange={mockOnSelectionChange} />);

      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes[1]).toBeChecked(); // First data row
      expect(checkboxes[2]).toBeChecked(); // Second data row
      expect(checkboxes[3]).not.toBeChecked(); // Third data row
    });

    it('should handle indeterminate state for header checkbox', () => {
      const mockOnSelectionChange = vi.fn();

      render(
        <Table
          data={mockData}
          columns={mockColumns}
          selectable
          selectedItems={[mockData[0]]} // Only one item selected
          onSelectionChange={mockOnSelectionChange}
        />
      );

      const headerCheckbox = screen.getAllByRole('checkbox')[0] as HTMLInputElement;
      expect(headerCheckbox.indeterminate).toBe(true);
    });
  });

  describe('Pagination Functionality', () => {
    const mockPagination = {
      currentPage: 1,
      totalPages: 3,
      totalItems: 25,
      onPageChange: vi.fn(),
    };

    it('should render pagination controls', () => {
      render(<Table data={mockData} columns={mockColumns} pagination={mockPagination} />);

      expect(screen.getByText('Previous')).toBeInTheDocument();
      expect(screen.getByText('Next')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
      expect(screen.getByText('3')).toBeInTheDocument();
    });

    it('should call onPageChange when page button is clicked', () => {
      const mockOnPageChange = vi.fn();
      const paginationProps = { ...mockPagination, onPageChange: mockOnPageChange };

      render(<Table data={mockData} columns={mockColumns} pagination={paginationProps} />);

      const page2Button = screen.getByText('2');
      fireEvent.click(page2Button);

      expect(mockOnPageChange).toHaveBeenCalledWith(2);
    });

    it('should disable Previous button on first page', () => {
      render(<Table data={mockData} columns={mockColumns} pagination={mockPagination} />);

      const previousButton = screen.getByText('Previous');
      expect(previousButton).toBeDisabled();
    });

    it('should disable Next button on last page', () => {
      const lastPagePagination = { ...mockPagination, currentPage: 3 };

      render(<Table data={mockData} columns={mockColumns} pagination={lastPagePagination} />);

      const nextButton = screen.getByText('Next');
      expect(nextButton).toBeDisabled();
    });

    it('should show current page as active', () => {
      const currentPagePagination = { ...mockPagination, currentPage: 2 };

      render(<Table data={mockData} columns={mockColumns} pagination={currentPagePagination} />);

      const page2Button = screen.getByText('2');
      expect(page2Button).toHaveClass('text-blue-600', 'bg-blue-50');
    });

    it('should handle Previous and Next button clicks', () => {
      const mockOnPageChange = vi.fn();
      const paginationProps = {
        ...mockPagination,
        currentPage: 2,
        onPageChange: mockOnPageChange,
      };

      render(<Table data={mockData} columns={mockColumns} pagination={paginationProps} />);

      const previousButton = screen.getByText('Previous');
      const nextButton = screen.getByText('Next');

      fireEvent.click(previousButton);
      expect(mockOnPageChange).toHaveBeenCalledWith(1);

      fireEvent.click(nextButton);
      expect(mockOnPageChange).toHaveBeenCalledWith(3);
    });
  });

  describe('Combined Selection and Pagination', () => {
    it('should work with both selection and pagination enabled', () => {
      const mockOnSelectionChange = vi.fn();
      const mockOnPageChange = vi.fn();

      render(
        <Table
          data={mockData}
          columns={mockColumns}
          selectable
          selectedItems={[]}
          onSelectionChange={mockOnSelectionChange}
          pagination={{
            currentPage: 1,
            totalPages: 2,
            totalItems: 6,
            onPageChange: mockOnPageChange,
          }}
        />
      );

      // Should have both checkboxes and pagination
      expect(screen.getAllByRole('checkbox')).toHaveLength(4);
      expect(screen.getByText('Previous')).toBeInTheDocument();
      expect(screen.getByText('Next')).toBeInTheDocument();

      // Test selection
      const firstCheckbox = screen.getAllByRole('checkbox')[1];
      fireEvent.click(firstCheckbox);
      expect(mockOnSelectionChange).toHaveBeenCalled();

      // Test pagination
      const nextButton = screen.getByText('Next');
      fireEvent.click(nextButton);
      expect(mockOnPageChange).toHaveBeenCalledWith(2);
    });
  });

  describe('Custom Rendering', () => {
    it('should support custom cell rendering', () => {
      const customColumns: TableColumn<(typeof mockData)[0]>[] = [
        {
          key: 'name',
          header: 'Name',
          render: (item) => <strong>{item.name}</strong>,
        },
        {
          key: 'status',
          header: 'Status',
          render: (item) => <span className={item.status === 'active' ? 'text-green-600' : 'text-red-600'}>{item.status}</span>,
        },
      ];

      render(<Table data={mockData} columns={customColumns} />);

      const nameElement = screen.getByText('John Doe');
      expect(nameElement.tagName).toBe('STRONG');

      const statusElement = screen.getByText('active');
      expect(statusElement).toHaveClass('text-green-600');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels for checkboxes', () => {
      const mockOnSelectionChange = vi.fn();

      render(<Table data={mockData} columns={mockColumns} selectable selectedItems={[]} onSelectionChange={mockOnSelectionChange} />);

      const headerCheckbox = screen.getAllByRole('checkbox')[0];
      expect(headerCheckbox).toHaveAttribute('aria-label', 'Select all items');

      const firstRowCheckbox = screen.getAllByRole('checkbox')[1];
      expect(firstRowCheckbox).toHaveAttribute('aria-label', 'Select John Doe');
    });

    it('should have proper table structure', () => {
      render(<Table data={mockData} columns={mockColumns} />);

      const table = screen.getByRole('table');
      expect(table).toBeInTheDocument();

      const columnHeaders = screen.getAllByRole('columnheader');
      expect(columnHeaders).toHaveLength(3);

      const rows = screen.getAllByRole('row');
      expect(rows).toHaveLength(4); // 1 header + 3 data rows
    });
  });
});

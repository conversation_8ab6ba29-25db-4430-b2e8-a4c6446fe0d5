import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { GuestsPage } from '../../components/guests/GuestsPage';
import { PartnersPage } from '../../components/partners/PartnersPage';
import CurrenciesPage from '../../components/currencies/CurrenciesPage';
import { RentalsPage } from '../../pages/RentalsPage';

// Mock the API modules
vi.mock('../../lib/api', () => ({
  guestsAPI: {
    getAll: vi.fn(),
    bulkDelete: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    search: vi.fn(),
  },
  partnersAPI: {
    getAll: vi.fn(),
    bulkDelete: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
  currenciesAPI: {
    getAll: vi.fn(),
    bulkDelete: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
  shortTermRentalsAPI: {
    getAll: vi.fn(),
    bulkDelete: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
  propertiesAPI: {
    getAll: vi.fn(),
  },
}));

// Mock hooks
vi.mock('../../hooks/useGuests');
vi.mock('../../hooks/usePartners');
vi.mock('../../hooks/useCurrencies');
vi.mock('../../hooks/useShortTermRentals');
vi.mock('../../hooks/useProperties');

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>{children}</BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Pagination and Selection Components', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('GuestsPage', () => {
    const mockGuests = Array.from({ length: 25 }, (_, i) => ({
      _id: `guest-${i + 1}`,
      firstName: `Guest${i + 1}`,
      lastName: 'Test',
      email: `guest${i + 1}@example.com`,
      relationshipWithOwner: 'Friend',
      isForeigner: i % 2 === 0,
    }));

    beforeEach(async () => {
      const { useGuests } = await import('../../hooks/useGuests');
      useGuests.mockReturnValue({
        guests: mockGuests,
        isLoading: false,
        deleteGuest: vi.fn(),
        isDeleting: false,
        bulkDeleteGuests: vi.fn().mockResolvedValue({ success: true, deletedCount: 2 }),
        isBulkDeleting: false,
      });
    });

    it('should display paginated guests (12 per page)', () => {
      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      // Should show first 12 guests
      expect(screen.getByText('Guest1 Test')).toBeInTheDocument();
      expect(screen.getByText('Guest12 Test')).toBeInTheDocument();
      expect(screen.queryByText('Guest13 Test')).not.toBeInTheDocument();
    });

    it('should navigate between pages', async () => {
      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      // Click next page
      const nextButton = screen.getByText('Next');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText('Guest13 Test')).toBeInTheDocument();
        expect(screen.getByText('Guest24 Test')).toBeInTheDocument();
        expect(screen.queryByText('Guest1 Test')).not.toBeInTheDocument();
      });
    });

    it('should handle guest selection', () => {
      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      // Select first guest
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[0]);

      // Bulk actions should appear
      expect(screen.getByText('1 selected')).toBeInTheDocument();
      expect(screen.getByText('Select All')).toBeInTheDocument();
      expect(screen.getByText('Select None')).toBeInTheDocument();
      expect(screen.getByText('Delete 1')).toBeInTheDocument();
    });

    it('should handle select all functionality', () => {
      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      // Select first guest to show bulk actions
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[0]);

      // Click select all
      const selectAllButton = screen.getByText('Select All');
      fireEvent.click(selectAllButton);

      expect(screen.getByText('25 selected')).toBeInTheDocument();
    });

    it('should handle bulk delete confirmation', async () => {
      const mockBulkDelete = vi.fn().mockResolvedValue({ success: true, deletedCount: 2 });
      const { useGuests } = await import('../../hooks/useGuests');
      useGuests.mockReturnValue({
        guests: mockGuests,
        isLoading: false,
        deleteGuest: vi.fn(),
        isDeleting: false,
        bulkDeleteGuests: mockBulkDelete,
        isBulkDeleting: false,
      });

      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      // Select guests
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[0]);
      fireEvent.click(checkboxes[1]);

      // Click bulk delete
      const deleteButton = screen.getByText('Delete 2');
      fireEvent.click(deleteButton);

      // Confirmation modal should appear
      expect(screen.getByText('Delete Guests')).toBeInTheDocument();
      expect(screen.getByText('Are you sure you want to delete 2 guests? This action cannot be undone.')).toBeInTheDocument();

      // Confirm deletion
      const confirmButton = screen.getByText('Delete 2 Guests');
      fireEvent.click(confirmButton);

      await waitFor(() => {
        expect(mockBulkDelete).toHaveBeenCalledWith(['guest-1', 'guest-2']);
      });
    });
  });

  describe('PartnersPage', () => {
    const mockPartners = Array.from({ length: 15 }, (_, i) => ({
      _id: `partner-${i + 1}`,
      name: `Partner ${i + 1}`,
      email: `partner${i + 1}@example.com`,
      createdAt: new Date().toISOString(),
    }));

    beforeEach(async () => {
      const { usePartners } = await import('../../hooks/usePartners');
      usePartners.mockReturnValue({
        partners: mockPartners,
        isLoading: false,
        updatePartner: vi.fn(),
        deletePartner: vi.fn(),
        isUpdating: false,
        isDeleting: false,
        bulkDeletePartners: vi.fn().mockResolvedValue({ success: true, deletedCount: 3 }),
        isBulkDeleting: false,
      });
    });

    it('should display paginated partners (10 per page)', () => {
      const wrapper = createWrapper();
      render(<PartnersPage />, { wrapper });

      // Should show first 10 partners
      expect(screen.getByText('Partner 1')).toBeInTheDocument();
      expect(screen.getByText('Partner 10')).toBeInTheDocument();
      expect(screen.queryByText('Partner 11')).not.toBeInTheDocument();
    });

    it('should show pagination controls when needed', () => {
      const wrapper = createWrapper();
      render(<PartnersPage />, { wrapper });

      // Should show pagination (15 partners, 10 per page = 2 pages)
      expect(screen.getByText('Previous')).toBeInTheDocument();
      expect(screen.getByText('Next')).toBeInTheDocument();
      expect(screen.getByText('1')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
    });

    it('should handle partner selection with table checkboxes', () => {
      const wrapper = createWrapper();
      render(<PartnersPage />, { wrapper });

      // Select header checkbox (select all visible)
      const headerCheckbox = screen.getAllByRole('checkbox')[0];
      fireEvent.click(headerCheckbox);

      // Should select all partners
      expect(screen.getByText('15 selected')).toBeInTheDocument();
    });
  });

  describe('CurrenciesPage', () => {
    const mockCurrencies = Array.from({ length: 12 }, (_, i) => ({
      _id: `currency-${i + 1}`,
      name: `Currency ${i + 1}`,
      symbol: `$${i + 1}`,
      code: `C${i + 1}`,
    }));

    beforeEach(async () => {
      const { useCurrencies } = await import('../../hooks/useCurrencies');
      useCurrencies.mockReturnValue({
        currencies: mockCurrencies,
        loading: false,
        error: null,
        createCurrency: vi.fn(),
        updateCurrency: vi.fn(),
        deleteCurrency: vi.fn(),
        bulkDeleteCurrencies: vi.fn().mockResolvedValue({ success: true, deletedCount: 2 }),
        refetch: vi.fn(),
        isCreating: false,
        isUpdating: false,
        isDeleting: false,
        isBulkDeleting: false,
      });
    });

    it('should display paginated currencies (10 per page)', () => {
      const wrapper = createWrapper();
      render(<CurrenciesPage />, { wrapper });

      // Should show first 10 currencies
      expect(screen.getByText('Currency 1')).toBeInTheDocument();
      expect(screen.getByText('Currency 10')).toBeInTheDocument();
      expect(screen.queryByText('Currency 11')).not.toBeInTheDocument();
    });

    it('should handle currency selection', () => {
      const wrapper = createWrapper();
      render(<CurrenciesPage />, { wrapper });

      // Select first currency
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[0]);

      // Bulk actions should appear
      expect(screen.getByText('1 selected')).toBeInTheDocument();
      expect(screen.getByText('Delete 1')).toBeInTheDocument();
    });

    it('should show pagination when there are more than 10 currencies', () => {
      const wrapper = createWrapper();
      render(<CurrenciesPage />, { wrapper });

      // Should show pagination (12 currencies, 10 per page = 2 pages)
      expect(screen.getByText('Previous')).toBeInTheDocument();
      expect(screen.getByText('Next')).toBeInTheDocument();
    });
  });

  describe('RentalsPage', () => {
    const mockRentals = Array.from({ length: 15 }, (_, i) => ({
      _id: `rental-${i + 1}`,
      propertyId: `property-${i + 1}`,
      guestId: `guest-${i + 1}`,
      checkInDate: new Date('2024-01-01'),
      checkOutDate: new Date('2024-01-05'),
      totalAmount: 500 + i * 50,
      status: 'confirmed',
    }));

    beforeEach(async () => {
      const { useShortTermRentals } = await import('../../hooks/useShortTermRentals');
      useShortTermRentals.mockReturnValue({
        rentals: mockRentals,
        loading: false,
        bulkDeleteRentals: vi.fn(),
        isBulkDeleting: false,
        createRental: vi.fn(),
        updateRental: vi.fn(),
        deleteRental: vi.fn(),
        isCreating: false,
        isUpdating: false,
        isDeleting: false,
        error: null,
        refetch: vi.fn(),
      });

      const { useProperties } = await import('../../hooks/useProperties');
      useProperties.mockReturnValue({
        properties: [],
        isLoading: false,
      });
    });

    it('should display paginated rentals (10 per page)', () => {
      const wrapper = createWrapper();
      render(<RentalsPage />, { wrapper });

      // Should show table with rentals
      expect(screen.getByText('Your Rentals')).toBeInTheDocument();

      // Should show pagination controls
      expect(screen.getByText('Previous')).toBeInTheDocument();
      expect(screen.getByText('Next')).toBeInTheDocument();
    });

    it('should handle rental selection and bulk actions', () => {
      const wrapper = createWrapper();
      render(<RentalsPage />, { wrapper });

      // The table should be rendered with selectable items
      const table = screen.getByRole('table');
      expect(table).toBeInTheDocument();
    });
  });

  describe('Common Pagination Behavior', () => {
    it('should disable Previous button on first page', async () => {
      const mockData = Array.from({ length: 25 }, (_, i) => ({
        _id: `item-${i + 1}`,
        name: `Item ${i + 1}`,
      }));

      const { useGuests } = await import('../../hooks/useGuests');
      useGuests.mockReturnValue({
        guests: mockData,
        isLoading: false,
        deleteGuest: vi.fn(),
        isDeleting: false,
        bulkDeleteGuests: vi.fn(),
        isBulkDeleting: false,
      });

      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      const previousButton = screen.getByText('Previous');
      expect(previousButton).toBeDisabled();
    });

    it('should disable Next button on last page', async () => {
      const mockData = Array.from({ length: 13 }, (_, i) => ({
        _id: `item-${i + 1}`,
        name: `Item ${i + 1}`,
      }));

      const { useGuests } = await import('../../hooks/useGuests');
      useGuests.mockReturnValue({
        guests: mockData,
        isLoading: false,
        deleteGuest: vi.fn(),
        isDeleting: false,
        bulkDeleteGuests: vi.fn(),
        isBulkDeleting: false,
      });

      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      // Go to second page (last page with 13 items, 12 per page)
      const nextButton = screen.getByText('Next');
      fireEvent.click(nextButton);

      await waitFor(() => {
        const nextButtonAfter = screen.getByText('Next');
        expect(nextButtonAfter).toBeDisabled();
      });
    });
  });
});

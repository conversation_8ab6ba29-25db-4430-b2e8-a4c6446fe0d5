import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { GuestsPage } from '../../components/guests/GuestsPage';
import { PartnersPage } from '../../components/partners/PartnersPage';
import CurrenciesPage from '../../components/currencies/CurrenciesPage';
import * as api from '../../lib/api';
import { createAxiosMockResponse } from '../../tests/utils';

// Mock the entire API module
vi.mock('../../lib/api');

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false, staleTime: 0 },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>{children}</BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Bulk Operations Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Guests Page Integration', () => {
    const mockGuests = [
      {
        _id: '1',
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        relationshipWithOwner: 'Friend',
        isForeigner: false,
      },
      {
        _id: '2',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        relationshipWithOwner: 'Family',
        isForeigner: true,
      },
      {
        _id: '3',
        firstName: 'Bob',
        lastName: 'Johnson',
        email: '<EMAIL>',
        relationshipWithOwner: 'Colleague',
        isForeigner: false,
      },
    ];

    beforeEach(() => {
      vi.mocked(api.guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockGuests }));
      vi.mocked(api.guestsAPI.bulkDelete).mockResolvedValue(
        createAxiosMockResponse({
          data: { message: '2 guests deleted successfully', deletedCount: 2, deletedIds: ['1', '2'] },
        })
      );
      vi.mocked(api.guestsAPI.search).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    });

    it('should complete full bulk delete workflow', async () => {
      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      // Wait for guests to load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Jane Smith')).toBeInTheDocument();
        expect(screen.getByText('Bob Johnson')).toBeInTheDocument();
      });

      // Select first two guests
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[0]); // John
      fireEvent.click(checkboxes[1]); // Jane

      // Verify selection UI appears
      await waitFor(() => {
        expect(screen.getByText('2 selected')).toBeInTheDocument();
        expect(screen.getByText('Delete 2')).toBeInTheDocument();
      });

      // Click bulk delete
      const deleteButton = screen.getByText('Delete 2');
      fireEvent.click(deleteButton);

      // Verify confirmation modal appears
      await waitFor(() => {
        expect(screen.getByText('Delete Guests')).toBeInTheDocument();
        expect(screen.getByText('Are you sure you want to delete 2 guests? This action cannot be undone.')).toBeInTheDocument();
      });

      // Confirm deletion
      const confirmButton = screen.getByText('Delete 2 Guests');
      fireEvent.click(confirmButton);

      // Verify API was called
      await waitFor(() => {
        expect(api.guestsAPI.bulkDelete).toHaveBeenCalledWith(['1', '2']);
      });
    });

    it('should handle select all and select none', async () => {
      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      // Wait for guests to load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Select one guest to show bulk actions
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[0]);

      await waitFor(() => {
        expect(screen.getByText('1 selected')).toBeInTheDocument();
      });

      // Click select all
      const selectAllButton = screen.getByText('Select All');
      fireEvent.click(selectAllButton);

      await waitFor(() => {
        expect(screen.getByText('3 selected')).toBeInTheDocument();
      });

      // Click select none
      const selectNoneButton = screen.getByText('Select None');
      fireEvent.click(selectNoneButton);

      await waitFor(() => {
        expect(screen.queryByText('selected')).not.toBeInTheDocument();
      });
    });

    it('should handle bulk delete errors gracefully', async () => {
      vi.mocked(api.guestsAPI.bulkDelete).mockRejectedValue(new Error('Network error'));

      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      // Wait for guests to load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Select and delete
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[0]);

      const deleteButton = screen.getByText('Delete 1');
      fireEvent.click(deleteButton);

      const confirmButton = screen.getByText('Delete 1 Guests');
      fireEvent.click(confirmButton);

      // Should handle error (implementation depends on error handling in component)
      await waitFor(() => {
        expect(api.guestsAPI.bulkDelete).toHaveBeenCalled();
      });
    });
  });

  describe('Partners Page Integration', () => {
    const mockPartners = [
      { _id: '1', name: 'Partner One', email: '<EMAIL>', createdAt: '2024-01-01' },
      { _id: '2', name: 'Partner Two', email: '<EMAIL>', createdAt: '2024-01-02' },
    ];

    beforeEach(() => {
      vi.mocked(api.partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockPartners }));
      vi.mocked(api.partnersAPI.bulkDelete).mockResolvedValue(
        createAxiosMockResponse({
          data: { message: '2 partners deleted successfully', deletedCount: 2, deletedIds: ['1', '2'] },
        })
      );
    });

    it('should complete full bulk delete workflow for partners', async () => {
      const wrapper = createWrapper();
      render(<PartnersPage />, { wrapper });

      // Wait for partners to load
      await waitFor(() => {
        expect(screen.getByText('Partner One')).toBeInTheDocument();
        expect(screen.getByText('Partner Two')).toBeInTheDocument();
      });

      // Select both partners using individual checkboxes
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[1]); // First partner (index 0 is header checkbox)
      fireEvent.click(checkboxes[2]); // Second partner

      // Verify selection UI appears
      await waitFor(() => {
        expect(screen.getByText('2 selected')).toBeInTheDocument();
        expect(screen.getByText('Delete 2')).toBeInTheDocument();
      });

      // Click bulk delete
      const deleteButton = screen.getByText('Delete 2');
      fireEvent.click(deleteButton);

      // Verify confirmation modal appears
      await waitFor(() => {
        expect(screen.getByText('Delete Partners')).toBeInTheDocument();
      });

      // Confirm deletion
      const confirmButton = screen.getByText('Delete 2 Partners');
      fireEvent.click(confirmButton);

      // Verify API was called
      await waitFor(() => {
        expect(api.partnersAPI.bulkDelete).toHaveBeenCalledWith(['1', '2']);
      });
    });

    it('should handle partner deletion with property associations error', async () => {
      vi.mocked(api.partnersAPI.bulkDelete).mockRejectedValue({
        response: {
          data: {
            error: 'Cannot delete partners that are associated with properties. Please remove them from properties first.',
            affectedProperties: 2,
          },
        },
      });

      const wrapper = createWrapper();
      render(<PartnersPage />, { wrapper });

      // Wait for partners to load
      await waitFor(() => {
        expect(screen.getByText('Partner One')).toBeInTheDocument();
      });

      // Select and try to delete
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[1]);

      const deleteButton = screen.getByText('Delete 1');
      fireEvent.click(deleteButton);

      const confirmButton = screen.getByText('Delete 1 Partners');
      fireEvent.click(confirmButton);

      // Should handle the specific error
      await waitFor(() => {
        expect(api.partnersAPI.bulkDelete).toHaveBeenCalled();
      });
    });
  });

  describe('Currencies Page Integration', () => {
    const mockCurrencies = [
      { _id: '1', name: 'US Dollar', symbol: '$', code: 'USD' },
      { _id: '2', name: 'Euro', symbol: '€', code: 'EUR' },
      { _id: '3', name: 'British Pound', symbol: '£', code: 'GBP' },
    ];

    beforeEach(() => {
      vi.mocked(api.currenciesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockCurrencies }));
      vi.mocked(api.currenciesAPI.bulkDelete).mockResolvedValue(
        createAxiosMockResponse({
          data: { message: '2 currencies deleted successfully', deletedCount: 2, deletedIds: ['1', '2'] },
        })
      );
    });

    it('should complete full bulk delete workflow for currencies', async () => {
      const wrapper = createWrapper();
      render(<CurrenciesPage />, { wrapper });

      // Wait for currencies to load
      await waitFor(() => {
        expect(screen.getByText('US Dollar')).toBeInTheDocument();
        expect(screen.getByText('Euro')).toBeInTheDocument();
        expect(screen.getByText('British Pound')).toBeInTheDocument();
      });

      // Select first two currencies
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[0]); // US Dollar
      fireEvent.click(checkboxes[1]); // Euro

      // Verify selection UI appears
      await waitFor(() => {
        expect(screen.getByText('2 selected')).toBeInTheDocument();
        expect(screen.getByText('Delete 2')).toBeInTheDocument();
      });

      // Click bulk delete
      const deleteButton = screen.getByText('Delete 2');
      fireEvent.click(deleteButton);

      // Verify confirmation modal appears
      await waitFor(() => {
        expect(screen.getByText('Delete Currencies')).toBeInTheDocument();
      });

      // Confirm deletion
      const confirmButton = screen.getByText('Delete 2 Currencies');
      fireEvent.click(confirmButton);

      // Verify API was called
      await waitFor(() => {
        expect(api.currenciesAPI.bulkDelete).toHaveBeenCalledWith(['1', '2']);
      });
    });
  });

  describe('Pagination Integration', () => {
    it('should handle pagination with large datasets', async () => {
      const largeGuestDataset = Array.from({ length: 25 }, (_, i) => ({
        _id: `guest-${i + 1}`,
        firstName: `Guest${i + 1}`,
        lastName: 'Test',
        email: `guest${i + 1}@example.com`,
        relationshipWithOwner: 'Friend',
        isForeigner: i % 2 === 0,
      }));

      vi.mocked(api.guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: largeGuestDataset }));
      vi.mocked(api.guestsAPI.search).mockResolvedValue(createAxiosMockResponse({ data: [] }));

      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      // Wait for first page to load (should show first 12 guests)
      await waitFor(() => {
        expect(screen.getByText('Guest1 Test')).toBeInTheDocument();
        expect(screen.getByText('Guest12 Test')).toBeInTheDocument();
        expect(screen.queryByText('Guest13 Test')).not.toBeInTheDocument();
      });

      // Navigate to next page
      const nextButton = screen.getByText('Next');
      fireEvent.click(nextButton);

      // Should show next 12 guests
      await waitFor(() => {
        expect(screen.getByText('Guest13 Test')).toBeInTheDocument();
        expect(screen.getByText('Guest24 Test')).toBeInTheDocument();
        expect(screen.queryByText('Guest1 Test')).not.toBeInTheDocument();
      });

      // Navigate to last page
      fireEvent.click(nextButton);

      // Should show remaining guest
      await waitFor(() => {
        expect(screen.getByText('Guest25 Test')).toBeInTheDocument();
        expect(screen.queryByText('Guest24 Test')).not.toBeInTheDocument();
      });
    });

    it('should maintain selection state when changing pages', async () => {
      const largeGuestDataset = Array.from({ length: 25 }, (_, i) => ({
        _id: `guest-${i + 1}`,
        firstName: `Guest${i + 1}`,
        lastName: 'Test',
        email: `guest${i + 1}@example.com`,
        relationshipWithOwner: 'Friend',
        isForeigner: i % 2 === 0,
      }));

      vi.mocked(api.guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: largeGuestDataset }));
      vi.mocked(api.guestsAPI.search).mockResolvedValue(createAxiosMockResponse({ data: [] }));

      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      // Wait for first page to load
      await waitFor(() => {
        expect(screen.getByText('Guest1 Test')).toBeInTheDocument();
      });

      // Select first guest
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[0]);

      await waitFor(() => {
        expect(screen.getByText('1 selected')).toBeInTheDocument();
      });

      // Navigate to next page
      const nextButton = screen.getByText('Next');
      fireEvent.click(nextButton);

      // Selection should be maintained
      await waitFor(() => {
        expect(screen.getByText('1 selected')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle network errors during bulk operations', async () => {
      const mockGuests = [{ _id: '1', firstName: 'John', lastName: 'Doe', email: '<EMAIL>', relationshipWithOwner: 'Friend', isForeigner: false }];

      vi.mocked(api.guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockGuests }));
      vi.mocked(api.guestsAPI.search).mockResolvedValue(createAxiosMockResponse({ data: [] }));
      vi.mocked(api.guestsAPI.bulkDelete).mockRejectedValue(new Error('Network error'));

      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Select and attempt to delete
      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[0]);

      const deleteButton = screen.getByText('Delete 1');
      fireEvent.click(deleteButton);

      const confirmButton = screen.getByText('Delete 1 Guests');
      fireEvent.click(confirmButton);

      // Should attempt the API call
      await waitFor(() => {
        expect(api.guestsAPI.bulkDelete).toHaveBeenCalledWith(['1']);
      });
    });

    it('should handle empty datasets gracefully', async () => {
      vi.mocked(api.guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
      vi.mocked(api.guestsAPI.search).mockResolvedValue(createAxiosMockResponse({ data: [] }));

      const wrapper = createWrapper();
      render(<GuestsPage />, { wrapper });

      await waitFor(() => {
        expect(screen.getByText('No guests found. Add your first guest to get started!')).toBeInTheDocument();
      });

      // Should not show pagination or bulk actions
      expect(screen.queryByText('Previous')).not.toBeInTheDocument();
      expect(screen.queryByText('Next')).not.toBeInTheDocument();
      expect(screen.queryByText('selected')).not.toBeInTheDocument();
    });
  });
});

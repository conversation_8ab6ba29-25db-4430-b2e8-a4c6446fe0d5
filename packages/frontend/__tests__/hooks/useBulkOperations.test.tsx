import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useGuests } from '../../hooks/useGuests';
import { usePartners } from '../../hooks/usePartners';
import { useCurrencies } from '../../hooks/useCurrencies';
import { useShortTermRentals } from '../../hooks/useShortTermRentals';
import { guestsAPI, partnersAPI, currenciesAPI, shortTermRentalsAPI } from '../../lib/api';
import { createAxiosMockResponse } from '../../tests/utils';

// Mock the API modules
vi.mock('../../lib/api', () => ({
  guestsAPI: {
    getAll: vi.fn(),
    bulkDelete: vi.fn(),
  },
  partnersAPI: {
    getAll: vi.fn(),
    bulkDelete: vi.fn(),
  },
  currenciesAPI: {
    getAll: vi.fn(),
    bulkDelete: vi.fn(),
  },
  shortTermRentalsAPI: {
    getAll: vi.fn(),
    bulkDelete: vi.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
};

describe('Bulk Operations Hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('useGuests bulk delete', () => {
    it('should successfully bulk delete guests', async () => {
      const mockGuests = [
        { _id: '1', firstName: 'John', lastName: 'Doe', email: '<EMAIL>' },
        { _id: '2', firstName: 'Jane', lastName: 'Smith', email: '<EMAIL>' },
      ];

      vi.mocked(guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockGuests }));
      vi.mocked(guestsAPI.bulkDelete).mockResolvedValue(
        createAxiosMockResponse({
          data: { message: '2 guests deleted successfully', deletedCount: 2, deletedIds: ['1', '2'] },
        })
      );

      const wrapper = createWrapper();
      const { result } = renderHook(() => useGuests(), { wrapper });

      await waitFor(() => {
        expect(result.current.guests).toEqual(mockGuests);
      });

      const deleteResult = await result.current.bulkDeleteGuests(['1', '2']);

      expect(deleteResult.success).toBe(true);
      expect(deleteResult.deletedCount).toBe(2);
      expect(guestsAPI.bulkDelete).toHaveBeenCalledWith(['1', '2']);
    });

    it('should handle bulk delete errors', async () => {
      vi.mocked(guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
      vi.mocked(guestsAPI.bulkDelete).mockRejectedValue(new Error('Network error'));

      const wrapper = createWrapper();
      const { result } = renderHook(() => useGuests(), { wrapper });

      const deleteResult = await result.current.bulkDeleteGuests(['1', '2']);

      expect(deleteResult.success).toBe(false);
      expect(deleteResult.error?.message).toContain('Failed to delete guests');
    });
  });

  describe('usePartners bulk delete', () => {
    it('should successfully bulk delete partners', async () => {
      const mockPartners = [
        { _id: '1', name: 'Partner One', email: '<EMAIL>' },
        { _id: '2', name: 'Partner Two', email: '<EMAIL>' },
      ];

      vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockPartners }));
      vi.mocked(partnersAPI.bulkDelete).mockResolvedValue(
        createAxiosMockResponse({
          data: { message: '2 partners deleted successfully', deletedCount: 2, deletedIds: ['1', '2'] },
        })
      );

      const wrapper = createWrapper();
      const { result } = renderHook(() => usePartners(), { wrapper });

      await waitFor(() => {
        expect(result.current.partners).toEqual(mockPartners);
      });

      const deleteResult = await result.current.bulkDeletePartners(['1', '2']);

      expect(deleteResult.success).toBe(true);
      expect(deleteResult.deletedCount).toBe(2);
      expect(partnersAPI.bulkDelete).toHaveBeenCalledWith(['1', '2']);
    });

    it('should handle partner deletion with property associations', async () => {
      vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
      vi.mocked(partnersAPI.bulkDelete).mockRejectedValue({
        response: {
          data: {
            error: 'Cannot delete partners that are associated with properties. Please remove them from properties first.',
            affectedProperties: 2,
          },
        },
      });

      const wrapper = createWrapper();
      const { result } = renderHook(() => usePartners(), { wrapper });

      const deleteResult = await result.current.bulkDeletePartners(['1']);

      expect(deleteResult.success).toBe(false);
      expect(deleteResult.error?.message).toContain('Cannot delete partners that are associated with properties');
    });
  });

  describe('useCurrencies bulk delete', () => {
    it('should successfully bulk delete currencies', async () => {
      const mockCurrencies = [
        { _id: '1', name: 'US Dollar', symbol: '$', code: 'USD' },
        { _id: '2', name: 'Euro', symbol: '€', code: 'EUR' },
      ];

      vi.mocked(currenciesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockCurrencies }));
      vi.mocked(currenciesAPI.bulkDelete).mockResolvedValue(
        createAxiosMockResponse({
          data: { message: '2 currencies deleted successfully', deletedCount: 2, deletedIds: ['1', '2'] },
        })
      );

      const wrapper = createWrapper();
      const { result } = renderHook(() => useCurrencies(), { wrapper });

      await waitFor(() => {
        expect(result.current.currencies).toEqual(mockCurrencies);
      });

      const deleteResult = await result.current.bulkDeleteCurrencies(['1', '2']);

      expect(deleteResult.success).toBe(true);
      expect(deleteResult.deletedCount).toBe(2);
      expect(currenciesAPI.bulkDelete).toHaveBeenCalledWith(['1', '2']);
    });

    it('should handle currency deletion with property associations', async () => {
      vi.mocked(currenciesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
      vi.mocked(currenciesAPI.bulkDelete).mockRejectedValue({
        response: {
          data: {
            error: 'Cannot delete currencies that are associated with properties. Please remove them from properties first.',
            affectedProperties: 1,
          },
        },
      });

      const wrapper = createWrapper();
      const { result } = renderHook(() => useCurrencies(), { wrapper });

      const deleteResult = await result.current.bulkDeleteCurrencies(['1']);

      expect(deleteResult.success).toBe(false);
      expect(deleteResult.error?.message).toContain('Cannot delete currencies that are associated with properties');
    });
  });

  describe('useShortTermRentals bulk delete', () => {
    it('should successfully bulk delete rentals', async () => {
      const mockRentals = [
        { _id: '1', propertyId: 'prop1', guestId: 'guest1', totalAmount: 500 },
        { _id: '2', propertyId: 'prop1', guestId: 'guest2', totalAmount: 600 },
      ];

      vi.mocked(shortTermRentalsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockRentals }));
      vi.mocked(shortTermRentalsAPI.bulkDelete).mockResolvedValue(
        createAxiosMockResponse({
          data: { message: '2 rentals deleted successfully', deletedCount: 2, deletedIds: ['1', '2'] },
        })
      );

      const wrapper = createWrapper();
      const { result } = renderHook(() => useShortTermRentals(), { wrapper });

      await waitFor(() => {
        expect(result.current.rentals).toEqual(mockRentals);
      });

      // Test bulk delete mutation
      result.current.bulkDeleteRentals(['1', '2']);

      await waitFor(() => {
        expect(shortTermRentalsAPI.bulkDelete).toHaveBeenCalledWith(['1', '2']);
      });
    });

    it('should handle bulk delete loading states', async () => {
      vi.mocked(shortTermRentalsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));

      const wrapper = createWrapper();
      const { result } = renderHook(() => useShortTermRentals(), { wrapper });

      expect(result.current.isBulkDeleting).toBe(false);

      // The mutation should be available
      expect(typeof result.current.bulkDeleteRentals).toBe('function');
    });
  });

  describe('Loading states', () => {
    it('should provide correct loading states for all hooks', async () => {
      vi.mocked(guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
      vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
      vi.mocked(currenciesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
      vi.mocked(shortTermRentalsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));

      const wrapper = createWrapper();

      const guestsResult = renderHook(() => useGuests(), { wrapper });
      const partnersResult = renderHook(() => usePartners(), { wrapper });
      const currenciesResult = renderHook(() => useCurrencies(), { wrapper });
      const rentalsResult = renderHook(() => useShortTermRentals(), { wrapper });

      // All hooks should have isBulkDeleting property
      expect(typeof guestsResult.result.current.isBulkDeleting).toBe('boolean');
      expect(typeof partnersResult.result.current.isBulkDeleting).toBe('boolean');
      expect(typeof currenciesResult.result.current.isBulkDeleting).toBe('boolean');
      expect(typeof rentalsResult.result.current.isBulkDeleting).toBe('boolean');

      // All hooks should have bulk delete functions
      expect(typeof guestsResult.result.current.bulkDeleteGuests).toBe('function');
      expect(typeof partnersResult.result.current.bulkDeletePartners).toBe('function');
      expect(typeof currenciesResult.result.current.bulkDeleteCurrencies).toBe('function');
      expect(typeof rentalsResult.result.current.bulkDeleteRentals).toBe('function');
    });
  });
});

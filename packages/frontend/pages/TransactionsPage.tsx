import React, { useState, useMemo } from 'react';
import { useAuth } from '../hooks/useAuth';
import { useTransactions } from '../hooks/useTransactions';
import { useAdminTransactions } from '../hooks/useAdminDataAccess';
import { UserContextSwitcher, UserContextBadge } from '../components/admin/UserContextSwitcher';
import {
  Card,
  CardHeader,
  CardContent,
  Button,
  SearchInput,
  Select,
  LoadingState,
  Table,
  type TableColumn,
  ReadOnlyWrapper,
} from '../components/ui';
import { TransactionForm } from '../components/transactions/TransactionForm';
import { formatDate, formatCurrency } from '../utils';
import { ITransaction } from '@alom-rentals/shared';
import { Plus, DollarSign, Edit2, Trash2 } from 'lucide-react';

const TRANSACTION_TYPE_LABELS = {
  property_cost: 'Property Cost',
  rental_payment: 'Rental Payment',
  dividend_payment: 'Dividend Payment',
  manual: 'Manual Entry',
};

const RECURRENT_LABELS = {
  none: 'One-time',
  daily: 'Daily',
  weekly: 'Weekly',
  monthly: 'Monthly',
  yearly: 'Yearly',
};

export const TransactionsPage: React.FC = () => {
  const { user } = useAuth();
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>();
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingTransaction, setEditingTransaction] = useState<ITransaction | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedProperty, setSelectedProperty] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);

  // Data fetching
  const regularTransactionsQuery = useTransactions({
    page: currentPage,
    limit: 20,
    type: selectedType || undefined,
    propertyId: selectedProperty || undefined,
  });

  const adminTransactionsQuery = useAdminTransactions(selectedUserId, {
    page: currentPage,
    limit: 20,
    type: selectedType || undefined,
    propertyId: selectedProperty || undefined,
  });

  const isAdmin = user?.role === 'ADMIN';
  const { transactions, pagination, isLoading, createTransaction, updateTransaction, deleteTransaction } = isAdmin
    ? {
        transactions: adminTransactionsQuery.data?.transactions || [],
        pagination: adminTransactionsQuery.data?.pagination,
        isLoading: adminTransactionsQuery.isLoading,
        createTransaction: regularTransactionsQuery.createTransaction,
        updateTransaction: regularTransactionsQuery.updateTransaction,
        deleteTransaction: regularTransactionsQuery.deleteTransaction,
      }
    : regularTransactionsQuery;

  // Filter transactions by search query
  const filteredTransactions = useMemo(() => {
    if (!searchQuery) return transactions;
    
    return transactions.filter((transaction) =>
      transaction.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      transaction.category?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [transactions, searchQuery]);

  const handleCreateSuccess = () => {
    setShowCreateForm(false);
  };

  const handleEditSuccess = () => {
    setEditingTransaction(null);
  };

  const handleDeleteTransaction = (id: string) => {
    if (window.confirm('Are you sure you want to delete this transaction?')) {
      deleteTransaction(id);
    }
  };

  const getAmountColor = (amount: number) => {
    return amount >= 0 ? 'text-green-600' : 'text-red-600';
  };

  const columns: TableColumn<ITransaction>[] = [
    {
      key: 'date',
      header: 'Date',
      render: (transaction) => formatDate(transaction.date),
    },
    {
      key: 'description',
      header: 'Description',
      render: (transaction) => (
        <div>
          <div className="font-medium">{transaction.description}</div>
          {transaction.category && (
            <div className="text-sm text-gray-500">{transaction.category}</div>
          )}
        </div>
      ),
    },
    {
      key: 'type',
      header: 'Type',
      render: (transaction) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {TRANSACTION_TYPE_LABELS[transaction.type]}
        </span>
      ),
    },
    {
      key: 'amount',
      header: 'Amount',
      render: (transaction) => (
        <div className={`font-medium ${getAmountColor(transaction.amount)}`}>
          {transaction.amount >= 0 ? '+' : ''}{formatCurrency(transaction.amount)}
        </div>
      ),
    },
    {
      key: 'recurrent',
      header: 'Frequency',
      render: (transaction) => RECURRENT_LABELS[transaction.recurrent],
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (transaction) => (
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            icon={Edit2}
            onClick={() => setEditingTransaction(transaction)}
          />
          <Button
            variant="ghost"
            size="sm"
            icon={Trash2}
            onClick={() => handleDeleteTransaction(transaction._id)}
            className="text-red-600 hover:text-red-700"
          />
        </div>
      ),
    },
  ];

  if (isLoading) {
    return <LoadingState message="Loading transactions..." />;
  }

  return (
    <ReadOnlyWrapper message="You are viewing transactions in read-only mode. Contact an administrator to make changes.">
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Transactions</h1>
            <p className="text-gray-600 mt-1">
              {isAdmin ? 'Manage transactions across all users' : 'Manage your property transactions'}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {isAdmin && (
              <div className="flex items-center space-x-3">
                <UserContextBadge selectedUserId={selectedUserId} />
                <UserContextSwitcher
                  selectedUserId={selectedUserId}
                  onUserChange={setSelectedUserId}
                  className="w-64"
                />
              </div>
            )}
            <Button onClick={() => setShowCreateForm(true)} icon={Plus}>
              Add Transaction
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <SearchInput
                  placeholder="Search transactions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onClear={() => setSearchQuery('')}
                />
              </div>
              <div className="sm:w-48">
                <Select
                  placeholder="All Types"
                  value={selectedType}
                  onChange={setSelectedType}
                  options={[
                    { value: '', label: 'All Types' },
                    ...Object.entries(TRANSACTION_TYPE_LABELS).map(([value, label]) => ({
                      value,
                      label,
                    })),
                  ]}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Transactions Table */}
        <Card>
          <CardHeader title="Transactions" icon={DollarSign} />
          <CardContent className="p-0">
            <Table
              data={filteredTransactions}
              columns={columns}
              emptyMessage="No transactions found. Create your first transaction to get started!"
              pagination={pagination ? {
                currentPage: pagination.page,
                totalPages: pagination.pages,
                onPageChange: setCurrentPage,
                totalItems: pagination.total,
              } : undefined}
            />
          </CardContent>
        </Card>

        {/* Create/Edit Transaction Modal */}
        {(showCreateForm || editingTransaction) && (
          <TransactionForm
            transaction={editingTransaction}
            isOpen={showCreateForm || !!editingTransaction}
            onClose={() => {
              setShowCreateForm(false);
              setEditingTransaction(null);
            }}
            onSuccess={editingTransaction ? handleEditSuccess : handleCreateSuccess}
          />
        )}
      </div>
    </ReadOnlyWrapper>
  );
};
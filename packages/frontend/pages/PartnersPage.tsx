import React, { useState, useMemo } from 'react';
import { usePartners } from '../hooks/usePartners';
import { useAdminPartners } from '../hooks/useAdminDataAccess';
import { useAuth } from '../hooks/useAuth';
import { UserContextSwitcher, UserContextBadge } from '../components/admin/UserContextSwitcher';
import { Card, CardHeader, CardContent, Button, SearchInput, LoadingState, ErrorMessage, Table, type TableColumn, Modal } from '../components/ui';
import { PartnerAddModal } from '../components/partners/PartnerAddModal';
import { formatDate } from '../utils';
import { IPartner } from '@alom-rentals/shared';
import { Plus, Edit2, Trash2, Mail, User, Users } from 'lucide-react';

export const PartnersPage: React.FC = () => {
  const { user } = useAuth();

  // Admin context state
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>();

  // Data fetching - use admin hook if admin, regular hook otherwise
  const regularPartnersQuery = usePartners();
  const adminPartnersQuery = useAdminPartners(selectedUserId);

  const isAdmin = user?.role === 'ADMIN';
  const { partners, isLoading, error, deletePartner, isDeleting, bulkDeletePartners, isBulkDeleting } = isAdmin
    ? {
        partners: adminPartnersQuery.data || [],
        isLoading: adminPartnersQuery.isLoading,
        error: adminPartnersQuery.error,
        deletePartner: regularPartnersQuery.deletePartner, // Still use regular mutations
        isDeleting: regularPartnersQuery.isDeleting,
        bulkDeletePartners: regularPartnersQuery.bulkDeletePartners,
        isBulkDeleting: regularPartnersQuery.isBulkDeleting,
      }
    : regularPartnersQuery;

  // UI State
  const [showAddModal, setShowAddModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Selection state for bulk operations
  const [selectedPartners, setSelectedPartners] = useState<IPartner[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10; // 10 items per page for table view

  // Filtered partners
  const filteredPartners = useMemo(() => {
    if (!partners) return [];

    return partners.filter((partner) => {
      const matchesSearch =
        !searchQuery || partner.name.toLowerCase().includes(searchQuery.toLowerCase()) || partner.email.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesSearch;
    });
  }, [partners, searchQuery]);

  // Paginated partners
  const paginatedPartners = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredPartners.slice(startIndex, startIndex + pageSize);
  }, [filteredPartners, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredPartners.length / pageSize);

  // Event handlers
  const handleDeletePartner = async (partner: IPartner) => {
    if (window.confirm(`Are you sure you want to delete ${partner.name}?`)) {
      await deletePartner(partner._id);
    }
  };

  const handleAddSuccess = () => {
    setShowAddModal(false);
  };

  // Bulk operation handlers
  const handleBulkDelete = () => {
    if (selectedPartners.length === 0) return;
    setShowBulkDeleteConfirm(true);
  };

  const confirmBulkDelete = async () => {
    if (selectedPartners.length === 0) return;

    const partnerIds = selectedPartners.map((p) => p._id);
    const result = await bulkDeletePartners(partnerIds);

    if (result.success) {
      setSelectedPartners([]);
      setShowBulkDeleteConfirm(false);

      // Reset to page 1 if current page becomes empty
      const remainingItems = filteredPartners.length - selectedPartners.length;
      const maxPage = Math.ceil(remainingItems / pageSize);
      if (currentPage > maxPage && maxPage > 0) {
        setCurrentPage(1);
      }
    }
  };

  const handleSelectAll = () => {
    setSelectedPartners([...paginatedPartners]);
  };

  const handleSelectNone = () => {
    setSelectedPartners([]);
  };

  // Table columns
  const columns: TableColumn<IPartner>[] = [
    {
      key: 'name',
      header: 'Partner',
      render: (partner) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <User className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div className="font-medium text-gray-900">{partner.name}</div>
            <div className="text-sm text-gray-500 flex items-center">
              <Mail className="w-3 h-3 mr-1" />
              {partner.email}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'createdAt',
      header: 'Added',
      render: (partner) => <div className="text-sm text-gray-900">{formatDate(partner.createdAt)}</div>,
    },
    ...(isAdmin
      ? [
          {
            key: 'createdBy',
            header: 'Created By',
            render: (partner: IPartner & { createdBy?: { fullName?: string; email?: string } }) => (
              <div className="text-sm">
                <div className="font-medium text-gray-900">{partner.createdBy?.fullName || 'Unknown'}</div>
                <div className="text-gray-500">{partner.createdBy?.email || ''}</div>
              </div>
            ),
          },
        ]
      : []),
    {
      key: 'actions',
      header: 'Actions',
      render: (partner) => (
        <div className="flex space-x-2">
          <Button
            variant="ghost"
            size="sm"
            icon={Edit2}
            onClick={() => {
              // TODO: Implement edit functionality
              console.log('Edit partner:', partner._id);
            }}
          />
          <Button
            variant="ghost"
            size="sm"
            icon={Trash2}
            onClick={() => handleDeletePartner(partner)}
            loading={isDeleting}
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          />
        </div>
      ),
    },
  ];

  // Loading state
  if (isLoading) {
    return <LoadingState message="Loading partners..." />;
  }

  // Error state
  if (error) {
    return <ErrorMessage message="Failed to load partners" />;
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Partners</h1>
          <p className="text-gray-600 mt-1">{isAdmin ? 'Manage partners across all users' : 'Manage your business partners'}</p>
        </div>
        <div className="flex items-center space-x-4">
          {isAdmin && (
            <div className="flex items-center space-x-3">
              <UserContextBadge selectedUserId={selectedUserId} />
              <UserContextSwitcher selectedUserId={selectedUserId} onUserChange={setSelectedUserId} className="w-64" />
            </div>
          )}
          <Button icon={Plus} onClick={() => setShowAddModal(true)}>
            Add Partner
          </Button>
        </div>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <SearchInput
            placeholder="Search partners by name or email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onClear={() => setSearchQuery('')}
          />
        </CardContent>
      </Card>

      {/* Partners Table */}
      <Card>
        <CardHeader title="Your Partners" icon={Users} />
        <CardContent className="p-0">
          {filteredPartners.length === 0 ? (
            <div className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No partners</h3>
              <p className="mt-1 text-sm text-gray-500">{searchQuery ? 'No partners match your search.' : 'Get started by adding your first partner.'}</p>
              {!searchQuery && (
                <div className="mt-6">
                  <Button icon={Plus} onClick={() => setShowAddModal(true)}>
                    Add Partner
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <>
              {/* Bulk Actions Bar */}
              {selectedPartners.length > 0 && (
                <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <span className="text-sm font-medium text-blue-900">{selectedPartners.length} selected</span>
                    <Button variant="secondary" size="sm" onClick={handleSelectAll}>
                      Select All
                    </Button>
                    <Button variant="secondary" size="sm" onClick={handleSelectNone}>
                      Select None
                    </Button>
                  </div>
                  <Button variant="danger" size="sm" icon={Trash2} onClick={handleBulkDelete} disabled={isBulkDeleting}>
                    Delete {selectedPartners.length}
                  </Button>
                </div>
              )}

              <Table
                columns={columns}
                data={paginatedPartners}
                selectable
                selectedItems={selectedPartners}
                onSelectionChange={setSelectedPartners}
                pagination={{
                  currentPage,
                  totalPages,
                  totalItems: filteredPartners.length,
                  onPageChange: setCurrentPage,
                }}
                emptyMessage="No partners found. Add your first partner to get started!"
              />
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Partner Modal */}
      <PartnerAddModal isOpen={showAddModal} onClose={() => setShowAddModal(false)} onSuccess={handleAddSuccess} />

      {/* Bulk Delete Confirmation Modal */}
      {showBulkDeleteConfirm && (
        <Modal isOpen={showBulkDeleteConfirm} onClose={() => setShowBulkDeleteConfirm(false)}>
          <div className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Delete Partners</h3>
            <p className="text-sm text-gray-500 mb-6">Are you sure you want to delete {selectedPartners.length} partners? This action cannot be undone.</p>
            <div className="flex justify-end space-x-3">
              <Button variant="secondary" onClick={() => setShowBulkDeleteConfirm(false)}>
                Cancel
              </Button>
              <Button variant="danger" onClick={confirmBulkDelete} disabled={isBulkDeleting}>
                Delete {selectedPartners.length} Partners
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

import React, { useState, useMemo } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useQueryState } from 'nuqs';
import { useShortTermRentals } from '../hooks/useShortTermRentals';
import { useAdminRentals } from '../hooks/useAdminDataAccess';
import { useAuth } from '../hooks/useAuth';
import { UserContextSwitcher, UserContextBadge } from '../components/admin/UserContextSwitcher';
import {
  Card,
  CardHeader,
  CardContent,
  Button,
  SearchInput,
  Select,
  LoadingState,
  Table,
  type TableColumn,
  ReadOnlyWrapper,
  CreateRentalButton,
  EditRentalButton,
  ViewButton,
} from '../components/ui';
import { ShortTermRentalForm } from '../components/rentals/ShortTermRentalForm';
import { RentalCalendar } from '../components/rentals/RentalCalendar';
import { RentalDetailModal } from '../components/rentals/RentalDetailModal';
import { formatDate, formatCurrency, RENTAL_STATUS_LABELS } from '../utils';
import { IShortTermRental } from '@alom-rentals/shared';
import { Plus, Edit2, Eye, Calendar, MapPin, List, Grid, Trash2 } from 'lucide-react';

export const RentalsPage: React.FC = () => {
  const { user } = useAuth();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();

  // Admin context state
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>();

  // Data fetching - use admin hook if admin, regular hook otherwise
  const regularRentalsQuery = useShortTermRentals();
  const adminRentalsQuery = useAdminRentals(selectedUserId);

  const isAdmin = user?.role === 'ADMIN';
  const {
    rentals,
    loading: isLoading,
    bulkDeleteRentals,
    isBulkDeleting,
  } = isAdmin
    ? {
        rentals: adminRentalsQuery.data || [],
        loading: adminRentalsQuery.isLoading,
        bulkDeleteRentals: regularRentalsQuery.bulkDeleteRentals, // Still use regular mutations
        isBulkDeleting: regularRentalsQuery.isBulkDeleting,
      }
    : regularRentalsQuery;

  // Determine current view from URL
  const isNewView = location.pathname === '/rentals/new';
  const isEditView = location.pathname.endsWith('/edit');
  const isDetailView = id && !isEditView;
  const isCalendarRoute = location.pathname === '/rentals/calendar';

  // UI State with URL state management
  const [view, setView] = useQueryState('view', {
    defaultValue: isCalendarRoute ? 'calendar' : 'table',
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // Selection state
  const [selectedRentals, setSelectedRentals] = useState<IShortTermRental[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [selectedRental, setSelectedRental] = useState<IShortTermRental | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  // Find current rental for detail/edit views
  const currentRental = useMemo(() => {
    if (!id || !rentals) return null;
    return rentals.find((r) => r._id === id) || null;
  }, [id, rentals]);

  // Filtered rentals for list view
  const filteredRentals = useMemo(() => {
    if (!rentals) return [];

    return rentals.filter((rental) => {
      const matchesSearch =
        !searchQuery ||
        rental.property?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        rental.guests.some(
          (g) => g.guest?.firstName.toLowerCase().includes(searchQuery.toLowerCase()) || g.guest?.lastName.toLowerCase().includes(searchQuery.toLowerCase())
        );

      const matchesStatus = !selectedStatus || rental.status === selectedStatus;

      return matchesSearch && matchesStatus;
    });
  }, [rentals, searchQuery, selectedStatus]);

  // Paginated rentals
  const paginatedRentals = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredRentals.slice(startIndex, endIndex);
  }, [filteredRentals, currentPage, pageSize]);

  // Pagination info
  const totalPages = Math.ceil(filteredRentals.length / pageSize);

  // Bulk delete handlers
  const handleBulkDelete = () => {
    if (selectedRentals.length === 0) return;
    setShowBulkDeleteConfirm(true);
  };

  const confirmBulkDelete = async () => {
    if (selectedRentals.length === 0) return;

    const rentalIds = selectedRentals.map((r) => r._id);
    bulkDeleteRentals(rentalIds);
    setSelectedRentals([]);
    setShowBulkDeleteConfirm(false);
    // Reset to first page if current page becomes empty
    if (paginatedRentals.length === selectedRentals.length && currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const cancelBulkDelete = () => {
    setShowBulkDeleteConfirm(false);
  };

  // Selection handlers
  const handleSelectionChange = (selected: IShortTermRental[]) => {
    setSelectedRentals(selected);
  };

  const handleSelectAll = () => {
    setSelectedRentals([...filteredRentals]);
  };

  const handleSelectNone = () => {
    setSelectedRentals([]);
  };

  // Pagination handler
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setSelectedRentals([]); // Clear selection when changing pages
  };

  // Event handlers
  const handleFormSuccess = () => {
    navigate('/rentals');
  };

  const handleFormCancel = () => {
    navigate('/rentals');
  };

  const handleRentalClick = (rental: IShortTermRental) => {
    setSelectedRental(rental);
    setIsDetailModalOpen(true);
  };

  const handleEditRental = (rental: IShortTermRental) => {
    setIsDetailModalOpen(false);
    navigate(`/rentals/${rental._id}/edit`);
  };

  const handleCloseDetailModal = () => {
    setIsDetailModalOpen(false);
    setSelectedRental(null);
  };

  const handleViewChange = (newView: string) => {
    if (newView === 'calendar') {
      navigate('/rentals/calendar');
    } else {
      navigate('/rentals');
    }
    setView(newView);
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Table columns
  const columns: TableColumn<IShortTermRental>[] = [
    {
      key: 'property',
      header: 'Property',
      render: (rental) => (
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <MapPin className="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <div className="font-medium text-gray-900">{rental.property?.name}</div>
            <div className="text-sm text-gray-500">{rental.property?.address}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'guests',
      header: 'Guests',
      render: (rental) => {
        const principalGuest = rental.guests.find((g) => g.isPrincipalContact);
        const guestCount = rental.guests.length;

        return (
          <div>
            <div className="font-medium text-gray-900">
              {principalGuest?.guest?.firstName} {principalGuest?.guest?.lastName}
            </div>
            <div className="text-sm text-gray-500">{guestCount === 1 ? '1 guest' : `${guestCount} guests`}</div>
          </div>
        );
      },
    },
    {
      key: 'dates',
      header: 'Dates',
      render: (rental) => (
        <div>
          <div className="text-sm text-gray-900">
            {formatDate(rental.startDate)} - {formatDate(rental.endDate)}
          </div>
          <div className="text-sm text-gray-500">
            {Math.ceil((new Date(rental.endDate).getTime() - new Date(rental.startDate).getTime()) / (1000 * 60 * 60 * 24))} nights
          </div>
        </div>
      ),
    },
    {
      key: 'amount',
      header: 'Amount',
      render: (rental) => (
        <div>
          <div className="font-medium text-gray-900">{formatCurrency(rental.rentalAmount)}</div>
          <div className="text-sm text-gray-500">Cost: {formatCurrency(rental.costAmount)}</div>
        </div>
      ),
    },
    {
      key: 'discounts',
      header: 'Discounts',
      render: (rental) => (
        <div>
          {rental.discountAmount && rental.discountAmount > 0 ? (
            <div className="text-sm text-red-600">-{formatCurrency(rental.discountAmount)}</div>
          ) : (
            <div className="text-sm text-gray-400">None</div>
          )}
        </div>
      ),
    },
    {
      key: 'commissions',
      header: 'Commissions',
      render: (rental) => {
        const totalCommissions =
          rental.commissions?.reduce((total, commission) => {
            if (commission.type === 'percentage') {
              return total + (rental.rentalAmount * commission.amount) / 100;
            }
            return total + commission.amount;
          }, 0) || 0;

        return (
          <div>
            {totalCommissions > 0 ? (
              <div className="text-sm text-orange-600">-{formatCurrency(totalCommissions)}</div>
            ) : (
              <div className="text-sm text-gray-400">None</div>
            )}
          </div>
        );
      },
    },
    {
      key: 'profit',
      header: 'Net Profit',
      render: (rental) => {
        const totalCommissions =
          rental.commissions?.reduce((total, commission) => {
            if (commission.type === 'percentage') {
              return total + (rental.rentalAmount * commission.amount) / 100;
            }
            return total + commission.amount;
          }, 0) || 0;

        const finalProfit = rental.rentalAmount - rental.costAmount - (rental.discountAmount || 0) - totalCommissions;

        return (
          <div>
            <div className={`font-medium ${finalProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>{formatCurrency(finalProfit)}</div>
            <div className="text-xs text-gray-500">After all deductions</div>
          </div>
        );
      },
    },
    {
      key: 'status',
      header: 'Status',
      render: (rental) => (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(rental.status)}`}>
          {RENTAL_STATUS_LABELS[rental.status as keyof typeof RENTAL_STATUS_LABELS]}
        </span>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (rental) => (
        <div className="flex space-x-2">
          <ViewButton variant="ghost" size="sm" icon={Eye} onClick={() => navigate(`/rentals/${rental._id}`)} />
          <EditRentalButton variant="ghost" size="sm" icon={Edit2} onClick={() => navigate(`/rentals/${rental._id}/edit`)} />
        </div>
      ),
    },
  ];

  // Render rental detail view (if we had one)
  if (isDetailView && currentRental) {
    // For now, redirect to edit since we don't have a detail view component
    navigate(`/rentals/${currentRental._id}/edit`);
    return null;
  }

  // Render rental edit form
  if (isEditView && currentRental) {
    return <ShortTermRentalForm rental={currentRental} onSuccess={handleFormSuccess} onCancel={handleFormCancel} />;
  }

  // Render rental create form
  if (isNewView) {
    return <ShortTermRentalForm onSuccess={handleFormSuccess} onCancel={handleFormCancel} />;
  }

  // Loading state
  if (isLoading) {
    return <LoadingState message="Loading rentals..." />;
  }

  return (
    <ReadOnlyWrapper message="You are viewing rentals in read-only mode. Contact an administrator to make changes.">
      <div className="p-6 space-y-6" data-testid="rentals-container">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Rentals</h1>
            <p className="text-gray-600 mt-1">{isAdmin ? 'Manage rentals across all users' : 'Manage your property rentals'}</p>
          </div>
          <div className="flex items-center space-x-3">
            {isAdmin && (
              <div className="flex items-center space-x-3">
                <UserContextBadge selectedUserId={selectedUserId} />
                <UserContextSwitcher selectedUserId={selectedUserId} onUserChange={setSelectedUserId} className="w-64" />
              </div>
            )}
            {/* View Toggle */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => handleViewChange('table')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  view === 'table' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <List className="w-4 h-4" />
                <span>Table</span>
              </button>
              <button
                onClick={() => handleViewChange('calendar')}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  view === 'calendar' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <Grid className="w-4 h-4" />
                <span>Calendar</span>
              </button>
            </div>

            <CreateRentalButton icon={Plus} onClick={() => navigate('/rentals/new')}>
              Create Rental
            </CreateRentalButton>
          </div>
        </div>

        {/* Conditional Content Based on View */}
        {view === 'calendar' ? (
          <RentalCalendar onRentalClick={handleRentalClick} />
        ) : (
          <>
            {/* Filters */}
            <Card>
              <CardContent className="p-4">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <SearchInput
                      placeholder="Search rentals..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      onClear={() => setSearchQuery('')}
                    />
                  </div>
                  <div className="sm:w-48">
                    <Select
                      placeholder="All Statuses"
                      value={selectedStatus}
                      onChange={setSelectedStatus}
                      options={[
                        { value: '', label: 'All Statuses' },
                        ...Object.entries(RENTAL_STATUS_LABELS).map(([value, label]) => ({
                          value,
                          label,
                        })),
                      ]}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Bulk Actions */}
            {selectedRentals.length > 0 && (
              <Card>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <span className="text-sm text-gray-600">{selectedRentals.length} selected</span>
                      <Button variant="secondary" size="sm" onClick={handleSelectAll}>
                        Select All
                      </Button>
                      <Button variant="secondary" size="sm" onClick={handleSelectNone}>
                        Select None
                      </Button>
                    </div>
                    <Button variant="danger" size="sm" icon={Trash2} onClick={handleBulkDelete} disabled={isBulkDeleting}>
                      Delete {selectedRentals.length}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Rentals Table */}
            <Card>
              <CardHeader title="Your Rentals" icon={Calendar} />
              <CardContent className="p-0">
                <Table
                  data={paginatedRentals}
                  columns={columns}
                  emptyMessage="No rentals found. Create your first rental to get started!"
                  selectable
                  selectedItems={selectedRentals}
                  onSelectionChange={handleSelectionChange}
                  pagination={{
                    currentPage,
                    totalPages,
                    onPageChange: handlePageChange,
                    totalItems: filteredRentals.length,
                  }}
                />
              </CardContent>
            </Card>
          </>
        )}

        {/* Rental Detail Modal */}
        <RentalDetailModal rental={selectedRental} isOpen={isDetailModalOpen} onClose={handleCloseDetailModal} onEdit={handleEditRental} />

        {/* Bulk Delete Confirmation Modal */}
        {showBulkDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Rentals</h3>
              <p className="text-gray-600 mb-4">Are you sure you want to delete {selectedRentals.length} rentals? This action cannot be undone.</p>
              <div className="flex justify-end space-x-3">
                <Button variant="secondary" onClick={cancelBulkDelete}>
                  Cancel
                </Button>
                <Button variant="danger" onClick={confirmBulkDelete} disabled={isBulkDeleting}>
                  Delete {selectedRentals.length} Rentals
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </ReadOnlyWrapper>
  );
};

import React, { useState, useMemo } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useProperties } from '../hooks/useProperties';
import { useAdminProperties } from '../hooks/useAdminDataAccess';
import { UserContextSwitcher, UserContextBadge } from '../components/admin/UserContextSwitcher';
import {
  Card,
  CardHeader,
  CardContent,
  Button,
  SearchInput,
  Select,
  LoadingState,
  ErrorMessage,
  Table,
  type TableColumn,
  ReadOnlyWrapper,
  CreatePropertyButton,
  EditPropertyButton,
  DeletePropertyButton,
  ViewButton,
} from '../components/ui';
import { PropertyForm } from '../components/properties/PropertyForm';
import { PropertyEditForm } from '../components/properties/PropertyEditForm';
import { PropertyDetailPage } from '../components/properties/PropertyDetailPage';
import { formatPercentage, formatCurrency, PROPERTY_TYPE_LABELS } from '../utils';
import { IProperty, IPropertyOwner, IUserPublic, IPartner } from '@alom-rentals/shared';
import { Plus, Eye, Edit, Home, Trash2, DollarSign } from 'lucide-react';

export const PropertiesPage: React.FC = () => {
  const { user } = useAuth();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();

  // Admin context state
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>();

  // Data fetching - use admin hook if admin, regular hook otherwise
  const regularPropertiesQuery = useProperties();
  const adminPropertiesQuery = useAdminProperties(selectedUserId);

  const isAdmin = user?.role === 'ADMIN';
  const { properties, isLoading, error, deleteProperty, bulkDeleteProperties, isDeleting, isBulkDeleting } = isAdmin
    ? {
        properties: adminPropertiesQuery.data || [],
        isLoading: adminPropertiesQuery.isLoading,
        error: adminPropertiesQuery.error,
        deleteProperty: regularPropertiesQuery.deleteProperty, // Still use regular mutations
        bulkDeleteProperties: regularPropertiesQuery.bulkDeleteProperties,
        isDeleting: regularPropertiesQuery.isDeleting,
        isBulkDeleting: regularPropertiesQuery.isBulkDeleting,
      }
    : regularPropertiesQuery;

  // Determine current view from URL
  const isNewView = location.pathname === '/properties/new';
  const isEditView = location.pathname.endsWith('/edit');
  const isDetailView = id && !isEditView;

  // UI State
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [deleteConfirmProperty, setDeleteConfirmProperty] = useState<IProperty | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // Selection state
  const [selectedProperties, setSelectedProperties] = useState<IProperty[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Find current property for detail/edit views
  const currentProperty = useMemo(() => {
    if (!id || !properties) return null;
    return properties.find((p) => p._id === id) || null;
  }, [id, properties]);

  // Filtered properties for list view
  const filteredProperties = useMemo(() => {
    if (!properties) return [];

    return properties.filter((property) => {
      const matchesSearch =
        !searchQuery || property.name.toLowerCase().includes(searchQuery.toLowerCase()) || property.address.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesType = !selectedType || property.propertyType === selectedType;

      return matchesSearch && matchesType;
    });
  }, [properties, searchQuery, selectedType]);

  // Paginated properties
  const paginatedProperties = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredProperties.slice(startIndex, endIndex);
  }, [filteredProperties, currentPage, pageSize]);

  // Pagination info
  const totalPages = Math.ceil(filteredProperties.length / pageSize);

  // Total cost calculation
  const totalCost = useMemo(() => {
    return filteredProperties.reduce((sum, property) => sum + (property.totalMonthlyCost || 0), 0);
  }, [filteredProperties]);

  // Table columns
  const columns: TableColumn<IProperty>[] = [
    {
      key: 'name',
      header: 'Property Name',
      render: (property) => (
        <div>
          <div className="font-medium text-gray-900">{property.name}</div>
          <div className="text-sm text-gray-500">{property.address}</div>
        </div>
      ),
    },
    {
      key: 'propertyType',
      header: 'Type',
      render: (property) => (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          {PROPERTY_TYPE_LABELS[property.propertyType as keyof typeof PROPERTY_TYPE_LABELS]}
        </span>
      ),
    },
    {
      key: 'ownership',
      header: isAdmin ? 'Ownership' : 'Your Ownership',
      render: (property) => {
        if (isAdmin) {
          // For admin, show the primary owner
          const primaryOwner = property.owners.find((o: IPropertyOwner) => o.ownershipPercentage > 0);
          if (primaryOwner) {
            const ownerName =
              primaryOwner.ownerType === 'User'
                ? (primaryOwner.owner as IUserPublic)?.fullName || (primaryOwner.owner as IUserPublic)?.email
                : (primaryOwner.owner as IPartner)?.name || (primaryOwner.owner as IPartner)?.email;
            return `${ownerName} (${formatPercentage(primaryOwner.ownershipPercentage)})`;
          }
          return 'No owner';
        } else {
          // For regular users, show their ownership
          const userOwnership =
            property.owners.find((o: IPropertyOwner) => o.ownerType === 'User' && (o.owner as IUserPublic)?.id === user?.id)?.ownershipPercentage || 0;
          return formatPercentage(userOwnership);
        }
      },
    },
    {
      key: 'partners',
      header: isAdmin ? 'All Owners' : 'Partners',
      render: (property) => {
        if (isAdmin) {
          // For admin, show all owners
          const allOwners = property.owners.filter((owner: IPropertyOwner) => owner.ownershipPercentage > 0);

          if (allOwners.length === 0) return 'No owners';

          return (
            <div className="space-y-1">
              {allOwners.slice(0, 2).map((owner: IPropertyOwner, index: number) => (
                <div key={index} className="text-sm">
                  {owner.ownerType === 'User'
                    ? (owner.owner as IUserPublic).fullName || (owner.owner as IUserPublic).email
                    : (owner.owner as IPartner).name || (owner.owner as IPartner).email}{' '}
                  ({formatPercentage(owner.ownershipPercentage)})
                </div>
              ))}
              {allOwners.length > 2 && <div className="text-xs text-gray-500">+{allOwners.length - 2} more</div>}
            </div>
          );
        } else {
          // For regular users, show partners (excluding themselves)
          const partners = property.owners.filter((owner: IPropertyOwner) =>
            owner.ownerType === 'User' ? (owner.owner as IUserPublic).id !== user?.id : (owner.owner as IPartner)._id !== user?.id
          );

          if (partners.length === 0) return 'None';

          return (
            <div className="space-y-1">
              {partners.slice(0, 2).map((owner: IPropertyOwner, index: number) => (
                <div key={index} className="text-sm">
                  {owner.ownerType === 'User'
                    ? (owner.owner as IUserPublic).fullName || owner.owner.email
                    : (owner.owner as IPartner).name || owner.owner.email}{' '}
                  ({formatPercentage(owner.ownershipPercentage)})
                </div>
              ))}
              {partners.length > 2 && <div className="text-xs text-gray-500">+{partners.length - 2} more</div>}
            </div>
          );
        }
      },
    },
    {
      key: 'totalCost',
      header: 'Monthly Cost',
      render: (property) => (
        <div className="flex items-center space-x-1">
          <DollarSign className="w-4 h-4 text-green-600" />
          <span className="font-medium text-green-600">{formatCurrency(property.totalMonthlyCost || 0)}</span>
        </div>
      ),
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (property) => (
        <div className="flex space-x-2">
          <ViewButton variant="ghost" size="sm" icon={Eye} onClick={() => navigate(`/properties/${property._id}`)} />
          <EditPropertyButton variant="ghost" size="sm" icon={Edit} onClick={() => navigate(`/properties/${property._id}/edit`)} />
          <DeletePropertyButton
            variant="ghost"
            size="sm"
            icon={Trash2}
            onClick={() => handleDeleteProperty(property)}
            className="text-red-600 hover:text-red-700"
          />
        </div>
      ),
    },
  ];

  // Event handlers
  const handleFormSuccess = () => {
    navigate('/properties');
  };

  const handleFormCancel = () => {
    navigate('/properties');
  };

  const handleDeleteProperty = async (property: IProperty) => {
    setDeleteConfirmProperty(property);
  };

  const confirmDeleteProperty = async () => {
    if (!deleteConfirmProperty) return;

    const result = await deleteProperty(deleteConfirmProperty._id);
    if (result.success) {
      setDeleteConfirmProperty(null);
    } else {
      // Handle error - could show a toast or error message
      console.error('Failed to delete property:', result.error?.message);
    }
  };

  const cancelDeleteProperty = () => {
    setDeleteConfirmProperty(null);
  };

  // Bulk delete handlers
  const handleBulkDelete = () => {
    if (selectedProperties.length === 0) return;
    setShowBulkDeleteConfirm(true);
  };

  const confirmBulkDelete = async () => {
    if (selectedProperties.length === 0) return;

    const propertyIds = selectedProperties.map((p) => p._id);
    const result = await bulkDeleteProperties(propertyIds);

    if (result.success) {
      setSelectedProperties([]);
      setShowBulkDeleteConfirm(false);
      // Reset to first page if current page becomes empty
      if (paginatedProperties.length === selectedProperties.length && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      }
    } else {
      console.error('Failed to delete properties:', result.error?.message);
    }
  };

  const cancelBulkDelete = () => {
    setShowBulkDeleteConfirm(false);
  };

  // Selection handlers
  const handleSelectionChange = (selected: IProperty[]) => {
    setSelectedProperties(selected);
  };

  const handleSelectAll = () => {
    setSelectedProperties([...filteredProperties]);
  };

  const handleSelectNone = () => {
    setSelectedProperties([]);
  };

  // Pagination handler
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    setSelectedProperties([]); // Clear selection when changing pages
  };

  // Render property detail view
  if (isDetailView && currentProperty) {
    return <PropertyDetailPage propertyId={currentProperty._id} onBack={() => navigate('/properties')} />;
  }

  // Render property edit form
  if (isEditView && currentProperty) {
    return <PropertyEditForm property={currentProperty} onSuccess={handleFormSuccess} onCancel={handleFormCancel} />;
  }

  // Render property create form
  if (isNewView) {
    return <PropertyForm onSuccess={handleFormSuccess} onCancel={handleFormCancel} />;
  }

  // Loading state
  if (isLoading) {
    return <LoadingState message="Loading properties..." />;
  }

  // Error state
  if (error) {
    return <ErrorMessage message="Failed to load properties" />;
  }

  // Main properties list view
  return (
    <ReadOnlyWrapper message="You are viewing properties in read-only mode. Contact an administrator to make changes.">
      <div className="p-6 space-y-6" data-testid="properties-list">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Properties</h1>
            <p className="text-gray-600 mt-1">{isAdmin ? 'Manage properties across all users' : 'Manage your property portfolio'}</p>
          </div>
          <div className="flex items-center space-x-4">
            {isAdmin && (
              <div className="flex items-center space-x-3">
                <UserContextBadge selectedUserId={selectedUserId} />
                <UserContextSwitcher selectedUserId={selectedUserId} onUserChange={setSelectedUserId} className="w-64" />
              </div>
            )}
            <CreatePropertyButton icon={Plus} onClick={() => navigate('/properties/new')}>
              Add Property
            </CreatePropertyButton>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <SearchInput
                  placeholder="Search properties..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onClear={() => setSearchQuery('')}
                />
              </div>
              <div className="sm:w-48">
                <Select
                  placeholder="All Types"
                  value={selectedType}
                  onChange={setSelectedType}
                  options={[
                    { value: '', label: 'All Types' },
                    ...Object.entries(PROPERTY_TYPE_LABELS).map(([value, label]) => ({
                      value,
                      label,
                    })),
                  ]}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Summary and Actions Bar */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center space-x-6">
                <div className="text-sm text-gray-600">
                  <span className="font-medium">{filteredProperties.length}</span> properties found
                </div>
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-green-600">Total Monthly Cost: {formatCurrency(totalCost)}</span>
                </div>
              </div>

              {selectedProperties.length > 0 && (
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-gray-600">{selectedProperties.length} selected</span>
                  <Button variant="secondary" size="sm" onClick={handleSelectAll}>
                    Select All
                  </Button>
                  <Button variant="secondary" size="sm" onClick={handleSelectNone}>
                    Select None
                  </Button>
                  <Button variant="danger" size="sm" onClick={handleBulkDelete} disabled={isBulkDeleting} icon={Trash2}>
                    {isBulkDeleting ? 'Deleting...' : `Delete ${selectedProperties.length}`}
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Properties Table */}
        <Card>
          <CardHeader title="Your Properties" icon={Home} />
          <CardContent className="p-0">
            <Table
              data={paginatedProperties}
              columns={columns}
              emptyMessage="No properties found. Add your first property to get started!"
              selectable={true}
              selectedItems={selectedProperties}
              onSelectionChange={handleSelectionChange}
              getItemId={(property) => property._id}
              pagination={{
                currentPage,
                totalPages,
                onPageChange: handlePageChange,
                pageSize,
                totalItems: filteredProperties.length,
              }}
            />
          </CardContent>
        </Card>

        {/* Delete Confirmation Modal */}
        {deleteConfirmProperty && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Property</h3>
              <p className="text-gray-600 mb-6">Are you sure you want to delete "{deleteConfirmProperty.name}"? This action cannot be undone.</p>
              <div className="flex justify-end space-x-3">
                <Button variant="secondary" onClick={cancelDeleteProperty} disabled={isDeleting}>
                  Cancel
                </Button>
                <Button variant="danger" onClick={confirmDeleteProperty} disabled={isDeleting}>
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Bulk Delete Confirmation Modal */}
        {showBulkDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Properties</h3>
              <p className="text-gray-600 mb-6">Are you sure you want to delete {selectedProperties.length} properties? This action cannot be undone.</p>
              <div className="flex justify-end space-x-3">
                <Button variant="secondary" onClick={cancelBulkDelete} disabled={isBulkDeleting}>
                  Cancel
                </Button>
                <Button variant="danger" onClick={confirmBulkDelete} disabled={isBulkDeleting}>
                  {isBulkDeleting ? 'Deleting...' : `Delete ${selectedProperties.length} Properties`}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </ReadOnlyWrapper>
  );
};

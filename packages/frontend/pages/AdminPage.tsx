import React from 'react';
import { AdminOnly } from '../components/admin/RoleGuard';
import { OperatorAssignments } from '../components/admin/OperatorAssignments';

const AdminPage: React.FC = () => {
  return (
    <AdminOnly>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="text-gray-600 mt-2">Manage users, roles, and system settings</p>
        </div>

        <div className="space-y-8">
          <OperatorAssignments />
        </div>
      </div>
    </AdminOnly>
  );
};

export default AdminPage;

import React, { useMemo, useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useQueryState } from 'nuqs';
import { useGuests } from '../hooks/useGuests';
import { useAdminGuests } from '../hooks/useAdminDataAccess';
import { useAuth } from '../hooks/useAuth';
import { UserContextSwitcher, UserContextBadge } from '../components/admin/UserContextSwitcher';
import {
  Card,
  CardHeader,
  CardContent,
  Button,
  SearchInput,
  LoadingState,
  Table,
  type TableColumn,
  Modal,
  ReadOnlyWrapper,
  CreateGuestButton,
  EditGuestButton,
  ViewButton,
} from '../components/ui';
import { GuestForm } from '../components/guests/GuestForm';
import { IGuest } from '@alom-rentals/shared';
import { Plus, Edit2, Eye, User, Users, Trash2 } from 'lucide-react';

export const GuestsPage: React.FC = () => {
  const { user } = useAuth();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();

  // Admin context state
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>();

  // Data fetching - use admin hook if admin, regular hook otherwise
  const regularGuestsQuery = useGuests();
  const adminGuestsQuery = useAdminGuests(selectedUserId);

  const isAdmin = user?.role === 'ADMIN';
  const { guests, isLoading, bulkDeleteGuests, isBulkDeleting } = isAdmin
    ? {
        guests: adminGuestsQuery.data || [],
        isLoading: adminGuestsQuery.isLoading,
        bulkDeleteGuests: regularGuestsQuery.bulkDeleteGuests, // Still use regular mutations
        isBulkDeleting: regularGuestsQuery.isBulkDeleting,
      }
    : regularGuestsQuery;

  // Determine current view from URL
  const isNewView = location.pathname === '/guests/new';
  const isEditView = location.pathname.endsWith('/edit');
  const isDetailView = id && !isEditView;

  // UI State
  const [searchQuery, setSearchQuery] = useQueryState('search', { defaultValue: '' });

  // Selection state for bulk operations
  const [selectedGuests, setSelectedGuests] = useState<IGuest[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10; // 10 items per page for table view

  // Find current guest for detail/edit views
  const currentGuest = useMemo(() => {
    if (!id || !guests) return null;
    return guests.find((g) => g._id === id) || null;
  }, [id, guests]);

  // Filtered guests for list view
  const filteredGuests = useMemo(() => {
    if (!guests) return [];

    return guests.filter((guest) => {
      const matchesSearch =
        !searchQuery ||
        guest.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        guest.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        guest.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        guest.phoneNumber?.includes(searchQuery);

      return matchesSearch;
    });
  }, [guests, searchQuery]);

  // Paginated guests
  const paginatedGuests = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    return filteredGuests.slice(startIndex, startIndex + pageSize);
  }, [filteredGuests, currentPage, pageSize]);

  const totalPages = Math.ceil(filteredGuests.length / pageSize);

  // Event handlers
  const handleFormSuccess = () => {
    navigate('/guests');
  };

  const handleFormCancel = () => {
    navigate('/guests');
  };

  // Bulk operation handlers
  const handleBulkDelete = () => {
    if (selectedGuests.length === 0) return;
    setShowBulkDeleteConfirm(true);
  };

  const confirmBulkDelete = async () => {
    if (selectedGuests.length === 0) return;

    const guestIds = selectedGuests.map((g) => g._id);
    const result = await bulkDeleteGuests(guestIds);

    if (result.success) {
      setSelectedGuests([]);
      setShowBulkDeleteConfirm(false);

      // Reset to page 1 if current page becomes empty
      const remainingItems = filteredGuests.length - selectedGuests.length;
      const maxPage = Math.ceil(remainingItems / pageSize);
      if (currentPage > maxPage && maxPage > 0) {
        setCurrentPage(1);
      }
    }
  };

  const handleSelectAll = () => {
    setSelectedGuests([...paginatedGuests]);
  };

  const handleSelectNone = () => {
    setSelectedGuests([]);
  };

  // Table columns
  const columns: TableColumn<IGuest>[] = [
    {
      key: 'name',
      header: 'Name',
      render: (guest) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-blue-600" />
            </div>
          </div>
          <div>
            <div className="text-sm font-medium text-gray-900">
              {guest.firstName} {guest.lastName}
            </div>
            <div className="text-sm text-gray-500">{guest.email}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'type',
      header: 'Type',
      render: (guest) => (
        <span
          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
            !guest.isForeigner ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
          }`}
        >
          {!guest.isForeigner ? 'National' : 'Foreign'}
        </span>
      ),
    },
    {
      key: 'document',
      header: 'Document',
      render: (guest) => (
        <div>
          <div className="text-sm text-gray-900">{guest.documentNumber}</div>
          <div className="text-sm text-gray-500">{guest.documentType}</div>
        </div>
      ),
    },
    {
      key: 'phone',
      header: 'Phone',
      render: (guest) => guest.phoneNumber || '-',
    },
    {
      key: 'nationality',
      header: 'Nationality',
      render: (guest) => guest.nationality || '-',
    },
    ...(isAdmin
      ? [
          {
            key: 'createdBy',
            header: 'Created By',
            render: (guest: IGuest & { createdBy?: { fullName?: string; email?: string } }) => (
              <div className="text-sm">
                <div className="font-medium text-gray-900">{guest.createdBy?.fullName || 'Unknown'}</div>
                <div className="text-gray-500">{guest.createdBy?.email || ''}</div>
              </div>
            ),
          },
        ]
      : []),
    {
      key: 'actions',
      header: 'Actions',
      render: (guest) => (
        <div className="flex space-x-2">
          <ViewButton variant="ghost" size="sm" icon={Eye} onClick={() => navigate(`/guests/${guest._id}`)} />
          <EditGuestButton variant="ghost" size="sm" icon={Edit2} onClick={() => navigate(`/guests/${guest._id}/edit`)} />
        </div>
      ),
    },
  ];

  // Render guest detail view (if we had one)
  if (isDetailView && currentGuest) {
    // For now, redirect to edit since we don't have a detail view component
    navigate(`/guests/${currentGuest._id}/edit`);
    return null;
  }

  // Render guest edit form
  if (isEditView && currentGuest) {
    return <GuestForm guest={currentGuest} onSuccess={handleFormSuccess} onCancel={handleFormCancel} />;
  }

  // Render guest create form
  if (isNewView) {
    return <GuestForm onSuccess={handleFormSuccess} onCancel={handleFormCancel} />;
  }

  // Loading state
  if (isLoading) {
    return <LoadingState message="Loading guests..." />;
  }

  // Main list view
  return (
    <ReadOnlyWrapper message="You are viewing guests in read-only mode. Contact an administrator to make changes.">
      <div className="p-6" data-testid="guests-list">
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-3">
                <Users className="w-6 h-6 text-blue-600" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Guests</h1>
                  <p className="text-sm text-gray-600 mt-1">{isAdmin ? 'Manage guests across all users' : 'Manage your guest contacts'}</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                {isAdmin && (
                  <div className="flex items-center space-x-3">
                    <UserContextBadge selectedUserId={selectedUserId} />
                    <UserContextSwitcher selectedUserId={selectedUserId} onUserChange={setSelectedUserId} className="w-64" />
                  </div>
                )}
                <CreateGuestButton icon={Plus} onClick={() => navigate('/guests/new')}>
                  Add Guest
                </CreateGuestButton>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="mb-6">
              <SearchInput
                placeholder="Search guests..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onClear={() => setSearchQuery('')}
              />
            </div>

            {filteredGuests.length === 0 ? (
              <div className="text-center py-12">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No guests</h3>
                <p className="mt-1 text-sm text-gray-500">{searchQuery ? 'No guests match your search.' : 'Get started by adding your first guest.'}</p>
                {!searchQuery && (
                  <div className="mt-6">
                    <CreateGuestButton icon={Plus} onClick={() => navigate('/guests/new')}>
                      Add Guest
                    </CreateGuestButton>
                  </div>
                )}
              </div>
            ) : (
              <>
                {/* Bulk Actions Bar */}
                {selectedGuests.length > 0 && (
                  <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <span className="text-sm font-medium text-blue-900">{selectedGuests.length} selected</span>
                      <Button variant="secondary" size="sm" onClick={handleSelectAll}>
                        Select All
                      </Button>
                      <Button variant="secondary" size="sm" onClick={handleSelectNone}>
                        Select None
                      </Button>
                    </div>
                    <Button variant="danger" size="sm" icon={Trash2} onClick={handleBulkDelete} disabled={isBulkDeleting}>
                      Delete {selectedGuests.length}
                    </Button>
                  </div>
                )}

                <Table
                  columns={columns}
                  data={paginatedGuests}
                  selectable
                  selectedItems={selectedGuests}
                  onSelectionChange={setSelectedGuests}
                  pagination={{
                    currentPage,
                    totalPages,
                    totalItems: filteredGuests.length,
                    onPageChange: setCurrentPage,
                  }}
                />
              </>
            )}
          </CardContent>
        </Card>

        {/* Bulk Delete Confirmation Modal */}
        {showBulkDeleteConfirm && (
          <Modal isOpen={showBulkDeleteConfirm} onClose={() => setShowBulkDeleteConfirm(false)}>
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Delete Guests</h3>
              <p className="text-sm text-gray-500 mb-6">Are you sure you want to delete {selectedGuests.length} guests? This action cannot be undone.</p>
              <div className="flex justify-end space-x-3">
                <Button variant="secondary" onClick={() => setShowBulkDeleteConfirm(false)}>
                  Cancel
                </Button>
                <Button variant="danger" onClick={confirmBulkDelete} disabled={isBulkDeleting}>
                  Delete {selectedGuests.length} Guests
                </Button>
              </div>
            </div>
          </Modal>
        )}
      </div>
    </ReadOnlyWrapper>
  );
};

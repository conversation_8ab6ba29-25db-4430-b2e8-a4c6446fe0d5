{"root": ["./app.tsx", "./main.tsx", "./playwright.config.ts", "./vite-env.d.ts", "./vite.config.ts", "./api/admin.ts", "./components/admin/bulkassignmentmodal.tsx", "./components/admin/operatorassignmentmodal.tsx", "./components/admin/operatorassignments.tsx", "./components/admin/passwordmanagement.tsx", "./components/admin/rolebadge.tsx", "./components/admin/roleguard.tsx", "./components/admin/roleselector.tsx", "./components/admin/usercontextswitcher.tsx", "./components/auth/authform.tsx", "./components/auth/authprovider.tsx", "./components/currencies/currenciespage.tsx", "./components/dashboard/dashboard.tsx", "./components/guests/guestform.tsx", "./components/guests/guestspage.tsx", "./components/layout/layout.tsx", "./components/layout/sidebar.tsx", "./components/operator/customerselector.tsx", "./components/partners/partneraddmodal.tsx", "./components/partners/partnerspage.tsx", "./components/properties/propertiespage.tsx", "./components/properties/propertydetailpage.tsx", "./components/properties/propertyeditform.tsx", "./components/properties/propertyform.tsx", "./components/properties/propertyowners.tsx", "./components/rentals/commissionmanager.tsx", "./components/rentals/guestselectionmodal.tsx", "./components/rentals/rentalcalendar.tsx", "./components/rentals/rentaldetailmodal.tsx", "./components/rentals/rentalguestmanager.tsx", "./components/rentals/rentalspage.tsx", "./components/rentals/shorttermrentalform.tsx", "./components/services/propertyservices.tsx", "./components/services/serviceform.tsx", "./components/services/serviceitem.tsx", "./components/services/servicelist.tsx", "./components/transactions/transactionform.tsx", "./components/ui/button.tsx", "./components/ui/calendar.tsx", "./components/ui/card.tsx", "./components/ui/checkbox.tsx", "./components/ui/errormessage.tsx", "./components/ui/input.tsx", "./components/ui/loadingspinner.tsx", "./components/ui/modal.tsx", "./components/ui/pagination.tsx", "./components/ui/readonlymessage.tsx", "./components/ui/rolebasedbutton.tsx", "./components/ui/searchinput.tsx", "./components/ui/select.tsx", "./components/ui/table.tsx", "./components/ui/index.ts", "./contexts/customercontext.tsx", "./contexts/customercontextdefinition.ts", "./e2e/bulk-operations.spec.ts", "./e2e/bulk-property-operations.spec.ts", "./e2e/dashboard.spec.ts", "./e2e/global-setup.ts", "./e2e/global-teardown.ts", "./e2e/navigation-integration.spec.ts", "./e2e/profile-management.spec.ts", "./e2e/property-management.spec.ts", "./e2e/rental-management.spec.ts", "./e2e/simple-bulk-delete.spec.ts", "./e2e/simple-role-test.spec.ts", "./e2e/user-account-management.spec.ts", "./e2e/user-roles-permissions.spec.ts", "./e2e/fixtures/auth.ts", "./e2e/fixtures/testdata.ts", "./e2e/pages/basepage.ts", "./e2e/pages/dashboardpage.ts", "./e2e/pages/profilepage.ts", "./e2e/pages/propertiespage.ts", "./e2e/pages/rentalspage.ts", "./hooks/useadmindataaccess.ts", "./hooks/useauth.ts", "./hooks/usecurrencies.ts", "./hooks/usecustomerutils.ts", "./hooks/usedashboard.ts", "./hooks/useguests.ts", "./hooks/usemonthlyrentals.ts", "./hooks/useoperatorassignments.ts", "./hooks/usepartners.ts", "./hooks/usepasswordmanagement.ts", "./hooks/useproperties.ts", "./hooks/usepropertyservices.ts", "./hooks/userolemanagement.ts", "./hooks/useshorttermrentalcost.ts", "./hooks/useshorttermrentals.ts", "./hooks/usetransactions.ts", "./lib/api.ts", "./lib/errorutils.ts", "./lib/queryclient.ts", "./lib/servicecalculations.ts", "./pages/adminpage.tsx", "./pages/dashboardpage.tsx", "./pages/guestspage.tsx", "./pages/partnerspage.tsx", "./pages/propertiespage.tsx", "./pages/rentalspage.tsx", "./pages/transactionspage.tsx", "./pages/index.ts", "./router/routes.tsx", "./utils/calendar.ts", "./utils/constants.ts", "./utils/formatters.ts", "./utils/helpers.ts", "./utils/index.ts", "./utils/roleguardutils.tsx", "./utils/validation.ts"], "errors": true, "version": "5.8.3"}
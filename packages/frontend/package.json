{"name": "@alom-rentals/frontend", "version": "1.0.0", "description": "Frontend React app for Alom Rentals", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint .", "clean": "rm -rf dist", "test": "vitest --run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:e2e:report": "playwright show-report"}, "dependencies": {"@alom-rentals/shared": "1.0.0", "@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "axios": "^1.7.7", "clsx": "^2.1.1", "lucide-react": "^0.446.0", "nuqs": "^2.4.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-redux": "^9.1.2", "react-router-dom": "^7.7.0", "recharts": "^2.12.7", "zod": "^4.0.17"}, "devDependencies": {"@eslint/js": "^9.11.1", "@playwright/test": "^1.54.1", "@reduxjs/toolkit": "^2.2.7", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.10", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "eslint": "^9.11.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.12", "globals": "^15.9.0", "jsdom": "^26.1.0", "postcss": "^8.4.47", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwindcss": "^3.4.13", "typescript": "^5.6.2", "typescript-eslint": "^8.7.0", "vite": "^5.4.8"}}
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '../utils';
import { PropertiesPage } from '../../pages/PropertiesPage';
import { useAuth } from '../../hooks/useAuth';
import { useProperties } from '../../hooks/useProperties';
import { mockUser } from '../utils';

// Mock the hooks
vi.mock('../../hooks/useAuth');
vi.mock('../../hooks/useProperties');

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  useParams: () => ({}),
  useNavigate: () => vi.fn(),
  useLocation: () => ({ pathname: '/properties' }),
}));

describe('Properties Cost Calculation Integration', () => {
  const mockPropertiesWithCosts = [
    {
      _id: 'property1',
      name: 'Apartment Complex',
      address: '123 Main St',
      propertyType: 'residential',
      isRented: true,
      totalMonthlyCost: 2500.5,
      userMonthlyCost: 1800.25,
      partnerMonthlyCost: 700.25,
      activeServicesCount: 5,
      owners: [
        {
          ownerType: 'User',
          owner: { id: mockUser._id, fullName: mockUser.fullName, email: mockUser.email },
          ownershipPercentage: 70,
        },
        {
          ownerType: 'Partner',
          owner: { _id: 'partner1', name: 'John Partner', email: '<EMAIL>' },
          ownershipPercentage: 30,
        },
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      _id: 'property2',
      name: 'Office Building',
      address: '456 Business Ave',
      propertyType: 'commercial',
      isRented: false,
      totalMonthlyCost: 1200.75,
      userMonthlyCost: 1200.75,
      partnerMonthlyCost: 0,
      activeServicesCount: 3,
      owners: [
        {
          ownerType: 'User',
          owner: { id: mockUser._id, fullName: mockUser.fullName, email: mockUser.email },
          ownershipPercentage: 100,
        },
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      _id: 'property3',
      name: 'Vacant Land',
      address: '789 Empty Lot',
      propertyType: 'land',
      isRented: false,
      totalMonthlyCost: 0,
      userMonthlyCost: 0,
      partnerMonthlyCost: 0,
      activeServicesCount: 0,
      owners: [
        {
          ownerType: 'User',
          owner: { id: mockUser._id, fullName: mockUser.fullName, email: mockUser.email },
          ownershipPercentage: 100,
        },
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useAuth).mockReturnValue({
      user: { ...mockUser, id: mockUser._id },
      loading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });
  });

  it('should display total cost across all properties', async () => {
    vi.mocked(useProperties).mockReturnValue({
      properties: mockPropertiesWithCosts,
      isLoading: false,
      error: null,
      createProperty: vi.fn(),
      updateProperty: vi.fn(),
      deleteProperty: vi.fn(),
      bulkDeleteProperties: vi.fn(),
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isBulkDeleting: false,
    });

    render(<PropertiesPage />);

    await waitFor(() => {
      // Total cost should be 2500.50 + 1200.75 + 0 = 3701.25
      expect(screen.getByText('Total Monthly Cost: $3,701.25')).toBeInTheDocument();
    });
  });

  it('should display individual property costs', async () => {
    vi.mocked(useProperties).mockReturnValue({
      properties: mockPropertiesWithCosts,
      isLoading: false,
      error: null,
      createProperty: vi.fn(),
      updateProperty: vi.fn(),
      deleteProperty: vi.fn(),
      bulkDeleteProperties: vi.fn(),
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isBulkDeleting: false,
    });

    render(<PropertiesPage />);

    await waitFor(() => {
      expect(screen.getByText('$2,500.50')).toBeInTheDocument();
      expect(screen.getByText('$1,200.75')).toBeInTheDocument();
      expect(screen.getByText('$0.00')).toBeInTheDocument();
    });
  });

  it('should handle properties with zero costs', async () => {
    const propertiesWithZeroCosts = [
      {
        ...mockPropertiesWithCosts[2], // Vacant land with zero costs
      },
    ];

    vi.mocked(useProperties).mockReturnValue({
      properties: propertiesWithZeroCosts,
      isLoading: false,
      error: null,
      createProperty: vi.fn(),
      updateProperty: vi.fn(),
      deleteProperty: vi.fn(),
      bulkDeleteProperties: vi.fn(),
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isBulkDeleting: false,
    });

    render(<PropertiesPage />);

    await waitFor(() => {
      expect(screen.getByText('Total Monthly Cost: $0.00')).toBeInTheDocument();
      expect(screen.getByText('$0.00')).toBeInTheDocument();
    });
  });

  it('should handle properties without cost information', async () => {
    const propertiesWithoutCosts = [
      {
        _id: 'property1',
        name: 'Property Without Costs',
        address: '123 No Cost St',
        propertyType: 'residential',
        isRented: false,
        // No cost fields
        owners: [
          {
            ownerType: 'User',
            owner: { id: mockUser._id, fullName: mockUser.fullName, email: mockUser.email },
            ownershipPercentage: 100,
          },
        ],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    vi.mocked(useProperties).mockReturnValue({
      properties: propertiesWithoutCosts,
      isLoading: false,
      error: null,
      createProperty: vi.fn(),
      updateProperty: vi.fn(),
      deleteProperty: vi.fn(),
      bulkDeleteProperties: vi.fn(),
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isBulkDeleting: false,
    });

    render(<PropertiesPage />);

    await waitFor(() => {
      // Should default to $0.00 when cost information is missing
      expect(screen.getByText('Total Monthly Cost: $0.00')).toBeInTheDocument();
      expect(screen.getByText('$0.00')).toBeInTheDocument();
    });
  });

  it('should update total cost when properties are filtered', async () => {
    vi.mocked(useProperties).mockReturnValue({
      properties: mockPropertiesWithCosts,
      isLoading: false,
      error: null,
      createProperty: vi.fn(),
      updateProperty: vi.fn(),
      deleteProperty: vi.fn(),
      bulkDeleteProperties: vi.fn(),
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      isBulkDeleting: false,
    });

    render(<PropertiesPage />);

    // Filter by commercial properties
    // Note: This would require more complex interaction testing
    // For now, we're testing that the cost calculation logic works

    await waitFor(() => {
      expect(screen.getByText('Total Monthly Cost: $3,701.25')).toBeInTheDocument();
    });
  });
});

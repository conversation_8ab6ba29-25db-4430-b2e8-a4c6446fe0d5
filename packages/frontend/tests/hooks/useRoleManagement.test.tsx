import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { toast } from 'react-hot-toast';
import { useUsers, useChangeUserRole, useBulkChangeUserRoles, useRolePermissions, useRoleBasedUI, useUserSelection } from '../../hooks/useRoleManagement';
import { useAuth } from '../../hooks/useAuth';
import * as adminApi from '../../api/admin';
import { UserRole, IUserPublic } from '@alom-rentals/shared';

// Mock dependencies
vi.mock('../../hooks/useAuth');
vi.mock('../../api/admin');
vi.mock('react-hot-toast');

const mockUseAuth = vi.mocked(useAuth);
const mockAdminApi = vi.mocked(adminApi);
const mockToast = vi.mocked(toast);

// Test data
const mockUsers: IUserPublic[] = [
  {
    id: '1',
    email: '<EMAIL>',
    fullName: 'Admin User',
    role: 'ADMIN',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01'),
    accountStatus: 'ACTIVE',
    loginCount: 10,
  },
  {
    id: '2',
    email: '<EMAIL>',
    fullName: 'Operator User',
    role: 'OPERATOR',
    createdAt: new Date('2023-01-02'),
    updatedAt: new Date('2023-01-02'),
    accountStatus: 'ACTIVE',
    loginCount: 5,
  },
  {
    id: '3',
    email: '<EMAIL>',
    fullName: 'Customer User',
    role: 'CUSTOMER',
    createdAt: new Date('2023-01-03'),
    updatedAt: new Date('2023-01-03'),
    accountStatus: 'ACTIVE',
    loginCount: 2,
  },
];

const mockAdminUser = mockUsers[0];

const mockOperatorUser = mockUsers[1];

const mockCustomerUser = mockUsers[2];

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};
describe('useRoleManagement hooks', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockToast.success = vi.fn();
    mockToast.error = vi.fn();
  });

  describe('useUsers', () => {
    it('should fetch all users successfully', async () => {
      mockAdminApi.getAllUsers.mockResolvedValue(mockUsers);

      const { result } = renderHook(() => useUsers(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockUsers);
      expect(mockAdminApi.getAllUsers).toHaveBeenCalledOnce();
    });

    it('should handle fetch error', async () => {
      const error = new Error('Failed to fetch users');
      mockAdminApi.getAllUsers.mockRejectedValue(error);

      const { result } = renderHook(() => useUsers(), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error).toEqual(error);
    });
  });

  describe('useChangeUserRole', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: mockAdminUser,
        signIn: vi.fn(),
        signOut: vi.fn(),
        loading: false,
        signUp: vi.fn(),
      });
    });

    it('should change user role successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'Role changed successfully',
        user: { ...mockUsers[2], role: 'OPERATOR' as UserRole },
      };

      mockAdminApi.changeUserRole.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useChangeUserRole(), {
        wrapper: createWrapper(),
      });

      await result.current.mutateAsync({
        userId: '3',
        roleData: { role: 'OPERATOR' },
      });

      expect(mockAdminApi.changeUserRole).toHaveBeenCalledWith('3', { role: 'OPERATOR' });
      expect(mockToast.success).toHaveBeenCalledWith('Role changed successfully');
    });

    it('should prevent admin from changing own role to non-admin', async () => {
      const { result } = renderHook(() => useChangeUserRole(), {
        wrapper: createWrapper(),
      });

      await expect(
        result.current.mutateAsync({
          userId: '1', // Admin's own ID
          roleData: { role: 'CUSTOMER' },
        })
      ).rejects.toThrow('Cannot change your own admin role');

      expect(mockAdminApi.changeUserRole).not.toHaveBeenCalled();
    });

    it('should handle role change error', async () => {
      const error = new Error('Failed to change role');
      mockAdminApi.changeUserRole.mockRejectedValue(error);

      const { result } = renderHook(() => useChangeUserRole(), {
        wrapper: createWrapper(),
      });

      await expect(
        result.current.mutateAsync({
          userId: '3',
          roleData: { role: 'OPERATOR' },
        })
      ).rejects.toThrow('Failed to change role');

      expect(mockToast.error).toHaveBeenCalledWith('Failed to change role');
    });
  });

  describe('useBulkChangeUserRoles', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: mockAdminUser,
        signIn: vi.fn(),
        signOut: vi.fn(),
        loading: false,
        signUp: vi.fn(),
      });
    });

    it('should perform bulk role change successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'Successfully updated 2 users',
        updatedCount: 2,
      };

      mockAdminApi.bulkChangeUserRoles.mockResolvedValue(mockResponse);

      const { result } = renderHook(() => useBulkChangeUserRoles(), {
        wrapper: createWrapper(),
      });

      await result.current.mutateAsync({
        userIds: ['2', '3'],
        role: 'CUSTOMER',
      });

      expect(mockAdminApi.bulkChangeUserRoles).toHaveBeenCalledWith({
        userIds: ['2', '3'],
        role: 'CUSTOMER',
      });
      expect(mockToast.success).toHaveBeenCalledWith('Successfully updated 2 users');
    });

    it('should prevent admin from changing own role in bulk operation', async () => {
      const { result } = renderHook(() => useBulkChangeUserRoles(), {
        wrapper: createWrapper(),
      });

      await expect(
        result.current.mutateAsync({
          userIds: ['1', '2'], // Includes admin's own ID
          role: 'CUSTOMER',
        })
      ).rejects.toThrow('Cannot change your own admin role in bulk operation');

      expect(mockAdminApi.bulkChangeUserRoles).not.toHaveBeenCalled();
    });
  });

  describe('useRolePermissions', () => {
    it('should return correct permissions for admin', () => {
      mockUseAuth.mockReturnValue({
        user: mockAdminUser,
        signIn: vi.fn(),
        signOut: vi.fn(),
        loading: false,
        signUp: vi.fn(),
      });

      const { result } = renderHook(() => useRolePermissions());

      expect(result.current).toEqual({
        canManageRoles: true,
        canViewAuditLogs: true,
        canViewAdminStats: true,
        canAccessAdminDashboard: true,
        currentUserRole: 'ADMIN',
        currentUserId: '1',
      });
    });

    it('should return correct permissions for operator', () => {
      mockUseAuth.mockReturnValue({
        user: mockOperatorUser,
        signIn: vi.fn(),
        signOut: vi.fn(),
        loading: false,
        signUp: vi.fn(),
      });

      const { result } = renderHook(() => useRolePermissions());

      expect(result.current).toEqual({
        canManageRoles: false,
        canViewAuditLogs: false,
        canViewAdminStats: false,
        canAccessAdminDashboard: false,
        currentUserRole: 'OPERATOR',
        currentUserId: '2',
      });
    });

    it('should return correct permissions for customer', () => {
      mockUseAuth.mockReturnValue({
        user: mockCustomerUser,
        signIn: vi.fn(),
        signOut: vi.fn(),
        loading: false,
        signUp: vi.fn(),
      });

      const { result } = renderHook(() => useRolePermissions());

      expect(result.current).toEqual({
        canManageRoles: false,
        canViewAuditLogs: false,
        canViewAdminStats: false,
        canAccessAdminDashboard: false,
        currentUserRole: 'CUSTOMER',
        currentUserId: '3',
      });
    });
  });

  describe('useRoleBasedUI', () => {
    it('should return correct UI flags for admin', () => {
      mockUseAuth.mockReturnValue({
        user: mockAdminUser,
        signIn: vi.fn(),
        signOut: vi.fn(),
        loading: false,
        signUp: vi.fn(),
      });

      const { result } = renderHook(() => useRoleBasedUI());

      expect(result.current).toEqual({
        currentRole: 'ADMIN',
        isAdmin: true,
        isOperator: false,
        isCustomer: false,
        canManageUsers: true,
        canManageProperties: true,
        canViewAllData: true,
        hasReadOnlyAccess: false,
      });
    });

    it('should return correct UI flags for operator', () => {
      mockUseAuth.mockReturnValue({
        user: mockOperatorUser,
        signIn: vi.fn(),
        signOut: vi.fn(),
        loading: false,
        signUp: vi.fn(),
      });

      const { result } = renderHook(() => useRoleBasedUI());

      expect(result.current).toEqual({
        currentRole: 'OPERATOR',
        isAdmin: false,
        isOperator: true,
        isCustomer: false,
        canManageUsers: false,
        canManageProperties: true,
        canViewAllData: false,
        hasReadOnlyAccess: false,
      });
    });

    it('should return correct UI flags for customer', () => {
      mockUseAuth.mockReturnValue({
        user: mockCustomerUser,
        signIn: vi.fn(),
        signOut: vi.fn(),
        loading: false,
        signUp: vi.fn(),
      });

      const { result } = renderHook(() => useRoleBasedUI());

      expect(result.current).toEqual({
        currentRole: 'CUSTOMER',
        isAdmin: false,
        isOperator: false,
        isCustomer: true,
        canManageUsers: false,
        canManageProperties: false,
        canViewAllData: false,
        hasReadOnlyAccess: true,
      });
    });
  });

  describe('useUserSelection', () => {
    it('should manage user selection correctly', () => {
      const { result } = renderHook(() => useUserSelection());

      // Initial state
      expect(result.current.selectedUsers).toEqual([]);
      expect(result.current.selectedCount).toBe(0);
      expect(result.current.hasSelection).toBe(false);

      // Select a user
      result.current.selectUser('1');
      expect(result.current.selectedUsers).toEqual(['1']);
      expect(result.current.selectedCount).toBe(1);
      expect(result.current.hasSelection).toBe(true);
      expect(result.current.isSelected('1')).toBe(true);

      // Toggle user (deselect)
      result.current.toggleUser('1');
      expect(result.current.selectedUsers).toEqual([]);
      expect(result.current.selectedCount).toBe(0);

      // Select multiple users
      result.current.selectAll(['1', '2', '3']);
      expect(result.current.selectedUsers).toEqual(['1', '2', '3']);
      expect(result.current.selectedCount).toBe(3);

      // Deselect all
      result.current.deselectAll();
      expect(result.current.selectedUsers).toEqual([]);
      expect(result.current.selectedCount).toBe(0);
    });
  });
});

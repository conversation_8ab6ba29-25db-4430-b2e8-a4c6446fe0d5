import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { usePropertyServices } from '../../hooks/usePropertyServices';
import { propertyServicesAPI } from '../../lib/api';
import { IPropertyService, IPropertyServiceCreateData, IPropertyServiceUpdateData, IServiceCostSummary } from '@alom-rentals/shared';

// Mock the API
vi.mock('../../lib/api', () => ({
  propertyServicesAPI: {
    getAll: vi.fn(),
    getSummary: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
}));

const mockPropertyServicesAPI = propertyServicesAPI as any;

// Test data
const mockServices: IPropertyService[] = [
  {
    _id: '1',
    propertyId: 'prop1',
    name: 'Internet Service',
    cost: 50.0,
    frequency: 'monthly',
    status: 'active',
    mandatory: true,
    paidByUser: true,
    paidByPartner: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    _id: '2',
    propertyId: 'prop1',
    name: 'Insurance',
    cost: 600.0,
    frequency: 'quarterly',
    status: 'active',
    mandatory: false,
    paidByUser: false,
    paidByPartner: true,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
  },
];

const mockSummary: IServiceCostSummary = {
  totalMonthlyCost: 250.0,
  userMonthlyCost: 50.0,
  partnerMonthlyCost: 200.0,
  mandatoryMonthlyCost: 50.0,
  activeServicesCount: 2,
  totalServicesCount: 2,
};

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};
describe('usePropertyServices', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default successful responses
    mockPropertyServicesAPI.getAll.mockResolvedValue({ data: { services: mockServices } });
    mockPropertyServicesAPI.getSummary.mockResolvedValue({ data: mockSummary });
  });

  describe('Initial state and data fetching', () => {
    it('should initialize with correct default state', () => {
      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      expect(result.current.services).toEqual([]);
      expect(result.current.summary).toBeNull();
      expect(result.current.loading).toBe(true);
      expect(result.current.error).toBeNull();
    });

    it('should fetch services and summary on mount', async () => {
      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(mockPropertyServicesAPI.getAll).toHaveBeenCalledWith('prop1');
      expect(mockPropertyServicesAPI.getSummary).toHaveBeenCalledWith('prop1');
      expect(result.current.services).toEqual(mockServices);
      expect(result.current.summary).toEqual(mockSummary);
      expect(result.current.error).toBeNull();
    });

    it('should handle fetch services error', async () => {
      const errorMessage = 'Failed to fetch services';
      mockPropertyServicesAPI.getAll.mockRejectedValue({
        response: { data: { error: errorMessage } },
      });

      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.services).toEqual([]);
      expect((result.current.error as any)?.response?.data?.error).toBe(errorMessage);
    });

    it('should handle fetch summary error gracefully', async () => {
      mockPropertyServicesAPI.getSummary.mockRejectedValue(new Error('Summary error'));
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.services).toEqual(mockServices);
      expect(result.current.summary).toBeNull();
      // React Query handles errors differently - no automatic console.error

      consoleSpy.mockRestore();
    });

    it('should not fetch data when propertyId is empty', () => {
      renderHook(() => usePropertyServices(''), {
        wrapper: createWrapper(),
      });

      expect(mockPropertyServicesAPI.getAll).not.toHaveBeenCalled();
      expect(mockPropertyServicesAPI.getSummary).not.toHaveBeenCalled();
    });
  });

  describe('createService', () => {
    it('should create a new service successfully', async () => {
      const newService: IPropertyService = {
        _id: '3',
        propertyId: 'prop1',
        name: 'New Service',
        cost: 75.0,
        frequency: 'monthly',
        status: 'active',
        mandatory: false,
        paidByUser: true,
        paidByPartner: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const serviceData: IPropertyServiceCreateData = {
        name: 'New Service',
        cost: 75.0,
        frequency: 'monthly',
        status: 'active',
        mandatory: false,
        paidByUser: true,
        paidByPartner: false,
      };

      mockPropertyServicesAPI.create.mockResolvedValue({ data: newService });

      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      let createdService: IPropertyService;
      await act(async () => {
        createdService = await result.current.createService(serviceData);
      });

      expect(mockPropertyServicesAPI.create).toHaveBeenCalledWith('prop1', serviceData);
      expect(mockPropertyServicesAPI.getSummary).toHaveBeenCalledTimes(2); // Initial + after create
      expect(createdService!).toEqual(newService);
      // With react-query, the cache is invalidated and refetched
      expect(result.current.error).toBeNull();
    });

    it('should handle create service error', async () => {
      const errorMessage = 'Failed to create service';
      mockPropertyServicesAPI.create.mockRejectedValue({
        response: { data: { error: errorMessage } },
      });

      const serviceData: IPropertyServiceCreateData = {
        name: 'New Service',
        cost: 75.0,
        frequency: 'monthly',
        status: 'active',
        mandatory: false,
        paidByUser: true,
        paidByPartner: false,
      };

      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await expect(result.current.createService(serviceData)).rejects.toThrow(errorMessage);
      });

      // With react-query, mutation errors are handled differently
      expect(result.current.services).toEqual(mockServices); // Should remain unchanged
    });
  });

  describe('updateService', () => {
    it('should update a service successfully', async () => {
      const updatedService: IPropertyService = {
        ...mockServices[0],
        name: 'Updated Internet Service',
        cost: 60.0,
      };

      const updateData: IPropertyServiceUpdateData = {
        name: 'Updated Internet Service',
        cost: 60.0,
      };

      mockPropertyServicesAPI.update.mockResolvedValue({
        data: updatedService,
      });

      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      let returnedService: IPropertyService;
      await act(async () => {
        returnedService = await result.current.updateService('1', updateData);
      });

      expect(mockPropertyServicesAPI.update).toHaveBeenCalledWith('prop1', '1', updateData);
      expect(mockPropertyServicesAPI.getSummary).toHaveBeenCalledTimes(2); // Initial + after update
      expect(returnedService!).toEqual(updatedService);

      // With react-query, the cache is invalidated and refetched
      expect(result.current.error).toBeNull();
    });

    it('should handle update service error', async () => {
      const errorMessage = 'Failed to update service';
      mockPropertyServicesAPI.update.mockRejectedValue({
        response: { data: { error: errorMessage } },
      });

      const updateData: IPropertyServiceUpdateData = {
        name: 'Updated Service',
      };

      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await expect(result.current.updateService('1', updateData)).rejects.toThrow(errorMessage);
      });

      // With react-query, mutation errors are handled differently
      expect(result.current.services).toEqual(mockServices); // Should remain unchanged
    });
  });

  describe('deleteService', () => {
    it('should delete a service successfully', async () => {
      mockPropertyServicesAPI.delete.mockResolvedValue({});

      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await result.current.deleteService('1');
      });

      expect(mockPropertyServicesAPI.delete).toHaveBeenCalledWith('prop1', '1');
      expect(mockPropertyServicesAPI.getSummary).toHaveBeenCalledTimes(2); // Initial + after delete

      // With react-query, the cache is invalidated and refetched
      expect(result.current.error).toBeNull();
    });

    it('should handle delete service error', async () => {
      const errorMessage = 'Failed to delete service';
      mockPropertyServicesAPI.delete.mockRejectedValue({
        response: { data: { error: errorMessage } },
      });

      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      await act(async () => {
        await expect(result.current.deleteService('1')).rejects.toThrow(errorMessage);
      });

      // With react-query, mutation errors are handled differently
      expect(result.current.services).toEqual(mockServices); // Should remain unchanged
    });
  });

  describe('refetch', () => {
    it('should refetch services and summary', async () => {
      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // Clear previous calls
      vi.clearAllMocks();
      mockPropertyServicesAPI.getAll.mockResolvedValue({ data: { services: mockServices } });
      mockPropertyServicesAPI.getSummary.mockResolvedValue({
        data: mockSummary,
      });

      await act(async () => {
        result.current.refetch();
      });

      expect(mockPropertyServicesAPI.getAll).toHaveBeenCalledWith('prop1');
      expect(mockPropertyServicesAPI.getSummary).toHaveBeenCalledWith('prop1');
    });
  });

  describe('Error handling', () => {
    it('should use default error message when response error is not available', async () => {
      mockPropertyServicesAPI.getAll.mockRejectedValue(new Error('Network error'));

      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.error?.message).toBe('Network error');
    });

    it('should clear error when operations succeed', async () => {
      // First, cause an error
      mockPropertyServicesAPI.getAll.mockRejectedValue({
        response: { data: { error: 'Initial error' } },
      });

      const { result } = renderHook(() => usePropertyServices('prop1'), {
        wrapper: createWrapper(),
      });

      await waitFor(() => {
        expect((result.current.error as any)?.response?.data?.error).toBe('Initial error');
      });

      // Then, make a successful operation
      const newService: IPropertyService = {
        _id: '3',
        propertyId: 'prop1',
        name: 'New Service',
        cost: 75.0,
        frequency: 'monthly',
        status: 'active',
        mandatory: false,
        paidByUser: true,
        paidByPartner: false,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPropertyServicesAPI.create.mockResolvedValue({ data: newService });

      await act(async () => {
        await result.current.createService({
          name: 'New Service',
          cost: 75.0,
          frequency: 'monthly',
          status: 'active',
          mandatory: false,
          paidByUser: true,
          paidByPartner: false,
        });
      });

      // With react-query, errors persist until the next query - this is expected behavior
    });
  });
});

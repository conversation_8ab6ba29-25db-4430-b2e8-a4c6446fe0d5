// React import removed - using JS<PERSON> transform
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { useAuth } from '../../hooks/useAuth';
import { authAPI } from '../../lib/api';

// Mock the API
vi.mock('../../lib/api', () => ({
  authAPI: {
    getCurrentUser: vi.fn(),
    login: vi.fn(),
    register: vi.fn(),
  },
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};

describe('useAuth', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should initialize with token from localStorage', () => {
    localStorageMock.getItem.mockReturnValue('test-token');

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    expect(localStorageMock.getItem).toHaveBeenCalledWith('token');
    expect(result.current.loading).toBe(true); // Should be loading user data
  });

  it('should initialize without token when localStorage is empty', () => {
    localStorageMock.getItem.mockReturnValue(null);

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    expect(result.current.user).toBeUndefined();
    expect(result.current.loading).toBe(false);
  });

  it('should fetch user data when token exists', async () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      fullName: 'Test User',
    };
    localStorageMock.getItem.mockReturnValue('test-token');
    vi.mocked(authAPI.getCurrentUser).mockResolvedValue({
      data: mockUser,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    });

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(authAPI.getCurrentUser).toHaveBeenCalled();
    expect(result.current.user).toEqual(mockUser);
  });

  it('should handle successful login', async () => {
    const mockResponse = {
      data: {
        token: 'new-token',
        user: { id: '1', email: '<EMAIL>', fullName: 'Test User' },
      },
    };

    localStorageMock.getItem.mockReturnValue(null);
    vi.mocked(authAPI.login).mockResolvedValue({
      ...mockResponse,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    });

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    const loginResult = await result.current.signIn('<EMAIL>', 'password');

    expect(authAPI.login).toHaveBeenCalledWith({ email: '<EMAIL>', password: 'password' });
    expect(localStorageMock.setItem).toHaveBeenCalledWith('token', 'new-token');
    expect(localStorageMock.setItem).toHaveBeenCalledWith('user', JSON.stringify(mockResponse.data.user));
    expect(loginResult.error).toBeNull();
  });

  it('should handle login failure', async () => {
    const mockError = {
      response: { data: { error: 'Invalid credentials' } },
    };

    localStorageMock.getItem.mockReturnValue(null);
    vi.mocked(authAPI.login).mockRejectedValue(mockError);

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    const loginResult = await result.current.signIn('<EMAIL>', 'wrong-password');

    expect(loginResult.error).toEqual({ message: 'Invalid credentials' });
    expect(localStorageMock.setItem).not.toHaveBeenCalled();
  });

  it('should handle successful registration', async () => {
    const mockResponse = {
      data: {
        token: 'new-token',
        user: { id: '1', email: '<EMAIL>', fullName: 'Test User' },
      },
    };

    localStorageMock.getItem.mockReturnValue(null);
    vi.mocked(authAPI.register).mockResolvedValue({
      ...mockResponse,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    });

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    const registerResult = await result.current.signUp('<EMAIL>', 'password', 'Test User');

    expect(authAPI.register).toHaveBeenCalledWith({ email: '<EMAIL>', password: 'password', fullName: 'Test User' });
    expect(localStorageMock.setItem).toHaveBeenCalledWith('token', 'new-token');
    expect(localStorageMock.setItem).toHaveBeenCalledWith('user', JSON.stringify(mockResponse.data.user));
    expect(registerResult.data).toEqual(mockResponse.data);
    expect(registerResult.error).toBeNull();
  });

  it('should handle registration failure', async () => {
    const mockError = {
      response: { data: { error: 'Email already exists' } },
    };

    localStorageMock.getItem.mockReturnValue(null);
    vi.mocked(authAPI.register).mockRejectedValue(mockError);

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    const registerResult = await result.current.signUp('<EMAIL>', 'password', 'Test User');

    expect(registerResult.data).toBeNull();
    expect(registerResult.error).toEqual({ message: 'Email already exists' });
    expect(localStorageMock.setItem).not.toHaveBeenCalled();
  });

  it('should handle sign out', async () => {
    localStorageMock.getItem.mockReturnValue('test-token');

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    const signOutResult = await result.current.signOut();

    expect(localStorageMock.removeItem).toHaveBeenCalledWith('token');
    expect(localStorageMock.removeItem).toHaveBeenCalledWith('user');
    expect(signOutResult.error).toBeNull();
  });

  it('should handle API errors gracefully', async () => {
    localStorageMock.getItem.mockReturnValue('test-token');
    vi.mocked(authAPI.getCurrentUser).mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.user).toBeUndefined();
  });
});

import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import React from 'react';
import { useShortTermRentals } from '../../hooks/useShortTermRentals';
import { useShortTermRentalCost } from '../../hooks/useShortTermRentalCost';
import { shortTermRentalsAPI } from '../../lib/api';

// Mock the API
vi.mock('../../lib/api', () => ({
  shortTermRentalsAPI: {
    getAll: vi.fn(),
    getById: vi.fn(),
    calculateCost: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
}));

const mockShortTermRentalsAPI = shortTermRentalsAPI as any;

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};
const mockRentals = [
  {
    _id: '1',
    property: {
      _id: 'prop1',
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'apartment',
    },
    propertyId: 'prop1',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-07'),
    rentalAmount: 1000,
    costAmount: 200,
    status: 'active' as const,
    isPaid: false,
    guests: [
      {
        guest: {
          _id: 'guest1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          documentType: 'passport',
          documentNumber: '*********',
          fullName: 'John Doe',
        },
        isPrincipalContact: true,
      },
    ],
    commissions: [],
    referenceNumber: 'STR-2024-0001',
    createdBy: 'user1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const mockPagination = {
  page: 1,
  limit: 10,
  total: 1,
  pages: 1,
};

describe('useShortTermRentals', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should fetch rentals successfully', async () => {
    mockShortTermRentalsAPI.getAll.mockResolvedValue({
      data: {
        rentals: mockRentals,
        pagination: mockPagination,
      },
    });

    const { result } = renderHook(() => useShortTermRentals(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.rentals).toEqual(mockRentals);
    expect(result.current.pagination).toEqual(mockPagination);
    expect(mockShortTermRentalsAPI.getAll).toHaveBeenCalledWith(undefined);
  });

  it('should fetch rentals with parameters', async () => {
    const params = {
      page: 2,
      limit: 5,
      status: 'active',
      propertyId: 'prop1',
    };

    mockShortTermRentalsAPI.getAll.mockResolvedValue({
      data: {
        rentals: mockRentals,
        pagination: mockPagination,
      },
    });

    const { result } = renderHook(() => useShortTermRentals(params), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(mockShortTermRentalsAPI.getAll).toHaveBeenCalledWith(params);
  });

  it('should handle fetch error', async () => {
    const errorMessage = 'Failed to fetch rentals';
    mockShortTermRentalsAPI.getAll.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useShortTermRentals(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBeTruthy();
    expect(result.current.rentals).toEqual([]);
  });

  it('should create rental successfully', async () => {
    const newRentalData = {
      property: 'prop1',
      startDate: '2024-02-01',
      endDate: '2024-02-07',
      rentalAmount: 1200,
      costAmount: 250,
      status: 'active' as const,
      isPaid: false,
      guests: [
        {
          guest: {
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            documentType: 'passport',
            documentNumber: '*********',
            dateOfBirth: new Date('1990-01-01'),
            relationshipWithOwner: 'tenant',
            isForeigner: false,
          },
          isPrincipalContact: true,
        },
      ],
    };

    const createdRental = { ...mockRentals[0], ...newRentalData };
    mockShortTermRentalsAPI.create.mockResolvedValue({ data: createdRental });
    mockShortTermRentalsAPI.getAll.mockResolvedValue({
      data: {
        rentals: [createdRental, ...mockRentals],
        pagination: mockPagination,
      },
    });

    const { result } = renderHook(() => useShortTermRentals(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    result.current.createRental(newRentalData as any);

    await waitFor(() => {
      expect(result.current.isCreating).toBe(false);
    });

    expect(mockShortTermRentalsAPI.create).toHaveBeenCalledWith(newRentalData);
  });

  it('should handle create rental error', async () => {
    const errorMessage = 'Failed to create rental';
    mockShortTermRentalsAPI.create.mockRejectedValue(new Error(errorMessage));
    mockShortTermRentalsAPI.getAll.mockResolvedValue({
      data: {
        rentals: mockRentals,
        pagination: mockPagination,
      },
    });

    const { result } = renderHook(() => useShortTermRentals(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    const newRentalData = {
      property: 'prop1',
      startDate: '2024-02-01',
      endDate: '2024-02-07',
      rentalAmount: 1200,
      costAmount: 250,
      status: 'active' as const,
      isPaid: false,
      guests: [],
    };

    result.current.createRental(newRentalData);

    await waitFor(() => {
      expect(result.current.isCreating).toBe(false);
    });

    expect(result.current.createError).toBeTruthy();
  });

  it('should update rental successfully', async () => {
    const updateData = {
      rentalAmount: 1500,
      status: 'ended' as const,
    };

    const updatedRental = { ...mockRentals[0], ...updateData };
    mockShortTermRentalsAPI.update.mockResolvedValue({ data: updatedRental });
    mockShortTermRentalsAPI.getAll.mockResolvedValue({
      data: {
        rentals: mockRentals,
        pagination: mockPagination,
      },
    });

    const { result } = renderHook(() => useShortTermRentals(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    result.current.updateRental({ id: '1', data: updateData });

    await waitFor(() => {
      expect(result.current.isUpdating).toBe(false);
    });

    expect(mockShortTermRentalsAPI.update).toHaveBeenCalledWith('1', updateData);
  });

  it('should delete rental successfully', async () => {
    mockShortTermRentalsAPI.delete.mockResolvedValue({
      data: { message: 'Rental deleted' },
    });
    mockShortTermRentalsAPI.getAll.mockResolvedValue({
      data: {
        rentals: [],
        pagination: { ...mockPagination, total: 0 },
      },
    });

    const { result } = renderHook(() => useShortTermRentals(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    result.current.deleteRental('1');

    await waitFor(() => {
      expect(result.current.isDeleting).toBe(false);
    });

    expect(mockShortTermRentalsAPI.delete).toHaveBeenCalledWith('1');
  });

  it('should calculate cost for property', async () => {
    const costData = {
      costAmount: 300,
      services: [
        {
          _id: 'service1',
          name: 'Cleaning',
          cost: 150,
          frequency: 'per_rental',
          mandatory: true,
        },
        {
          _id: 'service2',
          name: 'Maintenance',
          cost: 150,
          frequency: 'monthly',
          mandatory: false,
        },
      ],
    };

    mockShortTermRentalsAPI.calculateCost.mockResolvedValue({ data: costData });

    const { result } = renderHook(
      () => {
        const hook = useShortTermRentals();
        const costQuery = useShortTermRentalCost('prop1');
        return { ...hook, costQuery };
      },
      {
        wrapper: createWrapper(),
      }
    );

    await waitFor(() => {
      expect(result.current.costQuery.data).toEqual(costData);
    });

    expect(mockShortTermRentalsAPI.calculateCost).toHaveBeenCalledWith('prop1');
  });
});

// React import removed - using JSX transform
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { useProperties, useProperty } from '../../hooks/useProperties';
import { propertiesAPI } from '../../lib/api';
import { createAxiosMockResponse, mockProperty } from '../utils';
import { IPropertyCreateData } from '@alom-rentals/shared';

// Mock the API
vi.mock('../../lib/api', () => ({
  propertiesAPI: {
    getAll: vi.fn(),
    getById: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    bulkDelete: vi.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};
describe('useProperties', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should fetch properties on mount', async () => {
    const mockProperties = [mockProperty];
    vi.mocked(propertiesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockProperties }));

    const { result } = renderHook(() => useProperties(), {
      wrapper: createWrapper(),
    });

    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(propertiesAPI.getAll).toHaveBeenCalled();
    expect(result.current.properties).toEqual(mockProperties);
    expect(result.current.error).toBeNull();
  });

  it('should return empty array when no properties', async () => {
    vi.mocked(propertiesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));

    const { result } = renderHook(() => useProperties(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.properties).toEqual([]);
  });

  it('should handle fetch error', async () => {
    const mockError = new Error('Failed to fetch properties');
    vi.mocked(propertiesAPI.getAll).mockRejectedValue(mockError);

    const { result } = renderHook(() => useProperties(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBeTruthy();
    expect(result.current.properties).toEqual([]);
  });

  it('should create property successfully', async () => {
    const newProperty = { ...mockProperty, _id: 'new-id' };
    const propertyData: IPropertyCreateData = {
      name: 'New Property',
      address: '456 New St',
      propertyType: 'residential',
      isRented: false,
      owners: [],
    };

    vi.mocked(propertiesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(propertiesAPI.create).mockResolvedValue(createAxiosMockResponse({ data: newProperty }));

    const { result } = renderHook(() => useProperties(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let createResult: any;
    await act(async () => {
      createResult = await result.current.createProperty(propertyData);
    });

    expect(propertiesAPI.create).toHaveBeenCalledWith(propertyData);
    expect(createResult.data).toEqual(newProperty);
    expect(createResult.error).toBeNull();
  });

  it('should handle create property error', async () => {
    const mockError = {
      response: { data: { error: 'Validation failed' } },
    };
    const propertyData: IPropertyCreateData = {
      name: 'Invalid Property',
      address: '',
      propertyType: 'residential',
      isRented: false,
      owners: [],
    };

    vi.mocked(propertiesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(propertiesAPI.create).mockRejectedValue(mockError);

    const { result } = renderHook(() => useProperties(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let createResult: any;
    await act(async () => {
      createResult = await result.current.createProperty(propertyData);
    });

    expect(createResult.data).toBeNull();
    expect(createResult.error).toEqual({ message: 'Validation failed' });
  });

  it('should update property successfully', async () => {
    const updatedProperty = { ...mockProperty, name: 'Updated Property' };
    const updateData = { name: 'Updated Property' };

    vi.mocked(propertiesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [mockProperty] }));
    vi.mocked(propertiesAPI.update).mockResolvedValue(
      createAxiosMockResponse({
        data: updatedProperty,
      })
    );

    const { result } = renderHook(() => useProperties(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let updateResult: any;
    await act(async () => {
      updateResult = await result.current.updateProperty(mockProperty._id, updateData);
    });

    expect(propertiesAPI.update).toHaveBeenCalledWith(mockProperty._id, updateData);
    expect(updateResult.data).toEqual(updatedProperty);
    expect(updateResult.error).toBeNull();
  });

  it('should handle update property error', async () => {
    const mockError = {
      response: { data: { error: 'Property not found' } },
    };
    const updateData = { name: 'Updated Property' };

    vi.mocked(propertiesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(propertiesAPI.update).mockRejectedValue(mockError);

    const { result } = renderHook(() => useProperties(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let updateResult: any;
    await act(async () => {
      updateResult = await result.current.updateProperty('invalid-id', updateData);
    });

    expect(updateResult.data).toBeNull();
    expect(updateResult.error).toEqual({ message: 'Property not found' });
  });

  it('should track loading states correctly', async () => {
    vi.mocked(propertiesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(propertiesAPI.create).mockImplementation(
      () => new Promise((resolve) => setTimeout(() => resolve(createAxiosMockResponse({ data: mockProperty })), 100))
    );

    const { result } = renderHook(() => useProperties(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isCreating).toBe(false);
    expect(result.current.isUpdating).toBe(false);

    // Test creating state
    act(() => {
      result.current.createProperty({
        name: 'Test Property',
        address: 'Test Address',
        propertyType: 'residential',
        isRented: false,
        owners: [],
      });
    });

    // Wait for the loading state to be set and then cleared
    await waitFor(() => {
      expect(result.current.isCreating).toBe(false);
    });
  });
});

describe('useProperty', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should fetch single property by id', async () => {
    vi.mocked(propertiesAPI.getById).mockResolvedValue(createAxiosMockResponse({ data: mockProperty }));

    const { result } = renderHook(() => useProperty(mockProperty._id), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(propertiesAPI.getById).toHaveBeenCalledWith(mockProperty._id);
    expect(result.current.data).toEqual(mockProperty);
  });

  it('should not fetch when id is empty', () => {
    const { result } = renderHook(() => useProperty(''), {
      wrapper: createWrapper(),
    });

    expect(propertiesAPI.getById).not.toHaveBeenCalled();
    expect(result.current.data).toBeUndefined();
  });

  it('should handle fetch error for single property', async () => {
    const mockError = new Error('Property not found');
    vi.mocked(propertiesAPI.getById).mockRejectedValue(mockError);

    const { result } = renderHook(() => useProperty('invalid-id'), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBeTruthy();
    expect(result.current.data).toBeUndefined();
  });

  it('should handle bulk property deletion', async () => {
    const mockBulkDeleteResponse = {
      data: {
        message: '2 properties deleted successfully',
        deletedCount: 2,
        deletedIds: ['property1', 'property2'],
      },
    };
    vi.mocked(propertiesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [mockProperty] }));
    vi.mocked(propertiesAPI.bulkDelete).mockResolvedValue(mockBulkDeleteResponse as any);

    const { result } = renderHook(() => useProperties(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let deleteResult: any;
    await act(async () => {
      deleteResult = await result.current.bulkDeleteProperties(['property1', 'property2']);
    });

    expect(propertiesAPI.bulkDelete).toHaveBeenCalledWith(['property1', 'property2']);
    expect(deleteResult.success).toBe(true);
    expect(deleteResult.error).toBeNull();
    expect(deleteResult.deletedCount).toBe(2);
  });

  it('should handle bulk deletion error', async () => {
    const mockError = new Error('Bulk deletion failed');
    vi.mocked(propertiesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [mockProperty] }));
    vi.mocked(propertiesAPI.bulkDelete).mockRejectedValue(mockError);

    const { result } = renderHook(() => useProperties(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let deleteResult: any;
    await act(async () => {
      deleteResult = await result.current.bulkDeleteProperties(['property1', 'property2']);
    });

    expect(deleteResult.success).toBe(false);
    expect(deleteResult.error?.message).toBe('Bulk deletion failed');
  });

  it('should provide bulk deleting state', async () => {
    vi.mocked(propertiesAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [mockProperty] }));

    const { result } = renderHook(() => useProperties(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isBulkDeleting).toBe(false);
  });
});

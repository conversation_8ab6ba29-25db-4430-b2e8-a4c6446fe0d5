// React import removed - using JSX transform
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { useGuests } from '../../hooks/useGuests';
import { guestsAPI } from '../../lib/api';
import { createAxiosMockResponse, mockGuest } from '../utils';

// Mock the API
vi.mock('../../lib/api', () => ({
  guestsAPI: {
    getAll: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    search: vi.fn(),
  },
}));

// Mock console.error to avoid noise in tests
const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};
describe('useGuests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    consoleSpy.mockClear();
  });

  it('should fetch guests on mount', async () => {
    const mockGuests = [mockGuest];
    vi.mocked(guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockGuests }));

    const { result } = renderHook(() => useGuests(), {
      wrapper: createWrapper(),
    });

    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(guestsAPI.getAll).toHaveBeenCalled();
    expect(result.current.guests).toEqual(mockGuests);
  });

  it('should handle fetch guests error', async () => {
    const mockError = new Error('Failed to fetch');
    vi.mocked(guestsAPI.getAll).mockRejectedValue(mockError);

    const { result } = renderHook(() => useGuests(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // React Query handles errors differently - no automatic console.error
    expect(result.current.guests).toEqual([]);
  });

  it('should create a guest successfully', async () => {
    const newGuest = { ...mockGuest, _id: 'new-id' };
    const guestData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      documentType: 'National ID' as const,
      documentNumber: '123456789',
      dateOfBirth: new Date('1990-01-01'),
      isForeigner: false,
    };

    vi.mocked(guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(guestsAPI.create).mockResolvedValue(createAxiosMockResponse({ data: newGuest }));

    const { result } = renderHook(() => useGuests(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let createResult: any;
    await act(async () => {
      createResult = await result.current.createGuest(guestData);
    });

    expect(guestsAPI.create).toHaveBeenCalledWith(guestData);
    expect(createResult.data).toEqual(newGuest);
    expect(createResult.error).toBeNull();
    // With react-query, the cache is invalidated and refetched, so we don't expect immediate updates
  });

  it('should handle create guest error', async () => {
    const mockError = new Error('Failed to create');
    const guestData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      documentType: 'National ID' as const,
      documentNumber: '123456789',
      dateOfBirth: new Date('1990-01-01'),
      isForeigner: false,
    };

    vi.mocked(guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(guestsAPI.create).mockRejectedValue(mockError);

    const { result } = renderHook(() => useGuests(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let createResult: any;
    await act(async () => {
      createResult = await result.current.createGuest(guestData);
    });

    // With react-query, errors are returned in the result
    expect(createResult.data).toBeNull();
    expect(createResult.error).toEqual({ message: 'Failed to create guest' });
    // React Query handles errors differently - no automatic console.error
  });

  it('should update a guest successfully', async () => {
    const existingGuest = mockGuest;
    const updatedGuest = { ...mockGuest, firstName: 'Updated Name' };
    const updateData = { firstName: 'Updated Name' };

    vi.mocked(guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [existingGuest] }));
    vi.mocked(guestsAPI.update).mockResolvedValue(createAxiosMockResponse({ data: updatedGuest }));

    const { result } = renderHook(() => useGuests(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let updateResult: any;
    await act(async () => {
      updateResult = await result.current.updateGuest(mockGuest._id, updateData);
    });

    expect(guestsAPI.update).toHaveBeenCalledWith(mockGuest._id, updateData);
    expect(updateResult.data).toEqual(updatedGuest);
    expect(updateResult.error).toBeNull();
    // With react-query, the cache is invalidated and refetched
  });

  it('should delete a guest successfully', async () => {
    const existingGuest = mockGuest;

    vi.mocked(guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [existingGuest] }));
    vi.mocked(guestsAPI.delete).mockResolvedValue(createAxiosMockResponse({}));

    const { result } = renderHook(() => useGuests(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    await act(async () => {
      await result.current.deleteGuest(mockGuest._id);
    });

    expect(guestsAPI.delete).toHaveBeenCalledWith(mockGuest._id);
    // With react-query, the cache is invalidated and refetched
  });

  it('should search guests with valid query', async () => {
    const searchResults = [mockGuest];
    vi.mocked(guestsAPI.search).mockResolvedValue(createAxiosMockResponse({ data: searchResults }));

    const { result } = renderHook(() => useGuests(), {
      wrapper: createWrapper(),
    });

    let searchResult: any;
    await act(async () => {
      searchResult = await result.current.searchGuests('John');
    });

    expect(guestsAPI.search).toHaveBeenCalledWith('John');
    expect(searchResult).toEqual(searchResults);
  });

  it('should not search with query less than 2 characters', async () => {
    const { result } = renderHook(() => useGuests(), {
      wrapper: createWrapper(),
    });

    let searchResult: any;
    await act(async () => {
      searchResult = await result.current.searchGuests('J');
    });

    expect(guestsAPI.search).not.toHaveBeenCalled();
    expect(searchResult).toEqual([]);
  });

  it('should handle search error', async () => {
    const mockError = new Error('Search failed');
    vi.mocked(guestsAPI.search).mockRejectedValue(mockError);

    const { result } = renderHook(() => useGuests(), {
      wrapper: createWrapper(),
    });

    let searchResult: any;
    await act(async () => {
      searchResult = await result.current.searchGuests('John');
    });

    expect(console.error).toHaveBeenCalledWith('Error searching guests:', mockError);
    expect(searchResult).toEqual([]);
  });

  it('should refetch guests', async () => {
    const mockGuests = [mockGuest];
    vi.mocked(guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockGuests }));

    const { result } = renderHook(() => useGuests(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    // Clear the mock to test refetch
    vi.mocked(guestsAPI.getAll).mockClear();
    vi.mocked(guestsAPI.getAll).mockResolvedValue(
      createAxiosMockResponse({
        data: [...mockGuests, { ...mockGuest, _id: 'new-id' }],
      })
    );

    await act(async () => {
      await result.current.refetch();
    });

    expect(guestsAPI.getAll).toHaveBeenCalledTimes(1);
  });

  it('should track loading states correctly', async () => {
    vi.mocked(guestsAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(guestsAPI.create).mockImplementation(
      () => new Promise((resolve) => setTimeout(() => resolve(createAxiosMockResponse({ data: mockGuest })), 100))
    );

    const { result } = renderHook(() => useGuests(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isCreating).toBe(false);
    expect(result.current.isUpdating).toBe(false);
    expect(result.current.isDeleting).toBe(false);

    // Test creating state - with react-query, we need to await the mutation
    await act(async () => {
      await result.current.createGuest({
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        documentType: 'National ID',
        documentNumber: '123',
        dateOfBirth: new Date(),
        isForeigner: false,
      });
    });

    // Wait for the mutation to complete and isCreating to be false again
    await waitFor(() => {
      expect(result.current.isCreating).toBe(false);
    });
  });
});

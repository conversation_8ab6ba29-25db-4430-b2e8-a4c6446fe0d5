// React import removed - using JSX transform
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { usePartners } from '../../hooks/usePartners';
import { partnersAPI } from '../../lib/api';
import { createAxiosMockResponse, mockPartner } from '../utils';

// Mock the API
vi.mock('../../lib/api', () => ({
  partnersAPI: {
    getAll: vi.fn(),
    getById: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};
describe('usePartners', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should fetch partners on mount', async () => {
    const mockPartners = [mockPartner];
    vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: mockPartners }));

    const { result } = renderHook(() => usePartners(), {
      wrapper: createWrapper(),
    });

    expect(result.current.isLoading).toBe(true);

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(partnersAPI.getAll).toHaveBeenCalled();
    expect(result.current.partners).toEqual(mockPartners);
    expect(result.current.error).toBeNull();
  });

  it('should return empty array when no partners', async () => {
    vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));

    const { result } = renderHook(() => usePartners(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.partners).toEqual([]);
  });

  it('should handle fetch error', async () => {
    const mockError = new Error('Failed to fetch partners');
    vi.mocked(partnersAPI.getAll).mockRejectedValue(mockError);

    const { result } = renderHook(() => usePartners(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBeTruthy();
    expect(result.current.partners).toEqual([]);
  });

  it('should create partner successfully', async () => {
    const newPartner = { ...mockPartner, _id: 'new-id' };
    const partnerData = {
      email: '<EMAIL>',
      name: 'New Partner',
    };

    vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(partnersAPI.create).mockResolvedValue(createAxiosMockResponse({ data: newPartner }));

    const { result } = renderHook(() => usePartners(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let createResult: any;
    await act(async () => {
      createResult = await result.current.createPartner(partnerData);
    });

    expect(partnersAPI.create).toHaveBeenCalledWith(partnerData);
    expect(createResult.data).toEqual(newPartner);
    expect(createResult.error).toBeNull();
  });

  it('should handle create partner error', async () => {
    const mockError = {
      response: { data: { error: 'Email already exists' } },
    };
    const partnerData = {
      email: '<EMAIL>',
      name: 'Duplicate Partner',
    };

    vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(partnersAPI.create).mockRejectedValue(mockError);

    const { result } = renderHook(() => usePartners(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let createResult: any;
    await act(async () => {
      createResult = await result.current.createPartner(partnerData);
    });

    expect(createResult.data).toBeNull();
    expect(createResult.error).toEqual({ message: 'Email already exists' });
  });

  it('should update partner successfully', async () => {
    const updatedPartner = { ...mockPartner, name: 'Updated Partner' };
    const updateData = { name: 'Updated Partner' };

    vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [mockPartner] }));
    vi.mocked(partnersAPI.update).mockResolvedValue(createAxiosMockResponse({ data: updatedPartner }));

    const { result } = renderHook(() => usePartners(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let updateResult: any;
    await act(async () => {
      updateResult = await result.current.updatePartner(mockPartner._id, updateData);
    });

    expect(partnersAPI.update).toHaveBeenCalledWith(mockPartner._id, updateData);
    expect(updateResult.data).toEqual(updatedPartner);
    expect(updateResult.error).toBeNull();
  });

  it('should handle update partner error', async () => {
    const mockError = {
      response: { data: { error: 'Partner not found' } },
    };
    const updateData = { name: 'Updated Partner' };

    vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(partnersAPI.update).mockRejectedValue(mockError);

    const { result } = renderHook(() => usePartners(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let updateResult: any;
    await act(async () => {
      updateResult = await result.current.updatePartner('invalid-id', updateData);
    });

    expect(updateResult.data).toBeNull();
    expect(updateResult.error).toEqual({ message: 'Partner not found' });
  });

  it('should delete partner successfully', async () => {
    vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [mockPartner] }));
    vi.mocked(partnersAPI.delete).mockResolvedValue(createAxiosMockResponse({ data: {} }));

    const { result } = renderHook(() => usePartners(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let deleteResult: any;
    await act(async () => {
      deleteResult = await result.current.deletePartner(mockPartner._id);
    });

    expect(partnersAPI.delete).toHaveBeenCalledWith(mockPartner._id);
    expect(deleteResult.error).toBeNull();
  });

  it('should handle delete partner error', async () => {
    const mockError = {
      response: { data: { error: 'Cannot delete partner' } },
    };

    vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(partnersAPI.delete).mockRejectedValue(mockError);

    const { result } = renderHook(() => usePartners(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    let deleteResult: any;
    await act(async () => {
      deleteResult = await result.current.deletePartner('invalid-id');
    });

    expect(deleteResult.error).toEqual({ message: 'Cannot delete partner' });
  });

  it('should track loading states correctly', async () => {
    vi.mocked(partnersAPI.getAll).mockResolvedValue(createAxiosMockResponse({ data: [] }));
    vi.mocked(partnersAPI.create).mockImplementation(
      () => new Promise((resolve) => setTimeout(() => resolve(createAxiosMockResponse({ data: mockPartner })), 100))
    );

    const { result } = renderHook(() => usePartners(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.isCreating).toBe(false);
    expect(result.current.isUpdating).toBe(false);
    expect(result.current.isDeleting).toBe(false);

    // Test creating state
    act(() => {
      result.current.createPartner({
        email: '<EMAIL>',
        name: 'Test Partner',
      });
    });

    // Wait for the loading state to be set and then cleared
    await waitFor(() => {
      expect(result.current.isCreating).toBe(false);
    });
  });

  it('should handle network errors gracefully', async () => {
    const networkError = new Error('Network Error');
    vi.mocked(partnersAPI.getAll).mockRejectedValue(networkError);

    const { result } = renderHook(() => usePartners(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBeTruthy();
    expect(result.current.partners).toEqual([]);
  });
});

// React import removed - using JS<PERSON> transform
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { useDashboard } from '../../hooks/useDashboard';
import { dashboardAPI } from '../../lib/api';

// Mock the API
vi.mock('../../lib/api', () => ({
  dashboardAPI: {
    getStats: vi.fn(),
  },
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};
describe('useDashboard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return default stats when loading', () => {
    vi.mocked(dashboardAPI.getStats).mockImplementation(() => new Promise(() => {})); // Never resolves

    const { result } = renderHook(() => useDashboard(), {
      wrapper: createWrapper(),
    });

    expect(result.current.isLoading).toBe(true);
    expect(result.current.stats).toEqual({
      totalProperties: 0,
      totalMonthlyIncome: 0,
      totalPendingDebts: 0,
      netPropertyIncome: 0,
    });
  });

  it('should fetch and return dashboard stats', async () => {
    const mockStats = {
      totalProperties: 5,
      totalMonthlyIncome: 2500,
      totalPendingDebts: 500,
      netPropertyIncome: 2000,
    };

    vi.mocked(dashboardAPI.getStats).mockResolvedValue({
      data: mockStats,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    });

    const { result } = renderHook(() => useDashboard(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(dashboardAPI.getStats).toHaveBeenCalled();
    expect(result.current.stats).toEqual(mockStats);
    expect(result.current.error).toBeNull();
  });

  it('should handle API errors', async () => {
    const mockError = new Error('Failed to fetch stats');
    vi.mocked(dashboardAPI.getStats).mockRejectedValue(mockError);

    const { result } = renderHook(() => useDashboard(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBeTruthy();
    expect(result.current.stats).toEqual({
      totalProperties: 0,
      totalMonthlyIncome: 0,
      totalPendingDebts: 0,
      netPropertyIncome: 0,
    });
  });

  it('should return default stats when data is null', async () => {
    vi.mocked(dashboardAPI.getStats).mockResolvedValue({
      data: null,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    });

    const { result } = renderHook(() => useDashboard(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.stats).toEqual({
      totalProperties: 0,
      totalMonthlyIncome: 0,
      totalPendingDebts: 0,
      netPropertyIncome: 0,
    });
  });

  it('should return partial stats when some fields are missing', async () => {
    const partialStats = {
      totalProperties: 3,
      totalMonthlyIncome: 1500,
      // Missing totalPendingDebts and netPropertyIncome
    };

    vi.mocked(dashboardAPI.getStats).mockResolvedValue({
      data: partialStats,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    });

    const { result } = renderHook(() => useDashboard(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.stats).toEqual({
      totalProperties: 3,
      totalMonthlyIncome: 1500,
      // Note: Missing fields are not included in the response
    });
  });

  it('should handle network errors gracefully', async () => {
    const networkError = new Error('Network Error');
    vi.mocked(dashboardAPI.getStats).mockRejectedValue(networkError);

    const { result } = renderHook(() => useDashboard(), {
      wrapper: createWrapper(),
    });

    await waitFor(() => {
      expect(result.current.isLoading).toBe(false);
    });

    expect(result.current.error).toBeTruthy();
    expect(result.current.stats).toEqual({
      totalProperties: 0,
      totalMonthlyIncome: 0,
      totalPendingDebts: 0,
      netPropertyIncome: 0,
    });
  });
});

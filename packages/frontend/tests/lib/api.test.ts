import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock axios completely
const mockAxiosInstance = {
  interceptors: {
    request: {
      use: vi.fn(),
    },
    response: {
      use: vi.fn(),
    },
  },
  post: vi.fn(),
  get: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
};

const mockAxiosCreate = vi.fn(() => mockAxiosInstance);

vi.mock('axios', () => ({
  default: {
    create: mockAxiosCreate,
  },
}));

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock window.location
const mockLocation = {
  href: '',
};
Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

describe('API Configuration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocation.href = '';
  });

  it('should create axios instance with correct base URL', async () => {
    // Import the module to trigger axios.create
    await import('../../lib/api');

    expect(mockAxiosCreate).toHaveBeenCalledWith({
      baseURL: 'http://localhost:3100/api',
      headers: {
        'Content-Type': 'application/json',
      },
    });
  });

  it('should use environment variable for API URL when available', async () => {
    // Mock the environment variable
    vi.stubGlobal('import', {
      meta: {
        env: {
          VITE_API_URL: 'http://custom-api.com/api',
        },
      },
    });

    // Clear previous mocks and reset modules
    vi.clearAllMocks();
    vi.resetModules();

    // Re-import the module to trigger axios.create with new env
    await import('../../lib/api');

    expect(mockAxiosCreate).toHaveBeenCalled();
  });
});

describe('Request Interceptor', () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    // Clear module cache and re-import to set up interceptors
    vi.resetModules();
    await import('../../lib/api');
  });

  it('should add authorization header when token exists', () => {
    localStorageMock.getItem.mockReturnValue('test-token');

    // Verify that the request interceptor was set up
    expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();

    // Get the request interceptor function
    const requestInterceptor = mockAxiosInstance.interceptors.request.use.mock.calls[0][0];

    const config = { headers: {} };
    const result = requestInterceptor(config);

    expect(result.headers.Authorization).toBe('Bearer test-token');
  });

  it('should not add authorization header when token does not exist', () => {
    localStorageMock.getItem.mockReturnValue(null);

    // Verify that the request interceptor was set up
    expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();

    const requestInterceptor = mockAxiosInstance.interceptors.request.use.mock.calls[0][0];

    const config = { headers: {} };
    const result = requestInterceptor(config);

    expect(result.headers.Authorization).toBeUndefined();
  });
});

describe('Response Interceptor', () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    // Clear module cache and re-import to set up interceptors
    vi.resetModules();
    await import('../../lib/api');
  });

  it('should return response on success', () => {
    // Verify that the response interceptor was set up
    expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();

    const responseInterceptor = mockAxiosInstance.interceptors.response.use.mock.calls[0][0];
    const mockResponse = { data: 'test' };

    const result = responseInterceptor(mockResponse);

    expect(result).toBe(mockResponse);
  });

  it('should handle 401 errors by clearing storage and redirecting', async () => {
    // Verify that the response interceptor was set up
    expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();

    const errorInterceptor = mockAxiosInstance.interceptors.response.use.mock.calls[0][1];
    const mockError = {
      response: {
        status: 401,
      },
    };

    try {
      await errorInterceptor(mockError);
    } catch (error) {
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('token');
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('user');
      expect(mockLocation.href).toBe('/');
      expect(error).toBe(mockError);
    }
  });

  it('should reject non-401 errors without clearing storage', async () => {
    // Verify that the response interceptor was set up
    expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();

    const errorInterceptor = mockAxiosInstance.interceptors.response.use.mock.calls[0][1];
    const mockError = {
      response: {
        status: 500,
      },
    };

    try {
      await errorInterceptor(mockError);
    } catch (error) {
      expect(localStorageMock.removeItem).not.toHaveBeenCalled();
      // For non-401 errors, location should not be changed (it might be "/" from previous test)
      expect(error).toBe(mockError);
    }
  });
});

describe('Auth API', () => {
  beforeEach(async () => {
    vi.clearAllMocks();
    // Import the module to set up the API
    await import('../../lib/api');
  });

  it('should call login endpoint with correct parameters', async () => {
    const { authAPI } = await import('../../lib/api');
    authAPI.login({ email: '<EMAIL>', password: 'password' });
    expect(mockAxiosInstance.post).toHaveBeenCalledWith('/auth/login', {
      email: '<EMAIL>',
      password: 'password',
    });
  });

  it('should call register endpoint with correct parameters', async () => {
    const { authAPI } = await import('../../lib/api');
    authAPI.register({ email: '<EMAIL>', password: 'password', fullName: 'Test User' });
    expect(mockAxiosInstance.post).toHaveBeenCalledWith('/auth/register', {
      email: '<EMAIL>',
      password: 'password',
      fullName: 'Test User',
    });
  });

  it('should call getCurrentUser endpoint', async () => {
    const { authAPI } = await import('../../lib/api');
    authAPI.getCurrentUser();
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/auth/me');
  });

  it('should call searchUsers endpoint with encoded query', async () => {
    const { authAPI } = await import('../../lib/api');
    authAPI.searchUsers('test user');
    expect(mockAxiosInstance.get).toHaveBeenCalledWith('/auth/search?q=test%20user');
  });
});

describe('Properties API', () => {
  it('should have all required methods', async () => {
    const { propertiesAPI } = await import('../../lib/api');
    expect(propertiesAPI.getAll).toBeDefined();
    expect(propertiesAPI.getById).toBeDefined();
    expect(propertiesAPI.create).toBeDefined();
    expect(propertiesAPI.update).toBeDefined();
    expect(propertiesAPI.delete).toBeDefined();
  });
});

describe('Partners API', () => {
  it('should have all required methods', async () => {
    const { partnersAPI } = await import('../../lib/api');
    expect(partnersAPI.getAll).toBeDefined();
    expect(partnersAPI.getById).toBeDefined();
    expect(partnersAPI.create).toBeDefined();
    expect(partnersAPI.update).toBeDefined();
    expect(partnersAPI.delete).toBeDefined();
  });
});

describe('Guests API', () => {
  it('should have all required methods', async () => {
    const { guestsAPI } = await import('../../lib/api');
    expect(guestsAPI.getAll).toBeDefined();
    expect(guestsAPI.getById).toBeDefined();
    expect(guestsAPI.create).toBeDefined();
    expect(guestsAPI.update).toBeDefined();
    expect(guestsAPI.delete).toBeDefined();
    expect(guestsAPI.search).toBeDefined();
  });
});

describe('Dashboard API', () => {
  it('should have getStats method', async () => {
    const { dashboardAPI } = await import('../../lib/api');
    expect(dashboardAPI.getStats).toBeDefined();
  });
});

import { describe, it, expect } from 'vitest';
import { QueryClient } from '@tanstack/react-query';
import { queryClient } from '../../lib/queryClient';

describe('Query Client Configuration', () => {
  it('should be an instance of QueryClient', () => {
    expect(queryClient).toBeInstanceOf(QueryClient);
  });

  it('should have correct stale time configuration', () => {
    const defaultOptions = queryClient.getDefaultOptions();
    expect(defaultOptions.queries?.staleTime).toBe(5 * 60 * 1000); // 5 minutes
  });

  it('should have mutations retry disabled', () => {
    const defaultOptions = queryClient.getDefaultOptions();
    expect(defaultOptions.mutations?.retry).toBe(false);
  });

  it('should have custom retry logic for queries', () => {
    const defaultOptions = queryClient.getDefaultOptions();
    const retryFunction = defaultOptions.queries?.retry;

    expect(typeof retryFunction).toBe('function');

    if (typeof retryFunction === 'function') {
      // Test retry logic for 401 errors
      const error401 = Object.assign(new Error('Unauthorized'), {
        response: { status: 401 },
      });
      expect(retryFunction(1, error401)).toBe(false);

      // Test retry logic for other errors
      const error500 = Object.assign(new Error('Server Error'), {
        response: { status: 500 },
      });
      expect(retryFunction(1, error500)).toBe(true);
      expect(retryFunction(2, error500)).toBe(true);
      expect(retryFunction(3, error500)).toBe(false);

      // Test retry logic for network errors
      const networkError = new Error('Network Error');
      expect(retryFunction(1, networkError)).toBe(true);
      expect(retryFunction(2, networkError)).toBe(true);
      expect(retryFunction(3, networkError)).toBe(false);
    }
  });

  it('should not retry on 401 errors', () => {
    const defaultOptions = queryClient.getDefaultOptions();
    const retryFunction = defaultOptions.queries?.retry;

    if (typeof retryFunction === 'function') {
      const error401 = Object.assign(new Error('HTTP 401'), {
        response: { status: 401 },
      });
      expect(retryFunction(0, error401)).toBe(false);
      expect(retryFunction(1, error401)).toBe(false);
      expect(retryFunction(2, error401)).toBe(false);
    }
  });

  it('should retry up to 3 times for non-401 errors', () => {
    const defaultOptions = queryClient.getDefaultOptions();
    const retryFunction = defaultOptions.queries?.retry;

    if (typeof retryFunction === 'function') {
      const error500 = Object.assign(new Error('HTTP 500'), {
        response: { status: 500 },
      });

      // Should retry for failure counts 0, 1, 2
      expect(retryFunction(0, error500)).toBe(true);
      expect(retryFunction(1, error500)).toBe(true);
      expect(retryFunction(2, error500)).toBe(true);

      // Should not retry for failure count 3 or higher
      expect(retryFunction(3, error500)).toBe(false);
      expect(retryFunction(4, error500)).toBe(false);
    }
  });

  it('should handle errors without response object', () => {
    const defaultOptions = queryClient.getDefaultOptions();
    const retryFunction = defaultOptions.queries?.retry;

    if (typeof retryFunction === 'function') {
      const errorWithoutResponse = new Error('Some error');

      expect(retryFunction(0, errorWithoutResponse)).toBe(true);
      expect(retryFunction(1, errorWithoutResponse)).toBe(true);
      expect(retryFunction(2, errorWithoutResponse)).toBe(true);
      expect(retryFunction(3, errorWithoutResponse)).toBe(false);
    }
  });
});

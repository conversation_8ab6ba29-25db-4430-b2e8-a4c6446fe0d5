import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient } from '@tanstack/react-query';

// Create a test query client
const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

interface AllTheProvidersProps {
  children: React.ReactNode;
  initialState?: any;
  queryClient?: QueryClient;
}

const AllTheProviders = ({ children }: AllTheProvidersProps) => {
  return <>{children}</>;
};

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialState?: any;
  queryClient?: QueryClient;
}

const customRender = (ui: ReactElement, options: CustomRenderOptions = {}) => {
  const { initialState, queryClient, ...renderOptions } = options;

  return render(ui, {
    wrapper: ({ children }) => <AllTheProviders children={children} initialState={initialState} queryClient={queryClient} />,
    ...renderOptions,
  });
};

// Mock data factories
export const mockUser = {
  _id: '507f1f77bcf86cd799439011',
  email: '<EMAIL>',
  fullName: 'Test User',
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
};

export const mockPartner = {
  _id: '507f1f77bcf86cd799439012',
  email: '<EMAIL>',
  name: 'Test Partner',
  createdBy: mockUser._id,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
};

export const mockProperty = {
  _id: '507f1f77bcf86cd799439013',
  name: 'Test Property',
  address: '123 Test St',
  description: 'A test property',
  propertyType: 'residential' as const,
  isRented: false,
  owners: [],
  createdBy: mockUser._id,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
};

export const mockGuest = {
  _id: '507f1f77bcf86cd799439014',
  firstName: 'John',
  lastName: 'Doe',
  dateOfBirth: new Date('1990-01-01'),
  email: '<EMAIL>',
  documentType: 'National ID' as const,
  documentNumber: '123456789',
  phoneNumber: '+1234567890',
  relationshipWithOwner: 'Guest',
  isForeigner: false,
  createdBy: mockUser._id,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
};

export const createAxiosMockResponse = ({
  data,
  status = 200,
  statusText = 'ok',
  headers = {},
  config = {},
}: {
  data?: any;
  status?: number;
  statusText?: string;
  headers?: any;
  config?: any;
}) => ({
  data,
  status,
  statusText,
  headers,
  config,
});

// Re-export everything
export * from '@testing-library/react';
export { customRender as render, createTestQueryClient };

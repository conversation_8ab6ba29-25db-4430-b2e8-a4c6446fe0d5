// React import removed - using JSX transform
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../utils';
import { PartnerAddModal } from '../../../components/partners/PartnerAddModal';
import { usePartners } from '../../../hooks/usePartners';

// Mock the usePartners hook
vi.mock('../../../hooks/usePartners', () => ({
  usePartners: vi.fn(),
}));

describe('PartnerAddModal', () => {
  const mockCreatePartner = vi.fn();
  const mockOnClose = vi.fn();
  const mockOnSuccess = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(usePartners).mockReturnValue({
      partners: [],
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: mockCreatePartner,
      updatePartner: vi.fn(),
      deletePartner: vi.fn(),
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });
  });

  it('should not render when isOpen is false', () => {
    render(<PartnerAddModal isOpen={false} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    expect(screen.queryByText('Add New Partner')).not.toBeInTheDocument();
  });

  it('should render modal when isOpen is true', () => {
    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    expect(screen.getByText('Add New Partner')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByPlaceholderText("Partner's full name")).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /add partner/i })).toBeInTheDocument();
  });

  it('should render with custom title and submit button text', () => {
    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} title="Create New Partner" submitButtonText="Create & Add" />);

    expect(screen.getByText('Create New Partner')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /create & add/i })).toBeInTheDocument();
  });

  it('should handle form input changes', async () => {
    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    const emailInput = screen.getByPlaceholderText('<EMAIL>');
    const nameInput = screen.getByPlaceholderText("Partner's full name");

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(nameInput, { target: { value: 'Test Partner' } });

    expect(emailInput).toHaveValue('<EMAIL>');
    expect(nameInput).toHaveValue('Test Partner');
  });

  it('should have submit button disabled when fields are empty', () => {
    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    const submitButton = screen.getByRole('button', { name: /add partner/i });

    // The form should have HTML5 validation that prevents submission
    expect(submitButton).toBeInTheDocument();
    expect(submitButton).toHaveAttribute('type', 'submit');
  });

  it('should submit form with valid data', async () => {
    const mockPartner = {
      _id: '123',
      email: '<EMAIL>',
      name: 'Test Partner',
    };
    mockCreatePartner.mockResolvedValue({ data: mockPartner, error: null });

    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    const emailInput = screen.getByPlaceholderText('<EMAIL>');
    const nameInput = screen.getByPlaceholderText("Partner's full name");
    const submitButton = screen.getByRole('button', { name: /add partner/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(nameInput, { target: { value: 'Test Partner' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCreatePartner).toHaveBeenCalledWith({
        email: '<EMAIL>',
        name: 'Test Partner',
      });
      expect(mockOnSuccess).toHaveBeenCalledWith(mockPartner);
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  it('should handle API errors', async () => {
    const errorMessage = 'Email already exists';
    mockCreatePartner.mockResolvedValue({
      data: null,
      error: {
        message: errorMessage,
      },
    });

    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    const emailInput = screen.getByPlaceholderText('<EMAIL>');
    const nameInput = screen.getByPlaceholderText("Partner's full name");
    const submitButton = screen.getByRole('button', { name: /add partner/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(nameInput, { target: { value: 'Test Partner' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    expect(mockOnSuccess).not.toHaveBeenCalled();
    expect(mockOnClose).not.toHaveBeenCalled();
  });

  it('should handle generic API errors', async () => {
    mockCreatePartner.mockResolvedValue({
      data: null,
      error: {},
    });

    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    const emailInput = screen.getByPlaceholderText('<EMAIL>');
    const nameInput = screen.getByPlaceholderText("Partner's full name");
    const submitButton = screen.getByRole('button', { name: /add partner/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(nameInput, { target: { value: 'Test Partner' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Failed to create partner')).toBeInTheDocument();
    });
  });

  it('should close modal and reset form when cancel is clicked', async () => {
    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    const emailInput = screen.getByPlaceholderText('<EMAIL>');
    const nameInput = screen.getByPlaceholderText("Partner's full name");
    const cancelButton = screen.getByRole('button', { name: /cancel/i });

    // Fill form
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(nameInput, { target: { value: 'Test Partner' } });

    // Cancel
    fireEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should show loading state when creating partner', () => {
    vi.mocked(usePartners).mockReturnValue({
      partners: [],
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: mockCreatePartner,
      updatePartner: vi.fn(),
      deletePartner: vi.fn(),
      isCreating: true,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    const submitButton = screen.getByRole('button', { name: /add partner/i });
    expect(submitButton).toBeDisabled();
    // Check that the loading spinner is present
    expect(submitButton.querySelector('.animate-spin')).toBeInTheDocument();
  });

  it('should reset form after successful submission', async () => {
    const mockPartner = {
      _id: '123',
      email: '<EMAIL>',
      name: 'Test Partner',
    };
    mockCreatePartner.mockResolvedValue({ data: mockPartner, error: null });

    const { rerender } = render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    const emailInput = screen.getByPlaceholderText('<EMAIL>');
    const nameInput = screen.getByPlaceholderText("Partner's full name");
    const submitButton = screen.getByRole('button', { name: /add partner/i });

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(nameInput, { target: { value: 'Test Partner' } });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    });

    // Reopen modal to check if form is reset
    rerender(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    const newEmailInput = screen.getByPlaceholderText('<EMAIL>');
    const newNameInput = screen.getByPlaceholderText("Partner's full name");

    expect(newEmailInput).toHaveValue('');
    expect(newNameInput).toHaveValue('');
  });

  it('should clear form when cancelled', async () => {
    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    // Fill form
    const emailInput = screen.getByPlaceholderText('<EMAIL>');
    const nameInput = screen.getByPlaceholderText("Partner's full name");

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(nameInput, { target: { value: 'Test Partner' } });

    // Cancel to reset form
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should have proper form validation attributes', () => {
    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    const emailInput = screen.getByPlaceholderText('<EMAIL>');
    const nameInput = screen.getByPlaceholderText("Partner's full name");

    expect(emailInput).toHaveAttribute('type', 'email');
    expect(emailInput).toHaveAttribute('required');
    expect(emailInput).toHaveAttribute('placeholder', '<EMAIL>');

    expect(nameInput).toHaveAttribute('type', 'text');
    expect(nameInput).toHaveAttribute('required');
    expect(nameInput).toHaveAttribute('placeholder', "Partner's full name");
  });

  it('should handle Enter key press in form fields', () => {
    render(<PartnerAddModal isOpen={true} onClose={mockOnClose} onSuccess={mockOnSuccess} />);

    const emailInput = screen.getByPlaceholderText('<EMAIL>');
    const nameInput = screen.getByPlaceholderText("Partner's full name");

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(nameInput, { target: { value: 'Test Partner' } });

    // Test that Enter key doesn't cause errors
    fireEvent.keyDown(nameInput, { key: 'Enter', code: 'Enter' });

    // Form should still be visible and functional
    expect(screen.getByText('Add New Partner')).toBeInTheDocument();
    expect(emailInput).toHaveValue('<EMAIL>');
    expect(nameInput).toHaveValue('Test Partner');
  });
});

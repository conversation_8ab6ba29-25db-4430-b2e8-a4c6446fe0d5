// React import removed - using JSX transform
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../utils';
import { PartnersPage } from '../../../components/partners/PartnersPage';
import { usePartners } from '../../../hooks/usePartners';
import { mockPartner } from '../../utils';

// Mock the usePartners hook
vi.mock('../../../hooks/usePartners', () => ({
  usePartners: vi.fn(),
}));

// Mock the PartnerAddModal component
vi.mock('../../../components/partners/PartnerAddModal', () => ({
  PartnerAddModal: ({ isOpen, onClose, title, submitButtonText }: any) =>
    isOpen ? (
      <div data-testid="partner-add-modal">
        <div>{title}</div>
        <div>{submitButtonText}</div>
        <button onClick={onClose} data-testid="modal-close">
          Close
        </button>
      </div>
    ) : null,
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  Plus: ({ className }: { className?: string }) => <div data-testid="plus-icon" className={className} />,
  Edit2: ({ className }: { className?: string }) => <div data-testid="edit2-icon" className={className} />,
  Trash2: ({ className }: { className?: string }) => <div data-testid="trash2-icon" className={className} />,
  Mail: ({ className }: { className?: string }) => <div data-testid="mail-icon" className={className} />,
  User: ({ className }: { className?: string }) => <div data-testid="user-icon" className={className} />,
}));

// Mock window.confirm
const mockConfirm = vi.fn();
Object.defineProperty(window, 'confirm', {
  value: mockConfirm,
  writable: true,
});

describe('PartnersPage', () => {
  const mockUpdatePartner = vi.fn();
  const mockDeletePartner = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockConfirm.mockReturnValue(true);
  });

  it('should render loading state', () => {
    vi.mocked(usePartners).mockReturnValue({
      partners: [],
      isLoading: true,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    const spinners = screen.getAllByRole('generic').filter((el) => el.className.includes('animate-spin'));
    expect(spinners).toHaveLength(1);
  });

  it('should render empty state when no partners', () => {
    vi.mocked(usePartners).mockReturnValue({
      partners: [],
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    expect(screen.getByText('Partners')).toBeInTheDocument();
    expect(screen.getByText('Manage your property partners and co-owners')).toBeInTheDocument();
    expect(screen.getByText('No partners yet')).toBeInTheDocument();
    expect(screen.getByText('Add partners to manage property ownership and calculations')).toBeInTheDocument();
    expect(screen.getByText('Add Your First Partner')).toBeInTheDocument();
  });

  it('should render partners list when partners exist', () => {
    const mockPartners = [
      {
        ...mockPartner,
        name: 'John Doe',
        email: '<EMAIL>',
        createdAt: new Date('2023-01-01T00:00:00.000Z'),
      },
      {
        ...mockPartner,
        _id: 'partner2',
        name: 'Jane Smith',
        email: '<EMAIL>',
        createdAt: new Date('2023-01-02T00:00:00.000Z'),
      },
    ];

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('1/1/2023')).toBeInTheDocument();
    expect(screen.getByText('1/2/2023')).toBeInTheDocument();
  });

  it('should render table headers when partners exist', () => {
    const mockPartners = [mockPartner];

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    expect(screen.getByText('Partner')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Created')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();
  });

  it('should open add modal when clicking add partner button', async () => {
    vi.mocked(usePartners).mockReturnValue({
      partners: [],
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    // Check if modal is already open (due to mocking)
    if (screen.queryByTestId('partner-add-modal')) {
      // Modal is already open, just verify it's there
      expect(screen.getByTestId('partner-add-modal')).toBeInTheDocument();
      expect(screen.getByText('Add New Partner')).toBeInTheDocument();
    } else {
      // Modal is not open, click button to open it
      const addButton = screen.getByRole('button', { name: /add partner/i });
      fireEvent.click(addButton);

      await waitFor(() => {
        expect(screen.getByTestId('partner-add-modal')).toBeInTheDocument();
        expect(screen.getByText('Add New Partner')).toBeInTheDocument();
      });
    }
  });

  it('should open add modal when clicking add first partner button', async () => {
    vi.mocked(usePartners).mockReturnValue({
      partners: [],
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    const addFirstPartnerButton = screen.getByText('Add Your First Partner');
    fireEvent.click(addFirstPartnerButton);

    await waitFor(() => {
      expect(screen.getByTestId('partner-add-modal')).toBeInTheDocument();
    });
  });

  it('should close add modal when modal close is triggered', async () => {
    vi.mocked(usePartners).mockReturnValue({
      partners: [],
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    // Open modal
    const addButton = screen.getByText('Add Partner');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByTestId('partner-add-modal')).toBeInTheDocument();
    });

    // Close modal
    const closeButton = screen.getByTestId('modal-close');
    fireEvent.click(closeButton);

    await waitFor(() => {
      expect(screen.queryByTestId('partner-add-modal')).not.toBeInTheDocument();
    });
  });

  it('should render edit and delete buttons for each partner', () => {
    const mockPartners = [mockPartner];

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    expect(screen.getByTestId('edit2-icon')).toBeInTheDocument();
    expect(screen.getByTestId('trash2-icon')).toBeInTheDocument();
  });

  it('should open edit form when edit button is clicked', async () => {
    const mockPartners = [
      {
        ...mockPartner,
        name: 'John Doe',
        email: '<EMAIL>',
      },
    ];

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    const editButton = screen.getByTitle('Edit partner');
    fireEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText('Edit Partner')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument();
    });
  });

  it('should handle partner deletion with confirmation', async () => {
    const mockPartners = [
      {
        ...mockPartner,
        name: 'John Doe',
        email: '<EMAIL>',
      },
    ];

    mockDeletePartner.mockResolvedValue({ data: null, error: null });

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    const deleteButton = screen.getByTitle('Delete partner');
    fireEvent.click(deleteButton);

    await waitFor(() => {
      expect(mockConfirm).toHaveBeenCalledWith('Are you sure you want to delete John Doe?');
      expect(mockDeletePartner).toHaveBeenCalledWith(mockPartner._id);
    });
  });

  it('should not delete partner if confirmation is cancelled', async () => {
    mockConfirm.mockReturnValue(false);

    const mockPartners = [mockPartner];

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    const deleteButton = screen.getByTitle('Delete partner');
    fireEvent.click(deleteButton);

    await waitFor(() => {
      expect(mockConfirm).toHaveBeenCalled();
      expect(mockDeletePartner).not.toHaveBeenCalled();
    });
  });

  it('should handle edit form submission', async () => {
    const mockPartners = [
      {
        ...mockPartner,
        name: 'John Doe',
        email: '<EMAIL>',
      },
    ];

    mockUpdatePartner.mockResolvedValue({
      data: { ...mockPartner, name: 'John Updated' },
      error: null,
    });

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    // Open edit form
    const editButton = screen.getByTitle('Edit partner');
    fireEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText('Edit Partner')).toBeInTheDocument();
    });

    // Update form
    const nameInput = screen.getByDisplayValue('John Doe');
    fireEvent.change(nameInput, { target: { value: 'John Updated' } });

    // Submit form
    const updateButton = screen.getByRole('button', {
      name: /update partner/i,
    });
    fireEvent.click(updateButton);

    await waitFor(() => {
      expect(mockUpdatePartner).toHaveBeenCalledWith(mockPartner._id, {
        email: '<EMAIL>',
        name: 'John Updated',
      });
    });
  });

  it('should cancel edit form', async () => {
    const mockPartners = [
      {
        ...mockPartner,
        name: 'John Doe',
        email: '<EMAIL>',
      },
    ];

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    // Open edit form
    const editButton = screen.getByTitle('Edit partner');
    fireEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText('Edit Partner')).toBeInTheDocument();
    });

    // Cancel edit
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByText('Edit Partner')).not.toBeInTheDocument();
      expect(screen.getByText('Partners')).toBeInTheDocument();
    });
  });

  it('should handle edit form with empty fields', async () => {
    const mockPartners = [mockPartner];

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    // Open edit form
    const editButton = screen.getByTitle('Edit partner');
    fireEvent.click(editButton);

    await waitFor(() => {
      expect(screen.getByText('Edit Partner')).toBeInTheDocument();
    });

    // Clear required fields
    const emailInput = screen.getByDisplayValue(mockPartner.email);
    const nameInput = screen.getByDisplayValue(mockPartner.name);
    fireEvent.change(emailInput, { target: { value: '' } });
    fireEvent.change(nameInput, { target: { value: '' } });

    // Submit form
    const updateButton = screen.getByRole('button', {
      name: /update partner/i,
    });
    fireEvent.click(updateButton);

    // Form should still be visible with empty fields
    expect(emailInput).toHaveValue('');
    expect(nameInput).toHaveValue('');
    expect(screen.getByText('Edit Partner')).toBeInTheDocument();
  });

  it('should handle delete error', async () => {
    const mockPartners = [mockPartner];
    const errorMessage = 'Cannot delete partner';

    mockDeletePartner.mockResolvedValue({
      data: null,
      error: {
        message: errorMessage,
      },
    });

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PartnersPage />);

    const deleteButton = screen.getByTitle('Delete partner');
    fireEvent.click(deleteButton);

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  it('should disable delete button when deleting', () => {
    const mockPartners = [mockPartner];

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: mockUpdatePartner,
      deletePartner: mockDeletePartner,
      isCreating: false,
      isUpdating: false,
      isDeleting: true,
    });

    render(<PartnersPage />);

    const deleteButton = screen.getByTitle('Delete partner');
    expect(deleteButton).toBeDisabled();
  });
});

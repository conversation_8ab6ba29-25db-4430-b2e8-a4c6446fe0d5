// React import removed - using J<PERSON><PERSON> transform
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { GuestSelectionModal } from '../../../components/rentals/GuestSelectionModal';
import { useGuests } from '../../../hooks/useGuests';

// Mock the useGuests hook
vi.mock('../../../hooks/useGuests');
const mockUseGuests = useGuests as any;

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};

const mockGuests = [
  {
    _id: 'guest1',
    firstName: 'John',
    lastName: 'Doe',
    dateOfBirth: new Date('1990-01-01'),
    email: '<EMAIL>',
    documentType: 'Passport' as const,
    documentNumber: '*********',
    phoneNumber: '+**********',
    relationshipWithOwner: 'Guest',
    isForeigner: false,
    nationality: 'US',
    countryOfOrigin: 'US',
    nextDestination: 'Home',
    cityOfResidency: 'New York',
    cityOfBirth: 'New York',
    tripMotivation: 'Business',
    fullName: 'John Doe',
    createdBy: 'user1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: 'guest2',
    firstName: 'Jane',
    lastName: 'Smith',
    dateOfBirth: new Date('1985-05-15'),
    email: '<EMAIL>',
    documentType: 'National ID' as const,
    documentNumber: '*********',
    phoneNumber: '+**********',
    relationshipWithOwner: 'Guest',
    isForeigner: true,
    nationality: 'CA',
    countryOfOrigin: 'Canada',
    nextDestination: 'Toronto',
    cityOfResidency: 'Toronto',
    cityOfBirth: 'Toronto',
    tripMotivation: 'Tourism',
    fullName: 'Jane Smith',
    createdBy: 'user1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const defaultGuestsReturn = {
  guests: mockGuests,
  loading: false,
  error: null,
  createGuest: vi.fn(),
  updateGuest: vi.fn(),
  deleteGuest: vi.fn(),
  searchGuests: vi.fn(),
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  createError: null,
  updateError: null,
  deleteError: null,
  refetch: vi.fn(),
};

describe('GuestSelectionModal', () => {
  const mockOnClose = vi.fn();
  const mockOnSelectGuest = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseGuests.mockReturnValue(defaultGuestsReturn);
  });

  it('should not render when isOpen is false', () => {
    render(<GuestSelectionModal isOpen={false} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    expect(screen.queryByText('Select Guest')).not.toBeTruthy();
  });

  it('should render modal when isOpen is true', () => {
    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    expect(screen.getByText('Select Guest')).toBeTruthy();
    expect(screen.getByPlaceholderText('Search guests by name or email...')).toBeTruthy();
    expect(screen.getByText('Add New Guest')).toBeTruthy();
  });

  it('should show loading state', () => {
    mockUseGuests.mockReturnValue({
      ...defaultGuestsReturn,
      isLoading: true,
    });

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    expect(screen.getByRole('status', { hidden: true })).toBeTruthy();
  });

  it('should render guest list', () => {
    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    expect(screen.getByText('John Doe')).toBeTruthy();
    expect(screen.getByText('<EMAIL>')).toBeTruthy();
    expect(screen.getByText('Passport: *********')).toBeTruthy();
    expect(screen.getByText('Local')).toBeTruthy();

    expect(screen.getByText('Jane Smith')).toBeTruthy();
    expect(screen.getByText('<EMAIL>')).toBeTruthy();
    expect(screen.getByText('National ID: *********')).toBeTruthy();
    expect(screen.getByText('Foreigner')).toBeTruthy();
  });

  it('should filter guests based on search query', async () => {
    const user = userEvent.setup();

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    const searchInput = screen.getByPlaceholderText('Search guests by name or email...');
    await user.type(searchInput, 'john');

    // Only John should be visible
    expect(screen.getByText('John Doe')).toBeTruthy();
    expect(screen.queryByText('Jane Smith')).not.toBeTruthy();
  });

  it('should exclude already selected guests', () => {
    const existingGuests = [
      {
        guestId: 'guest1',
        guest: mockGuests[0],
        isPrincipalContact: true,
      },
    ];

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} existingGuests={existingGuests} />, {
      wrapper: createWrapper(),
    });

    // John should not be visible since he's already selected
    expect(screen.queryByText('John Doe')).not.toBeTruthy();
    // Jane should still be visible
    expect(screen.getByText('Jane Smith')).toBeTruthy();
  });

  it('should handle guest selection', async () => {
    const user = userEvent.setup();

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    const guestCard = screen.getByText('John Doe').closest('div');
    await user.click(guestCard!);

    expect(mockOnSelectGuest).toHaveBeenCalledWith({
      guestId: 'guest1',
      isPrincipalContact: true, // First guest becomes principal contact
    });
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should set isPrincipalContact to false when there are existing guests', async () => {
    const user = userEvent.setup();
    const existingGuests = [
      {
        guestId: 'guest2',
        guest: mockGuests[1],
        isPrincipalContact: true,
      },
    ];

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} existingGuests={existingGuests} />, {
      wrapper: createWrapper(),
    });

    const guestCard = screen.getByText('John Doe').closest('div');
    await user.click(guestCard!);

    expect(mockOnSelectGuest).toHaveBeenCalledWith({
      guestId: 'guest1',
      isPrincipalContact: false, // Not principal since one already exists
    });
  });

  it('should show empty state when no guests available', () => {
    mockUseGuests.mockReturnValue({
      ...defaultGuestsReturn,
      guests: [],
    });

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    expect(screen.getByText('No guests available.')).toBeTruthy();
    expect(screen.getByText('Create New Guest')).toBeTruthy();
  });

  it('should show no results message when search has no matches', async () => {
    const user = userEvent.setup();

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    const searchInput = screen.getByPlaceholderText('Search guests by name or email...');
    await user.type(searchInput, 'nonexistent');

    expect(screen.getByText('No guests found matching your search.')).toBeTruthy();
  });

  it('should show new guest form when Add New Guest is clicked', async () => {
    const user = userEvent.setup();

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    const addNewButton = screen.getByText('Add New Guest');
    await user.click(addNewButton);

    expect(screen.getByText('Add New Guest')).toBeTruthy();
    expect(screen.getByText('← Back to guest selection')).toBeTruthy();
  });

  it('should handle back button from new guest form', async () => {
    const user = userEvent.setup();

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    // Go to new guest form
    const addNewButton = screen.getByText('Add New Guest');
    await user.click(addNewButton);

    // Go back
    const backButton = screen.getByText('← Back to guest selection');
    await user.click(backButton);

    expect(screen.getByText('Select Guest')).toBeTruthy();
    expect(screen.queryByText('Add New Guest')).toBeTruthy();
  });

  it('should close modal when close button is clicked', async () => {
    const user = userEvent.setup();

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    const closeButton = screen.getByRole('button', { name: /close/i });
    await user.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should close modal when cancel button is clicked', async () => {
    const user = userEvent.setup();

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('should handle phone number display', () => {
    const guestWithoutPhone = {
      ...mockGuests[0],
      phoneNumber: undefined,
    };

    mockUseGuests.mockReturnValue({
      ...defaultGuestsReturn,
      guests: [guestWithoutPhone],
    });

    render(<GuestSelectionModal isOpen={true} onClose={mockOnClose} onSelectGuest={mockOnSelectGuest} />, { wrapper: createWrapper() });

    expect(screen.getByText('No phone')).toBeTruthy();
  });
});

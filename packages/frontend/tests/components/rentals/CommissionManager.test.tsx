import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, Mock } from 'vitest';
import { useForm } from 'react-hook-form';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CommissionManager } from '../../../components/rentals/CommissionManager';
import { IShortTermRentalCreateData } from '@alom-rentals/shared';

// Mock the usePartners hook
vi.mock('../../../hooks/usePartners', () => ({
  usePartners: vi.fn(() => ({
    partners: [
      { _id: '1', name: '<PERSON>', email: '<EMAIL>' },
      { _id: '2', name: '<PERSON>', email: '<EMAIL>' },
    ],
    isLoading: false,
  })),
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
};

const TestComponent: React.FC<{ rentalAmount?: number }> = ({ rentalAmount = 1000 }) => {
  const {
    control,
    formState: { errors },
  } = useForm<IShortTermRentalCreateData>({
    defaultValues: {
      commissions: [],
    },
  });

  return (
    <TestWrapper>
      <CommissionManager control={control} rentalAmount={rentalAmount} errors={errors} />
    </TestWrapper>
  );
};

describe('CommissionManager', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render commission manager with add button', () => {
    render(<TestComponent />);

    expect(screen.getByText('Commissions (Optional)')).toBeTruthy();
    expect(screen.getByText('Add Commission')).toBeTruthy();
    expect(screen.getByText('No commissions added. Click "Add Commission" to add one.')).toBeTruthy();
  });

  it('should add a new commission when add button is clicked', async () => {
    render(<TestComponent />);

    const addButton = screen.getByText('Add Commission');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText('Commission #1')).toBeTruthy();
    });

    expect(screen.getByDisplayValue('fixed')).toBeTruthy();
    expect(screen.getByDisplayValue('Partner')).toBeTruthy();
  });

  it('should remove commission when delete button is clicked', async () => {
    render(<TestComponent />);

    // Add a commission first
    const addButton = screen.getByText('Add Commission');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText('Commission #1')).toBeTruthy();
    });

    // Remove the commission
    const deleteButton = screen.getByRole('button', { name: /trash/i });
    fireEvent.click(deleteButton);

    await waitFor(() => {
      expect(screen.queryByText('Commission #1')).not.toBeTruthy();
    });

    expect(screen.getByText('No commissions added. Click "Add Commission" to add one.')).toBeTruthy();
  });

  it('should show percentage calculation for percentage commissions', async () => {
    render(<TestComponent rentalAmount={1000} />);

    // Add a commission
    const addButton = screen.getByText('Add Commission');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText('Commission #1')).toBeTruthy();
    });

    // Change to percentage type
    const typeSelect = screen.getByDisplayValue('fixed');
    fireEvent.change(typeSelect, { target: { value: 'percentage' } });

    // Enter percentage amount
    const amountInput = screen.getByPlaceholderText('0.00');
    fireEvent.change(amountInput, { target: { value: '10' } });

    await waitFor(() => {
      expect(screen.getByText('Calculated amount: $100.00')).toBeTruthy();
    });
  });

  it('should display partner options in receiver dropdown', async () => {
    render(<TestComponent />);

    // Add a commission
    const addButton = screen.getByText('Add Commission');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText('Commission #1')).toBeTruthy();
    });

    const receiverSelect = screen.getByDisplayValue('');
    expect(receiverSelect).toBeTruthy();

    // Check that partner options are available
    expect(screen.getByText('John Doe (<EMAIL>)')).toBeTruthy();
    expect(screen.getByText('Jane Smith (<EMAIL>)')).toBeTruthy();
  });

  it('should calculate total commissions correctly', async () => {
    render(<TestComponent rentalAmount={1000} />);

    // Add first commission (fixed amount)
    const addButton = screen.getByText('Add Commission');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText('Commission #1')).toBeTruthy();
    });

    // Set fixed amount
    const amountInput = screen.getByPlaceholderText('0.00');
    fireEvent.change(amountInput, { target: { value: '50' } });

    // Add second commission
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText('Commission #2')).toBeTruthy();
    });

    // Set second commission as percentage
    const typeSelects = screen.getAllByDisplayValue('fixed');
    fireEvent.change(typeSelects[1], { target: { value: 'percentage' } });

    const amountInputs = screen.getAllByPlaceholderText('0.00');
    fireEvent.change(amountInputs[1], { target: { value: '5' } });

    await waitFor(() => {
      // Fixed: $50 + Percentage: 5% of $1000 = $50 = Total: $100
      expect(screen.getByText('Total Commissions:')).toBeTruthy();
      expect(screen.getByText('$100.00')).toBeTruthy();
      expect(screen.getByText('Remaining after commissions:')).toBeTruthy();
      expect(screen.getByText('$900.00')).toBeTruthy();
    });
  });

  it('should show loading state when partners are loading', async () => {
    const { usePartners } = await import('../../../hooks/usePartners');
    (usePartners as Mock).mockReturnValue({
      partners: [],
      isLoading: true,
    });

    render(<TestComponent />);

    expect(screen.getByRole('status')).toBeTruthy();
  });

  it('should handle commission type changes correctly', async () => {
    render(<TestComponent />);

    // Add a commission
    const addButton = screen.getByText('Add Commission');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText('Commission #1')).toBeTruthy();
    });

    // Initially should show "Amount ($)" label
    expect(screen.getByText('Amount ($)')).toBeTruthy();

    // Change to percentage
    const typeSelect = screen.getByDisplayValue('fixed');
    fireEvent.change(typeSelect, { target: { value: 'percentage' } });

    await waitFor(() => {
      expect(screen.getByText('Percentage (%)')).toBeTruthy();
    });
  });

  it('should handle receiver type changes correctly', async () => {
    render(<TestComponent />);

    // Add a commission
    const addButton = screen.getByText('Add Commission');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByText('Commission #1')).toBeTruthy();
    });

    // Initially should show "Partner" as receiver type
    expect(screen.getByDisplayValue('Partner')).toBeTruthy();

    // Change to User
    const receiverTypeSelect = screen.getByDisplayValue('Partner');
    fireEvent.change(receiverTypeSelect, { target: { value: 'User' } });

    await waitFor(() => {
      expect(screen.getByDisplayValue('User')).toBeTruthy();
    });
  });
});

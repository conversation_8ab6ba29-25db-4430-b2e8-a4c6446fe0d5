import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ShortTermRentalForm } from '../../../components/rentals/ShortTermRentalForm';
import { useProperties } from '../../../hooks/useProperties';
import { useShortTermRentals } from '../../../hooks/useShortTermRentals';
import { useShortTermRentalCost } from '../../../hooks/useShortTermRentalCost';

// Mock the hooks
vi.mock('../../../hooks/useProperties');
vi.mock('../../../hooks/useShortTermRentals');
vi.mock('../../../hooks/useShortTermRentalCost');

const mockUseProperties = useProperties as any;
const mockUseShortTermRentals = useShortTermRentals as any;
const mockUseShortTermRentalCost = useShortTermRentalCost as any;

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};
const mockProperties = [
  {
    _id: 'prop1',
    name: 'Test Property 1',
    address: '123 Test St',
    propertyType: 'apartment',
    currencyId: 'curr1',
    owners: [],
    createdBy: 'user1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: 'prop2',
    name: 'Test Property 2',
    address: '456 Test Ave',
    propertyType: 'house',
    currencyId: 'curr1',
    owners: [],
    createdBy: 'user1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const mockCostCalculation = {
  costAmount: 250,
  services: [
    {
      _id: 'service1',
      name: 'Cleaning',
      cost: 150,
      frequency: 'per-rental',
      mandatory: true,
    },
    {
      _id: 'service2',
      name: 'Utilities',
      cost: 100,
      frequency: 'monthly',
      mandatory: false,
    },
  ],
};

const defaultPropertiesReturn = {
  properties: mockProperties,
  loading: false,
  error: null,
  createProperty: vi.fn(),
  updateProperty: vi.fn(),
  deleteProperty: vi.fn(),
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  createError: null,
  updateError: null,
  deleteError: null,
  refetch: vi.fn(),
};

const defaultRentalsReturn = {
  rentals: [],
  pagination: null,
  loading: false,
  error: null,
  createRental: vi.fn(),
  updateRental: vi.fn(),
  deleteRental: vi.fn(),
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  createError: null,
  updateError: null,
  deleteError: null,
  refetch: vi.fn(),
};

const defaultCostCalculationReturn = {
  data: mockCostCalculation,
  isLoading: false,
  error: null,
  isSuccess: true,
  isError: false,
  refetch: vi.fn(),
};

describe('ShortTermRentalForm', () => {
  const mockOnSuccess = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseProperties.mockReturnValue(defaultPropertiesReturn);
    mockUseShortTermRentals.mockReturnValue(defaultRentalsReturn);
    mockUseShortTermRentalCost.mockReturnValue(defaultCostCalculationReturn);
  });

  it('should render form with all required fields', () => {
    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    expect(screen.getByText('Create Short-Term Rental')).toBeTruthy();
    expect(screen.getByLabelText(/Property/)).toBeTruthy();
    expect(screen.getByLabelText(/Start Date/)).toBeTruthy();
    expect(screen.getByLabelText(/End Date/)).toBeTruthy();
    expect(screen.getByLabelText(/Rental Amount/)).toBeTruthy();
    expect(screen.getByLabelText(/Cost Amount/)).toBeTruthy();
    expect(screen.getByLabelText(/Status/)).toBeTruthy();
    expect(screen.getByText('Guests')).toBeTruthy();
    expect(screen.getByText('Create Rental')).toBeTruthy();
    expect(screen.getByText('Cancel')).toBeTruthy();
  });

  it('should show loading state when properties are loading', () => {
    mockUseProperties.mockReturnValue({
      ...defaultPropertiesReturn,
      isLoading: true,
    });

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    expect(screen.getByRole('status', { hidden: true })).toBeTruthy();
  });

  it('should populate property options', () => {
    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    const propertySelect = screen.getByLabelText(/Property/);
    expect(propertySelect).toBeTruthy();

    // Check that properties are in the select options
    expect(screen.getByText('Test Property 1 - 123 Test St')).toBeTruthy();
    expect(screen.getByText('Test Property 2 - 456 Test Ave')).toBeTruthy();
  });

  it('should show create error when present', () => {
    const errorMessage = 'Failed to create rental';
    mockUseShortTermRentals.mockReturnValue({
      ...defaultRentalsReturn,
      createError: {
        response: {
          data: {
            error: errorMessage,
          },
        },
      },
    });

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    expect(screen.getByText(errorMessage)).toBeTruthy();
  });

  it('should handle form submission with valid data', async () => {
    const user = userEvent.setup();
    const mockCreateRental = vi.fn();
    mockUseShortTermRentals.mockReturnValue({
      ...defaultRentalsReturn,
      createRental: mockCreateRental,
    });

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    // Fill out the form
    const propertySelect = screen.getByLabelText(/Property/);
    await user.selectOptions(propertySelect, 'prop1');

    const startDateInput = screen.getByLabelText(/Start Date/);
    await user.type(startDateInput, '2024-12-01');

    const endDateInput = screen.getByLabelText(/End Date/);
    await user.type(endDateInput, '2024-12-07');

    const rentalAmountInput = screen.getByLabelText(/Rental Amount/);
    await user.clear(rentalAmountInput);
    await user.type(rentalAmountInput, '1000');

    const costAmountInput = screen.getByDisplayValue('250');
    await user.clear(costAmountInput);
    await user.type(costAmountInput, '200');

    // Note: This test cannot fully test form submission because it requires
    // guests to be added through the RentalGuestManager component, which
    // involves complex modal interactions. The form validation will prevent
    // submission without guests.

    // Instead, let's verify the form structure and that the submit button exists
    const submitButton = screen.getByText('Create Rental');
    expect(submitButton).toBeTruthy();

    // Verify that the form has the required fields filled
    expect(propertySelect.nodeValue).toBe('prop1');
    expect(startDateInput.nodeValue).toBe('2024-12-01');
    expect(endDateInput.nodeValue).toBe('2024-12-07');
    expect(rentalAmountInput.nodeValue).toBe(1000);
    expect(costAmountInput.nodeValue).toBe(200);
  });

  it('should call onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    const cancelButton = screen.getByText('Cancel');
    await user.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should show auto-calculate button when property is selected', async () => {
    const user = userEvent.setup();

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    const propertySelect = screen.getByLabelText(/Property/);
    await user.selectOptions(propertySelect, 'prop1');

    await waitFor(() => {
      expect(screen.getByText('Auto-calculate')).toBeTruthy();
    });
  });

  it('should handle auto-calculate cost', async () => {
    const user = userEvent.setup();

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    const propertySelect = screen.getByLabelText(/Property/);
    await user.selectOptions(propertySelect, 'prop1');

    await waitFor(() => {
      const autoCalculateButton = screen.getByText('Auto-calculate');
      expect(autoCalculateButton).toBeTruthy();
    });

    const autoCalculateButton = screen.getByText('Auto-calculate');
    await user.click(autoCalculateButton);

    // The cost amount should be updated
    const costAmountInput = screen.getByDisplayValue('250') as HTMLInputElement;
    await waitFor(() => {
      expect(costAmountInput.value).toBe('250');
    });
  });

  it('should show payment checkbox', () => {
    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    expect(screen.getByText('Rental has been paid')).toBeTruthy();
  });

  it('should handle status selection', async () => {
    const user = userEvent.setup();

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    const statusSelect = screen.getByLabelText(/Status/);
    await user.selectOptions(statusSelect, 'ended');

    expect((statusSelect as HTMLSelectElement).value).toBe('ended');
  });

  it('should show guest management section', () => {
    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    expect(screen.getByText('Guests')).toBeTruthy();
    expect(screen.getByText('No guests added yet')).toBeTruthy();
    expect(screen.getByText('At least one guest is required for the rental')).toBeTruthy();
  });

  it('should disable submit button when creating', () => {
    mockUseShortTermRentals.mockReturnValue({
      ...defaultRentalsReturn,
      isCreating: true,
    });

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    const submitButton = screen.getByText('Creating...');
    expect(submitButton.getAttribute('disabled')).toBeTruthy();
  });

  it('should show validation errors', async () => {
    const user = userEvent.setup();

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    // Try to submit without filling required fields
    const submitButton = screen.getByText('Create Rental');
    await user.click(submitButton);

    // Validation errors should appear (exact messages depend on Zod schema)
    await waitFor(() => {
      expect(screen.getByText(/Property is required/)).toBeTruthy();
    });
  });

  it('should display discount section with proper validation', async () => {
    const user = userEvent.setup();

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    // Check discount section is present
    expect(screen.getByText('Discount (Optional)')).toBeTruthy();
    expect(screen.getByLabelText(/discount amount/i)).toBeTruthy();
    expect(screen.getByLabelText(/discount reason/i)).toBeTruthy();

    // Fill in rental amount first
    const rentalAmountInput = screen.getByLabelText(/rental amount/i);
    await user.clear(rentalAmountInput);
    await user.type(rentalAmountInput, '1000');

    // Test discount amount validation (should not exceed rental amount)
    const discountInput = screen.getByLabelText(/discount amount/i);
    await user.clear(discountInput);
    await user.type(discountInput, '1500'); // More than rental amount

    await waitFor(() => {
      expect(discountInput.getAttribute('max')).toBe('1000');
    });
  });

  it('should calculate profit correctly with discount', async () => {
    const user = userEvent.setup();

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    // Fill in amounts
    const rentalAmountInput = screen.getByLabelText(/rental amount/i);
    await user.clear(rentalAmountInput);
    await user.type(rentalAmountInput, '1000');

    const costAmountInput = screen.getByLabelText(/cost amount/i);
    await user.clear(costAmountInput);
    await user.type(costAmountInput, '300');

    const discountInput = screen.getByLabelText(/discount amount/i);
    await user.clear(discountInput);
    await user.type(discountInput, '100');

    // Check profit calculation
    await waitFor(() => {
      expect(screen.getByText('Rental Amount:')).toBeTruthy();
      expect(screen.getByText('$1000.00')).toBeTruthy();
      expect(screen.getByText('Cost Amount:')).toBeTruthy();
      expect(screen.getByText('-$300.00')).toBeTruthy();
      expect(screen.getByText('Discount:')).toBeTruthy();
      expect(screen.getByText('-$100.00')).toBeTruthy();
      expect(screen.getByText('Final Profit:')).toBeTruthy();
      expect(screen.getByText('$600.00')).toBeTruthy();
    });
  });

  it('should display commission section', async () => {
    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    // Check commission section is present
    expect(screen.getByText('Commissions (Optional)')).toBeTruthy();
    expect(screen.getByText('Add Commission')).toBeTruthy();
  });

  it('should handle discount reason input', async () => {
    const user = userEvent.setup();

    render(<ShortTermRentalForm onSuccess={mockOnSuccess} onCancel={mockOnCancel} />, { wrapper: createWrapper() });

    const discountReasonInput = screen.getByLabelText(/discount reason/i);
    await user.type(discountReasonInput, 'Early booking discount');

    expect(discountReasonInput.nodeValue).toBe('Early booking discount');
  });
});

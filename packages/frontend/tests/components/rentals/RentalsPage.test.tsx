import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { RentalsPage } from '../../../components/rentals/RentalsPage';
import { useShortTermRentals } from '../../../hooks/useShortTermRentals';
import { useProperties } from '../../../hooks/useProperties';

// Mock the hooks
vi.mock('../../../hooks/useShortTermRentals');
vi.mock('../../../hooks/useProperties');
const mockUseShortTermRentals = useShortTermRentals as any;
const mockUseProperties = useProperties as any;

// Mock window.confirm
Object.defineProperty(window, 'confirm', {
  writable: true,
  value: vi.fn(),
});

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};
const mockRentals = [
  {
    _id: '1',
    property: {
      _id: 'prop1',
      name: 'Test Property',
      address: '123 Test St',
      propertyType: 'apartment',
    },
    propertyId: 'prop1',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-01-07'),
    rentalAmount: 1000,
    costAmount: 200,
    status: 'active' as const,
    isPaid: false,
    guests: [
      {
        guest: {
          _id: 'guest1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          documentType: 'passport',
          documentNumber: '*********',
          fullName: 'John Doe',
        },
        isPrincipalContact: true,
      },
    ],
    commissions: [],
    referenceNumber: 'STR-2024-0001',
    createdBy: 'user1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const mockPagination = {
  page: 1,
  limit: 10,
  total: 1,
  pages: 1,
};

const defaultMockReturn = {
  rentals: mockRentals,
  pagination: mockPagination,
  loading: false,
  error: null,
  deleteRental: vi.fn(),
  isDeleting: false,
  isCreating: false,
  isUpdating: false,
  createError: null,
  updateError: null,
  deleteError: null,
  refetch: vi.fn(),
  createRental: vi.fn(),
  updateRental: vi.fn(),
  getRental: vi.fn(),
  calculateCost: vi.fn(() => ({
    data: null,
    isLoading: false,
    error: null,
    isSuccess: false,
  })),
};

describe('RentalsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseShortTermRentals.mockReturnValue(defaultMockReturn);
    mockUseProperties.mockReturnValue({
      properties: [],
      isLoading: false,
      error: null,
      createProperty: vi.fn(),
      updateProperty: vi.fn(),
      isCreating: false,
      isUpdating: false,
    });
  });

  it('should render page header correctly', () => {
    render(<RentalsPage />, { wrapper: createWrapper() });

    expect(screen.getByText('Short-Term Rentals')).toBeTruthy();
    expect(screen.getByText('Manage your property rentals and bookings')).toBeTruthy();
    expect(screen.getByText('Create Rental')).toBeTruthy();
  });

  it('should render filters section', () => {
    render(<RentalsPage />, { wrapper: createWrapper() });

    expect(screen.getByPlaceholderText('Search rentals...')).toBeTruthy();
    expect(screen.getByDisplayValue('All Statuses')).toBeTruthy();
  });

  it('should show loading state', () => {
    mockUseShortTermRentals.mockReturnValue({
      ...defaultMockReturn,
      loading: true,
    });

    render(<RentalsPage />, { wrapper: createWrapper() });

    expect(screen.getByRole('status', { hidden: true })).toBeTruthy();
  });

  it('should show error state', () => {
    const errorMessage = 'Failed to load rentals';
    mockUseShortTermRentals.mockReturnValue({
      ...defaultMockReturn,
      error: {
        response: {
          data: {
            error: errorMessage,
          },
        },
      },
    });

    render(<RentalsPage />, { wrapper: createWrapper() });

    expect(screen.getByText(errorMessage)).toBeTruthy();
  });

  it('should show empty state when no rentals', () => {
    mockUseShortTermRentals.mockReturnValue({
      ...defaultMockReturn,
      rentals: [],
      pagination: { ...mockPagination, total: 0 },
    });

    render(<RentalsPage />, { wrapper: createWrapper() });

    expect(screen.getByText('No rentals found')).toBeTruthy();
    expect(screen.getByText('Get started by creating your first rental.')).toBeTruthy();
    expect(screen.getByText('Create First Rental')).toBeTruthy();
  });

  it('should render rentals table with data', () => {
    render(<RentalsPage />, { wrapper: createWrapper() });

    // Check table headers
    expect(screen.getByText('Property')).toBeTruthy();
    expect(screen.getByText('Guest')).toBeTruthy();
    expect(screen.getByText('Dates')).toBeTruthy();
    expect(screen.getByText('Amount')).toBeTruthy();
    expect(screen.getByText('Status')).toBeTruthy();
    expect(screen.getByText('Reference')).toBeTruthy();

    // Check rental data
    expect(screen.getByText('Test Property')).toBeTruthy();
    expect(screen.getByText('123 Test St')).toBeTruthy();
    expect(screen.getByText('John Doe')).toBeTruthy();
    expect(screen.getByText('1 guest')).toBeTruthy();
    expect(screen.getByText('$1,000.00')).toBeTruthy();
    expect(screen.getByText('Cost: $200.00')).toBeTruthy();
    // Look for the status badge specifically
    const statusBadges = screen.getAllByText('Active');
    expect(statusBadges.length).toBeGreaterThan(0);
    expect(screen.getByText('STR-2024-0001')).toBeTruthy();
  });

  it('should show create form when create button is clicked', () => {
    render(<RentalsPage />, { wrapper: createWrapper() });

    const createButton = screen.getByText('Create Rental');
    fireEvent.click(createButton);

    // The form should be rendered
    expect(screen.getByText('Create Rental')).toBeTruthy();
  });

  it('should filter rentals by status', () => {
    render(<RentalsPage />, { wrapper: createWrapper() });

    const statusSelect = screen.getByDisplayValue('All Statuses');
    fireEvent.change(statusSelect, { target: { value: 'active' } });

    // The hook should be called with the new status filter
    expect(mockUseShortTermRentals).toHaveBeenCalledWith(
      expect.objectContaining({
        status: 'active',
      })
    );
  });

  it('should handle search input', () => {
    render(<RentalsPage />, { wrapper: createWrapper() });

    const searchInput = screen.getByPlaceholderText('Search rentals...');
    fireEvent.change(searchInput, { target: { value: 'test search' } });

    expect(searchInput.nodeValue).toBe('test search');
  });

  it('should handle delete rental with confirmation', async () => {
    const mockDeleteRental = vi.fn();
    mockUseShortTermRentals.mockReturnValue({
      ...defaultMockReturn,
      deleteRental: mockDeleteRental,
    });

    (window.confirm as any).mockReturnValue(true);

    render(<RentalsPage />, { wrapper: createWrapper() });

    const deleteButton = screen.getByTitle('Delete rental');
    fireEvent.click(deleteButton);

    expect(window.confirm).toHaveBeenCalledWith('Are you sure you want to delete this rental?');
    expect(mockDeleteRental).toHaveBeenCalledWith('1');
  });

  it('should not delete rental when confirmation is cancelled', () => {
    const mockDeleteRental = vi.fn();
    mockUseShortTermRentals.mockReturnValue({
      ...defaultMockReturn,
      deleteRental: mockDeleteRental,
    });

    (window.confirm as any).mockReturnValue(false);

    render(<RentalsPage />, { wrapper: createWrapper() });

    const deleteButton = screen.getByTitle('Delete rental');
    fireEvent.click(deleteButton);

    expect(window.confirm).toHaveBeenCalledWith('Are you sure you want to delete this rental?');
    expect(mockDeleteRental).not.toHaveBeenCalled();
  });

  it('should show pagination when multiple pages', () => {
    mockUseShortTermRentals.mockReturnValue({
      ...defaultMockReturn,
      pagination: {
        page: 1,
        limit: 10,
        total: 25,
        pages: 3,
      },
    });

    render(<RentalsPage />, { wrapper: createWrapper() });

    // Check for pagination text (broken up by multiple elements, may be hidden on small screens)
    // Look for the pagination text directly - it should be visible somewhere in the DOM
    const showingText = screen.getByText(/Showing/);
    expect(showingText).toBeTruthy();

    // Check that the full pagination text is present
    const paginationContainer = showingText.closest('.bg-white');
    expect(paginationContainer).toBeTruthy();
    expect(paginationContainer?.textContent).toBe('results');
    expect(screen.getAllByText('Previous')).toHaveLength(2); // Mobile and desktop
    expect(screen.getAllByText('Next')).toHaveLength(2); // Mobile and desktop
  });

  it('should handle pagination navigation', () => {
    mockUseShortTermRentals.mockReturnValue({
      ...defaultMockReturn,
      pagination: {
        page: 1,
        limit: 10,
        total: 25,
        pages: 3,
      },
    });

    render(<RentalsPage />, { wrapper: createWrapper() });

    const nextButtons = screen.getAllByText('Next');
    fireEvent.click(nextButtons[0]); // Click the first Next button

    // This would trigger a re-render with page 2, but we can't easily test that
    // without more complex state management in the test
  });

  it('should show correct guest count for multiple guests', () => {
    const rentalWithMultipleGuests = {
      ...mockRentals[0],
      guests: [
        ...mockRentals[0].guests,
        {
          guest: {
            _id: 'guest2',
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            documentType: 'passport',
            documentNumber: '*********',
            fullName: 'Jane Smith',
          },
          isPrincipalContact: false,
        },
      ],
    };

    mockUseShortTermRentals.mockReturnValue({
      ...defaultMockReturn,
      rentals: [rentalWithMultipleGuests],
    });

    render(<RentalsPage />, { wrapper: createWrapper() });

    expect(screen.getByText('2 guests')).toBeTruthy();
  });

  it('should format dates correctly', () => {
    render(<RentalsPage />, { wrapper: createWrapper() });

    // Check that dates are formatted (exact format may vary by locale)
    expect(screen.getByText(/1\/1\/2024.*1\/7\/2024/)).toBeTruthy();
  });

  it('should format currency correctly', () => {
    render(<RentalsPage />, { wrapper: createWrapper() });

    expect(screen.getByText('$1,000.00')).toBeTruthy();
    expect(screen.getByText('Cost: $200.00')).toBeTruthy();
  });
});

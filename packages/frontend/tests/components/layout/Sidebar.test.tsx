import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent } from '../../utils';
import { Sidebar } from '../../../components/layout/Sidebar';
import { useAuth } from '../../../hooks/useAuth';

// Mock the useAuth hook
vi.mock('../../../hooks/useAuth', () => ({
  useAuth: vi.fn(),
}));

// Helper to render Sidebar with router context
const renderSidebar = (initialEntries = ['/dashboard']) => {
  return render(<Sidebar />, { initialEntries });
};

describe('Sidebar', () => {
  const mockSignOut = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useAuth).mockReturnValue({
      signOut: mockSignOut,
      user: undefined,
      loading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
    });
  });

  it('should render sidebar with brand logo and title', () => {
    renderSidebar();

    expect(screen.getByText('PropertyFin')).toBeInTheDocument();
    expect(screen.getAllByTestId('building2-icon')).toHaveLength(2); // One in brand, one in menu
  });

  it('should render all menu items', () => {
    renderSidebar();

    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Properties')).toBeInTheDocument();
    expect(screen.getByText('Partners')).toBeInTheDocument();
    expect(screen.getByText('Guests')).toBeInTheDocument();
    expect(screen.getByText('Currencies')).toBeInTheDocument();
    expect(screen.getByText('Rentals')).toBeInTheDocument();
    expect(screen.getByText('Recurrent Costs')).toBeInTheDocument();
    expect(screen.getByText('Personal Finance')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
  });

  it('should render all menu icons', () => {
    renderSidebar();

    expect(screen.getByTestId('home-icon')).toBeInTheDocument();
    expect(screen.getAllByTestId('building2-icon')).toHaveLength(2); // One in brand, one in menu
    expect(screen.getByTestId('users-icon')).toBeInTheDocument(); // Partners
    expect(screen.getByTestId('usercheck-icon')).toBeInTheDocument(); // Guests
    expect(screen.getByTestId('dollar-sign-icon')).toBeInTheDocument(); // Currencies
    expect(screen.getByTestId('calendar-icon')).toBeInTheDocument();
    expect(screen.getByTestId('creditcard-icon')).toBeInTheDocument();
    expect(screen.getByTestId('trending-up-icon')).toBeInTheDocument();
    expect(screen.getByTestId('settings-icon')).toBeInTheDocument();
  });

  it('should highlight active tab', () => {
    renderSidebar(['/properties']);

    const propertiesLink = screen.getByRole('link', {
      name: /properties/i,
    });
    expect(propertiesLink).toHaveClass('bg-blue-50', 'text-blue-700');

    const dashboardLink = screen.getByRole('link', { name: /dashboard/i });
    expect(dashboardLink).toHaveClass('text-gray-600');
    expect(dashboardLink).not.toHaveClass('bg-blue-50', 'text-blue-700');
  });

  it('should navigate when menu item is clicked', () => {
    renderSidebar();

    const propertiesLink = screen.getByRole('link', {
      name: /properties/i,
    });

    expect(propertiesLink).toHaveAttribute('href', '/properties');
  });

  it('should render sign out button', () => {
    renderSidebar();

    expect(screen.getByText('Sign Out')).toBeInTheDocument();
    expect(screen.getByTestId('logout-icon')).toBeInTheDocument();
  });

  it('should call signOut when sign out button is clicked', () => {
    renderSidebar();

    const signOutButton = screen.getByRole('button', { name: /sign out/i });
    fireEvent.click(signOutButton);

    expect(mockSignOut).toHaveBeenCalled();
  });
});

import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '../../utils';
import { Checkbox } from '../../../components/ui/Checkbox';

describe('Checkbox', () => {
  it('should render with label', () => {
    render(<Checkbox label="Test checkbox" />);

    expect(screen.getByText('Test checkbox')).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toBeInTheDocument();
  });

  it('should render without label', () => {
    render(<Checkbox />);

    expect(screen.getByRole('checkbox')).toBeInTheDocument();
    expect(screen.queryByText('Test checkbox')).not.toBeInTheDocument();
  });

  it('should handle checked state', () => {
    render(<Checkbox checked={true} onChange={() => {}} />);

    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeChecked();
  });

  it('should handle unchecked state', () => {
    render(<Checkbox checked={false} onChange={() => {}} />);

    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).not.toBeChecked();
  });

  it('should call onChange when clicked', () => {
    const handleChange = vi.fn();
    render(<Checkbox onChange={handleChange} />);

    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);

    expect(handleChange).toHaveBeenCalledTimes(1);
  });

  it('should render indeterminate state', () => {
    render(<Checkbox indeterminate={true} />);

    // Check that the minus icon is rendered for indeterminate state
    const minusIcon = screen.getByTestId('minus-icon') || screen.getByRole('checkbox').parentElement?.querySelector('[data-testid="minus-icon"]');
    expect(minusIcon || screen.getByRole('checkbox')).toBeInTheDocument();
  });

  it('should render with error state', () => {
    render(<Checkbox label="Test" error="This field is required" />);

    expect(screen.getByText('This field is required')).toBeInTheDocument();
    expect(screen.getByText('This field is required')).toHaveClass('text-red-600');
  });

  it('should render with helper text', () => {
    render(<Checkbox label="Test" helperText="This is helpful information" />);

    expect(screen.getByText('This is helpful information')).toBeInTheDocument();
    expect(screen.getByText('This is helpful information')).toHaveClass('text-gray-500');
  });

  it('should render required indicator', () => {
    render(<Checkbox label="Test" required />);

    expect(screen.getByText('*')).toBeInTheDocument();
    expect(screen.getByText('*')).toHaveClass('text-red-500');
  });

  it('should handle disabled state', () => {
    render(<Checkbox label="Test" disabled />);

    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeDisabled();
  });

  it('should render different sizes', () => {
    const { rerender } = render(<Checkbox size="sm" />);
    const checkboxLabel = screen.getByRole('checkbox').nextElementSibling;
    expect(checkboxLabel).toHaveClass('w-4', 'h-4');

    rerender(<Checkbox size="md" />);
    const checkboxLabelMd = screen.getByRole('checkbox').nextElementSibling;
    expect(checkboxLabelMd).toHaveClass('w-5', 'h-5');

    rerender(<Checkbox size="lg" />);
    const checkboxLabelLg = screen.getByRole('checkbox').nextElementSibling;
    expect(checkboxLabelLg).toHaveClass('w-6', 'h-6');
  });

  it('should apply custom className', () => {
    render(<Checkbox className="custom-class" />);

    const checkboxLabel = screen.getByRole('checkbox').nextElementSibling;
    expect(checkboxLabel).toHaveClass('custom-class');
  });

  it('should generate unique id when not provided', () => {
    render(<Checkbox label="Test 1" />);
    render(<Checkbox label="Test 2" />);

    const checkboxes = screen.getAllByRole('checkbox');
    expect(checkboxes[0].id).not.toBe(checkboxes[1].id);
    expect(checkboxes[0].id).toBeTruthy();
    expect(checkboxes[1].id).toBeTruthy();
  });

  it('should use provided id', () => {
    render(<Checkbox id="custom-id" label="Test" />);

    const checkbox = screen.getByRole('checkbox');
    expect(checkbox.id).toBe('custom-id');
  });
});

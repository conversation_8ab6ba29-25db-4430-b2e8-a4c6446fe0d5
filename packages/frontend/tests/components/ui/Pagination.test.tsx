import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '../../utils';
import React from 'react';
import { Pagination } from '../../../components/ui/Pagination';

describe('Pagination', () => {
  const defaultProps = {
    currentPage: 1,
    totalPages: 5,
    onPageChange: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should not render when totalPages is 1 or less', () => {
    const { container } = render(<Pagination {...defaultProps} totalPages={1} />);
    expect(container.firstChild).toBeNull();

    const { container: container2 } = render(<Pagination {...defaultProps} totalPages={0} />);
    expect(container2.firstChild).toBeNull();
  });

  it('should render page numbers', () => {
    render(<Pagination {...defaultProps} />);

    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('4')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('should highlight current page', () => {
    render(<Pagination {...defaultProps} currentPage={3} />);

    const currentPageButton = screen.getByText('3');
    expect(currentPageButton).toHaveClass('font-semibold');
  });

  it('should call onPageChange when page is clicked', () => {
    const onPageChange = vi.fn();
    render(<Pagination {...defaultProps} onPageChange={onPageChange} />);

    fireEvent.click(screen.getByText('3'));
    expect(onPageChange).toHaveBeenCalledWith(3);
  });

  it('should not call onPageChange when current page is clicked', () => {
    const onPageChange = vi.fn();
    render(<Pagination {...defaultProps} currentPage={3} onPageChange={onPageChange} />);

    fireEvent.click(screen.getByText('3'));
    expect(onPageChange).not.toHaveBeenCalled();
  });

  it('should render previous and next buttons', () => {
    render(<Pagination {...defaultProps} currentPage={3} />);

    expect(screen.getByTestId('chevron-left-icon')).toBeInTheDocument();
    expect(screen.getByTestId('chevron-right-icon')).toBeInTheDocument();
  });

  it('should disable previous button on first page', () => {
    render(<Pagination {...defaultProps} currentPage={1} />);

    const prevButton = screen.getByTestId('chevron-left-icon').closest('button');
    expect(prevButton).toBeDisabled();
  });

  it('should disable next button on last page', () => {
    render(<Pagination {...defaultProps} currentPage={5} />);

    const nextButton = screen.getByTestId('chevron-right-icon').closest('button');
    expect(nextButton).toBeDisabled();
  });

  it('should handle previous button click', () => {
    const onPageChange = vi.fn();
    render(<Pagination {...defaultProps} currentPage={3} onPageChange={onPageChange} />);

    const prevButton = screen.getByTestId('chevron-left-icon').closest('button');
    fireEvent.click(prevButton);

    expect(onPageChange).toHaveBeenCalledWith(2);
  });

  it('should handle next button click', () => {
    const onPageChange = vi.fn();
    render(<Pagination {...defaultProps} currentPage={3} onPageChange={onPageChange} />);

    const nextButton = screen.getByTestId('chevron-right-icon').closest('button');
    fireEvent.click(nextButton);

    expect(onPageChange).toHaveBeenCalledWith(4);
  });

  it('should render first and last buttons when enabled', () => {
    render(<Pagination {...defaultProps} currentPage={3} showFirstLast={true} />);

    expect(screen.getByText('First')).toBeInTheDocument();
    expect(screen.getByText('Last')).toBeInTheDocument();
  });

  it('should handle first button click', () => {
    const onPageChange = vi.fn();
    render(<Pagination {...defaultProps} currentPage={3} onPageChange={onPageChange} showFirstLast={true} />);

    fireEvent.click(screen.getByText('First'));
    expect(onPageChange).toHaveBeenCalledWith(1);
  });

  it('should handle last button click', () => {
    const onPageChange = vi.fn();
    render(<Pagination {...defaultProps} currentPage={3} onPageChange={onPageChange} showFirstLast={true} />);

    fireEvent.click(screen.getByText('Last'));
    expect(onPageChange).toHaveBeenCalledWith(5);
  });

  it('should render ellipsis for large page counts', () => {
    render(<Pagination {...defaultProps} totalPages={20} currentPage={10} maxVisiblePages={5} />);

    // Should show ellipsis when there are many pages
    const ellipsisElements = screen.getAllByTestId('more-horizontal-icon') || [];
    expect(ellipsisElements.length).toBeGreaterThan(0);
  });

  it('should hide first/last buttons when showFirstLast is false', () => {
    render(<Pagination {...defaultProps} currentPage={3} showFirstLast={false} />);

    expect(screen.queryByText('First')).not.toBeInTheDocument();
    expect(screen.queryByText('Last')).not.toBeInTheDocument();
  });

  it('should hide prev/next buttons when showPrevNext is false', () => {
    render(<Pagination {...defaultProps} currentPage={3} showPrevNext={false} />);

    expect(screen.queryByLabelText('Previous page')).not.toBeInTheDocument();
    expect(screen.queryByLabelText('Next page')).not.toBeInTheDocument();
  });

  it('should apply custom className', () => {
    const { container } = render(<Pagination {...defaultProps} className="custom-pagination" />);

    expect(container.firstChild).toHaveClass('custom-pagination');
  });
});

import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '../../utils';
import React from 'react';
import { Table, TableColumn } from '../../../components/ui/Table';

interface TestItem {
  id: string;
  name: string;
  value: number;
}

describe('Table', () => {
  const mockData: TestItem[] = [
    { id: '1', name: 'Item 1', value: 100 },
    { id: '2', name: 'Item 2', value: 200 },
    { id: '3', name: 'Item 3', value: 300 },
  ];

  const mockColumns: TableColumn<TestItem>[] = [
    { key: 'name', header: 'Name' },
    { key: 'value', header: 'Value', render: (item) => `$${item.value}` },
  ];

  it('should render table with data', () => {
    render(<Table data={mockData} columns={mockColumns} />);

    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Value')).toBeInTheDocument();
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('$100')).toBeInTheDocument();
  });

  it('should render loading state', () => {
    render(<Table data={[]} columns={mockColumns} loading={true} />);

    // Check for the loading spinner
    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it('should render empty state', () => {
    render(<Table data={[]} columns={mockColumns} emptyMessage="No items found" />);

    expect(screen.getByText('No items found')).toBeInTheDocument();
  });

  it('should render with selection checkboxes when selectable', () => {
    render(<Table data={mockData} columns={mockColumns} selectable={true} />);

    // Should have header checkbox plus one for each row
    const checkboxes = screen.getAllByRole('checkbox');
    expect(checkboxes).toHaveLength(mockData.length + 1);
  });

  it('should handle select all functionality', () => {
    const onSelectionChange = vi.fn();
    render(<Table data={mockData} columns={mockColumns} selectable={true} onSelectionChange={onSelectionChange} />);

    const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(selectAllCheckbox);

    expect(onSelectionChange).toHaveBeenCalledWith(mockData);
  });

  it('should handle individual item selection', () => {
    const onSelectionChange = vi.fn();
    render(<Table data={mockData} columns={mockColumns} selectable={true} onSelectionChange={onSelectionChange} />);

    const itemCheckbox = screen.getAllByRole('checkbox')[1]; // First item checkbox
    fireEvent.click(itemCheckbox);

    expect(onSelectionChange).toHaveBeenCalledWith([mockData[0]]);
  });

  it('should show indeterminate state when some items selected', () => {
    render(<Table data={mockData} columns={mockColumns} selectable={true} selectedItems={[mockData[0]]} onSelectionChange={vi.fn()} />);

    // Check for the minus icon which indicates indeterminate state in our custom checkbox
    expect(screen.getByTestId('minus-icon')).toBeInTheDocument();
  });

  it('should show all selected state when all items selected', () => {
    render(<Table data={mockData} columns={mockColumns} selectable={true} selectedItems={mockData} onSelectionChange={vi.fn()} />);

    const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
    expect(selectAllCheckbox).toBeChecked();
  });

  it('should render pagination when provided', () => {
    const pagination = {
      currentPage: 1,
      totalPages: 3,
      onPageChange: vi.fn(),
    };

    render(<Table data={mockData} columns={mockColumns} pagination={pagination} />);

    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('should handle pagination page change', () => {
    const onPageChange = vi.fn();
    const pagination = {
      currentPage: 1,
      totalPages: 3,
      onPageChange,
    };

    render(<Table data={mockData} columns={mockColumns} pagination={pagination} />);

    fireEvent.click(screen.getByText('2'));
    expect(onPageChange).toHaveBeenCalledWith(2);
  });

  it('should use custom getItemId function', () => {
    const onSelectionChange = vi.fn();
    const customGetItemId = (item: TestItem) => `custom-${item.id}`;

    render(<Table data={mockData} columns={mockColumns} selectable={true} onSelectionChange={onSelectionChange} getItemId={customGetItemId} />);

    const itemCheckbox = screen.getAllByRole('checkbox')[1];
    fireEvent.click(itemCheckbox);

    expect(onSelectionChange).toHaveBeenCalledWith([mockData[0]]);
  });

  it('should render custom column content', () => {
    const customColumns: TableColumn<TestItem>[] = [
      {
        key: 'custom',
        header: 'Custom',
        render: (item) => <span data-testid={`custom-${item.id}`}>Custom {item.name}</span>,
      },
    ];

    render(<Table data={mockData} columns={customColumns} />);

    expect(screen.getByTestId('custom-1')).toBeInTheDocument();
    expect(screen.getByText('Custom Item 1')).toBeInTheDocument();
  });

  it('should apply custom className', () => {
    const { container } = render(<Table data={mockData} columns={mockColumns} className="custom-table" />);

    expect(container.querySelector('.custom-table')).toBeInTheDocument();
  });

  it('should apply column-specific classes', () => {
    const columnsWithClasses: TableColumn<TestItem>[] = [{ key: 'name', header: 'Name', className: 'name-column', headerClassName: 'name-header' }];

    render(<Table data={mockData} columns={columnsWithClasses} />);

    const header = screen.getByText('Name');
    expect(header).toHaveClass('name-header');
  });

  it('should handle deselection of items', () => {
    const onSelectionChange = vi.fn();
    render(<Table data={mockData} columns={mockColumns} selectable={true} selectedItems={[mockData[0]]} onSelectionChange={onSelectionChange} />);

    const itemCheckbox = screen.getAllByRole('checkbox')[1]; // First item checkbox
    fireEvent.click(itemCheckbox); // Deselect

    expect(onSelectionChange).toHaveBeenCalledWith([]);
  });

  it('should handle select all when some items already selected', () => {
    const onSelectionChange = vi.fn();
    render(<Table data={mockData} columns={mockColumns} selectable={true} selectedItems={[mockData[0]]} onSelectionChange={onSelectionChange} />);

    const selectAllCheckbox = screen.getAllByRole('checkbox')[0];
    fireEvent.click(selectAllCheckbox);

    expect(onSelectionChange).toHaveBeenCalledWith(mockData);
  });
});

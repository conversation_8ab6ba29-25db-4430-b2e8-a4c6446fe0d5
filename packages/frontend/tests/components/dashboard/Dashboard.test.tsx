// React import removed - using JSX transform
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen } from '../../utils';
import { Dashboard } from '../../../components/dashboard/Dashboard';
import { useDashboard } from '../../../hooks/useDashboard';
import { useMonthlyRentals } from '../../../hooks/useMonthlyRentals';

// Mock the useDashboard hook
vi.mock('../../../hooks/useDashboard', () => ({
  useDashboard: vi.fn(),
}));

// Mock the useMonthlyRentals hook
vi.mock('../../../hooks/useMonthlyRentals', () => ({
  useMonthlyRentals: vi.fn(),
}));

// Mock Recharts components
vi.mock('recharts', () => ({
  LineChart: ({ children }: { children: React.ReactNode }) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ResponsiveContainer: ({ children }: { children: React.ReactNode }) => <div data-testid="responsive-container">{children}</div>,
  BarChart: ({ children }: { children: React.ReactNode }) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
}));

describe('Dashboard', () => {
  const mockUseDashboard = vi.mocked(useDashboard);
  const mockUseMonthlyRentals = vi.mocked(useMonthlyRentals);

  beforeEach(() => {
    vi.clearAllMocks();

    // Default mock for monthly rentals
    mockUseMonthlyRentals.mockReturnValue({
      monthlyData: [],
      isLoading: false,
      error: null,
    });
  });

  it('should render loading state', () => {
    mockUseDashboard.mockReturnValue({
      stats: {
        totalProperties: 0,
        totalMonthlyIncome: 0,
        totalPendingDebts: 0,
        netPropertyIncome: 0,
      },
      isLoading: true,
      error: null,
    });

    render(<Dashboard />);

    // Should show loading skeleton cards (6 stat cards + chart + quick actions)
    const loadingCards = screen.getAllByRole('generic').filter((el) => el.className.includes('animate-pulse'));
    expect(loadingCards.length).toBeGreaterThanOrEqual(6);
  });

  it('should render dashboard with stats', async () => {
    const mockStats = {
      totalProperties: 5,
      totalMonthlyIncome: 2500,
      totalPendingDebts: 500,
      netPropertyIncome: 2000,
    };

    mockUseDashboard.mockReturnValue({
      stats: mockStats,
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    // Check header
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Overview of your property portfolio and finances')).toBeInTheDocument();

    // Check stat cards
    expect(screen.getByText('Total Properties')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();

    expect(screen.getByText('This Month Income')).toBeInTheDocument();
    expect(screen.getByText('$2,500.00')).toBeInTheDocument();

    expect(screen.getByText('Rentals This Year')).toBeInTheDocument();
    expect(screen.getByText('Revenue This Year')).toBeInTheDocument();
    expect(screen.getByText('Avg Monthly Revenue')).toBeInTheDocument();

    expect(screen.getByText('This Month Net Income')).toBeInTheDocument();
    expect(screen.getByText('$2,000.00')).toBeInTheDocument();
  });

  it('should render dashboard with zero stats', () => {
    const mockStats = {
      totalProperties: 0,
      totalMonthlyIncome: 0,
      totalPendingDebts: 0,
      netPropertyIncome: 0,
    };

    mockUseDashboard.mockReturnValue({
      stats: mockStats,
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    expect(screen.getAllByText('0')).toHaveLength(2); // Total Properties and Rentals This Year
    expect(screen.getAllByText('$0.00')).toHaveLength(4); // This Month Income, Revenue This Year, Avg Monthly Revenue, This Month Net Income
  });

  it('should handle negative net income', () => {
    const mockStats = {
      totalProperties: 2,
      totalMonthlyIncome: 1000,
      totalPendingDebts: 1500,
      netPropertyIncome: -500,
    };

    mockUseDashboard.mockReturnValue({
      stats: mockStats,
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    expect(screen.getByText('-$500.00')).toBeInTheDocument();
  });

  it('should format currency correctly', () => {
    const mockStats = {
      totalProperties: 1,
      totalMonthlyIncome: 1234.56,
      totalPendingDebts: 789.12,
      netPropertyIncome: 445.44,
    };

    mockUseDashboard.mockReturnValue({
      stats: mockStats,
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    expect(screen.getByText('$1,234.56')).toBeInTheDocument();
    expect(screen.getByText('$445.44')).toBeInTheDocument();
  });

  it('should render quick actions section', () => {
    mockUseDashboard.mockReturnValue({
      stats: {
        totalProperties: 0,
        totalMonthlyIncome: 0,
        totalPendingDebts: 0,
        netPropertyIncome: 0,
      },
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    expect(screen.getByText('Add New Property')).toBeInTheDocument();
    expect(screen.getByText('Create a new property and set ownership')).toBeInTheDocument();
    expect(screen.getByText('Create Rental')).toBeInTheDocument();
    expect(screen.getByText('Set up a new rental agreement')).toBeInTheDocument();
    expect(screen.getByText('Add Guest')).toBeInTheDocument();
    expect(screen.getByText('Register a new guest')).toBeInTheDocument();
  });

  it('should render monthly trends section', () => {
    mockUseDashboard.mockReturnValue({
      stats: {
        totalProperties: 0,
        totalMonthlyIncome: 0,
        totalPendingDebts: 0,
        netPropertyIncome: 0,
      },
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    expect(screen.getByText('Monthly Trends (Over Time)')).toBeInTheDocument();
    expect(screen.getByText('No monthly trend data available')).toBeInTheDocument();
    expect(screen.getByText('Trends will show month-to-month changes over time')).toBeInTheDocument();
  });

  it('should render correct icons for each stat card', () => {
    mockUseDashboard.mockReturnValue({
      stats: {
        totalProperties: 1,
        totalMonthlyIncome: 1000,
        totalPendingDebts: 100,
        netPropertyIncome: 900,
      },
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    expect(screen.getByTestId('building2-icon')).toBeInTheDocument();
    expect(screen.getAllByTestId('trending-up-icon')).toHaveLength(3); // Monthly Income, Avg Monthly Revenue, and Monthly Trends chart
    expect(screen.getAllByTestId('calendar-icon')).toHaveLength(2); // Total Rentals stat card and Create Rental button
    expect(screen.getAllByTestId('bar-chart3-icon')).toHaveLength(3); // Yearly Revenue stat card, chart header, and chart empty state
    expect(screen.getByTestId('dollar-sign-icon')).toBeInTheDocument();
  });

  it('should apply correct color classes for positive net income', () => {
    mockUseDashboard.mockReturnValue({
      stats: {
        totalProperties: 1,
        totalMonthlyIncome: 1000,
        totalPendingDebts: 100,
        netPropertyIncome: 900,
      },
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    // Find the net income card's icon container
    const netIncomeCard = screen.getByText('This Month Net Income').closest('.bg-white');
    const iconContainer = netIncomeCard?.querySelector('.bg-green-500');
    expect(iconContainer).toBeInTheDocument();
  });

  it('should apply correct color classes for negative net income', () => {
    mockUseDashboard.mockReturnValue({
      stats: {
        totalProperties: 1,
        totalMonthlyIncome: 500,
        totalPendingDebts: 1000,
        netPropertyIncome: -500,
      },
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    // Find the net income card's icon container
    const netIncomeCard = screen.getByText('This Month Net Income').closest('.bg-white');
    const iconContainer = netIncomeCard?.querySelector('.bg-red-500');
    expect(iconContainer).toBeInTheDocument();
  });

  it('should handle large numbers correctly', () => {
    const mockStats = {
      totalProperties: 100,
      totalMonthlyIncome: 1000000,
      totalPendingDebts: 250000,
      netPropertyIncome: 750000,
    };

    mockUseDashboard.mockReturnValue({
      stats: mockStats,
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    expect(screen.getByText('100')).toBeInTheDocument();
    expect(screen.getByText('$1,000,000.00')).toBeInTheDocument();
    expect(screen.getByText('$750,000.00')).toBeInTheDocument();
  });

  it('should handle decimal values correctly', () => {
    const mockStats = {
      totalProperties: 3,
      totalMonthlyIncome: 2500.75,
      totalPendingDebts: 500.25,
      netPropertyIncome: 2000.5,
    };

    mockUseDashboard.mockReturnValue({
      stats: mockStats,
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    expect(screen.getByText('$2,500.75')).toBeInTheDocument();
    expect(screen.getByText('$2,000.50')).toBeInTheDocument();
  });

  it('should render all stat cards with correct structure', () => {
    mockUseDashboard.mockReturnValue({
      stats: {
        totalProperties: 1,
        totalMonthlyIncome: 1000,
        totalPendingDebts: 100,
        netPropertyIncome: 900,
      },
      isLoading: false,
      error: null,
    });

    render(<Dashboard />);

    // Check that all stat cards are rendered
    const statCards = screen
      .getAllByRole('generic')
      .filter((el) => el.className.includes('bg-white') && el.className.includes('rounded-xl') && el.className.includes('border-gray-200'));

    // Should have 6 stat cards plus 3 additional sections (Chart, Quick Actions and Monthly Trends)
    expect(statCards.length).toBeGreaterThanOrEqual(6);
  });
});

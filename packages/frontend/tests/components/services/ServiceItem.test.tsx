// React import removed - using JSX transform
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent } from '../../utils';
import { ServiceItem } from '../../../components/services/ServiceItem';
import { IPropertyService } from '@alom-rentals/shared';

// Mock serviceCalculations
vi.mock('../../../lib/serviceCalculations', () => ({
  formatCurrency: (amount: number, symbol = '$') => `${symbol}${amount.toFixed(2)}`,
  getMonthlyEquivalent: (cost: number, frequency: string) => {
    if (frequency === 'quarterly') return cost / 3;
    if (frequency === 'yearly') return cost / 12;
    return cost;
  },
  getFrequencyLabel: (frequency: string) => {
    const labels = {
      monthly: 'Monthly',
      quarterly: 'Quarterly',
      yearly: 'Yearly',
    };
    return labels[frequency as keyof typeof labels] || frequency;
  },
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  Edit2: ({ className }: { className?: string }) => <div data-testid="edit2-icon" className={className} />,
  Trash2: ({ className }: { className?: string }) => <div data-testid="trash2-icon" className={className} />,
  DollarSign: ({ className }: { className?: string }) => <div data-testid="dollar-sign-icon" className={className} />,
  Clock: ({ className }: { className?: string }) => <div data-testid="clock-icon" className={className} />,
  Shield: ({ className }: { className?: string }) => <div data-testid="shield-icon" className={className} />,
  User: ({ className }: { className?: string }) => <div data-testid="user-icon" className={className} />,
  Users: ({ className }: { className?: string }) => <div data-testid="users-icon" className={className} />,
}));

describe('ServiceItem - US-PS-002: View Property Services', () => {
  const mockActiveService: IPropertyService = {
    _id: '1',
    propertyId: 'prop1',
    name: 'Internet Service',
    cost: 50.0,
    frequency: 'monthly',
    status: 'active',
    mandatory: true,
    paidByUser: true,
    paidByPartner: false,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  };

  const mockQuarterlyService: IPropertyService = {
    _id: '2',
    propertyId: 'prop1',
    name: 'Insurance',
    cost: 600.0,
    frequency: 'quarterly',
    status: 'active',
    mandatory: false,
    paidByUser: false,
    paidByPartner: true,
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
  };

  const mockInactiveService: IPropertyService = {
    _id: '3',
    propertyId: 'prop1',
    name: 'Old Service',
    cost: 25.0,
    frequency: 'yearly',
    status: 'inactive',
    mandatory: false,
    paidByUser: true,
    paidByPartner: true,
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
  };

  const defaultProps = {
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    currencySymbol: '$',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('AC2: Each service displays required information', () => {
    it('should display service name', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      expect(screen.getByText('Internet Service')).toBeInTheDocument();
    });

    it('should display service cost with currency symbol', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      expect(screen.getByText('$50.00')).toBeInTheDocument();
      expect(screen.getByTestId('dollar-sign-icon')).toBeInTheDocument();
    });

    it('should display service frequency', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      expect(screen.getByText('Monthly')).toBeInTheDocument();
      expect(screen.getByTestId('clock-icon')).toBeInTheDocument();
    });

    it('should display quarterly frequency correctly', () => {
      render(<ServiceItem {...defaultProps} service={mockQuarterlyService} />);

      expect(screen.getByText('Quarterly')).toBeInTheDocument();
    });

    it('should display yearly frequency correctly', () => {
      render(<ServiceItem {...defaultProps} service={mockInactiveService} />);

      expect(screen.getByText('Yearly')).toBeInTheDocument();
    });

    it('should display service status as active', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      const statusBadge = screen.getByText('active');
      expect(statusBadge).toBeInTheDocument();
      expect(statusBadge).toHaveClass('bg-green-100', 'text-green-800');
    });

    it('should display service status as inactive', () => {
      render(<ServiceItem {...defaultProps} service={mockInactiveService} />);

      const statusBadge = screen.getByText('inactive');
      expect(statusBadge).toBeInTheDocument();
      expect(statusBadge).toHaveClass('bg-gray-100', 'text-gray-800');
    });

    it('should display mandatory indicator for mandatory services', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      expect(screen.getByTestId('shield-icon')).toBeInTheDocument();
    });

    it('should not display mandatory indicator for non-mandatory services', () => {
      render(<ServiceItem {...defaultProps} service={mockQuarterlyService} />);

      expect(screen.queryByTestId('shield-icon')).not.toBeInTheDocument();
    });

    it('should display payment responsibility - user only', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      expect(screen.getByText('Paid by:')).toBeInTheDocument();
      expect(screen.getByText('User')).toBeInTheDocument();
      expect(screen.getByTestId('user-icon')).toBeInTheDocument();
      expect(screen.queryByText('Partner')).not.toBeInTheDocument();
    });

    it('should display payment responsibility - partner only', () => {
      render(<ServiceItem {...defaultProps} service={mockQuarterlyService} />);

      expect(screen.getByText('Paid by:')).toBeInTheDocument();
      expect(screen.getByText('Partner')).toBeInTheDocument();
      expect(screen.getByTestId('users-icon')).toBeInTheDocument();
      expect(screen.queryByText('User')).not.toBeInTheDocument();
    });

    it('should display payment responsibility - both user and partner', () => {
      render(<ServiceItem {...defaultProps} service={mockInactiveService} />);

      expect(screen.getByText('Paid by:')).toBeInTheDocument();
      expect(screen.getByText('User')).toBeInTheDocument();
      expect(screen.getByText('Partner')).toBeInTheDocument();
      expect(screen.getByTestId('user-icon')).toBeInTheDocument();
      expect(screen.getByTestId('users-icon')).toBeInTheDocument();
    });
  });

  describe('Monthly equivalent calculation', () => {
    it('should not show monthly equivalent for monthly services', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      expect(screen.queryByText(/Monthly equivalent:/)).not.toBeInTheDocument();
    });

    it('should show monthly equivalent for quarterly services', () => {
      render(<ServiceItem {...defaultProps} service={mockQuarterlyService} />);

      expect(screen.getByText('Monthly equivalent: $200.00')).toBeInTheDocument();
    });

    it('should show monthly equivalent for yearly services', () => {
      render(<ServiceItem {...defaultProps} service={mockInactiveService} />);

      expect(screen.getByText('Monthly equivalent: $2.08')).toBeInTheDocument();
    });
  });

  describe('Visual states', () => {
    it('should apply opacity for inactive services', () => {
      render(<ServiceItem {...defaultProps} service={mockInactiveService} />);

      // Find the main service container (the outermost div with bg-white)
      const serviceContainer = screen.getByText('Old Service').closest('.bg-white');
      expect(serviceContainer).toHaveClass('opacity-60');
    });

    it('should not apply opacity for active services', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      // Find the main service container (the outermost div with bg-white)
      const serviceContainer = screen.getByText('Internet Service').closest('.bg-white');
      expect(serviceContainer).not.toHaveClass('opacity-60');
    });

    it('should show hover effects on service container', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      // Find the main service container (the outermost div with bg-white)
      const serviceContainer = screen.getByText('Internet Service').closest('.bg-white');
      expect(serviceContainer).toHaveClass('hover:shadow-md', 'transition-shadow');
    });
  });

  describe('Action buttons', () => {
    it('should display edit and delete buttons', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      expect(screen.getByTestId('edit2-icon')).toBeInTheDocument();
      expect(screen.getByTestId('trash2-icon')).toBeInTheDocument();
    });

    it('should call onEdit when edit button is clicked', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      const editButton = screen.getByTestId('edit2-icon').closest('button');
      fireEvent.click(editButton!);

      expect(defaultProps.onEdit).toHaveBeenCalledWith(mockActiveService);
    });

    it('should show delete confirmation modal when delete button is clicked', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      const deleteButton = screen.getByTestId('trash2-icon').closest('button');
      fireEvent.click(deleteButton!);

      expect(screen.getByText('Delete Service')).toBeInTheDocument();
      expect(screen.getByText(/Are you sure you want to delete "Internet Service"/)).toBeInTheDocument();
    });

    it('should call onDelete when delete is confirmed', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      // Click delete button to open modal
      const deleteButton = screen.getByTestId('trash2-icon').closest('button');
      fireEvent.click(deleteButton!);

      // Click confirm delete
      const confirmButton = screen.getByText('Delete');
      fireEvent.click(confirmButton);

      expect(defaultProps.onDelete).toHaveBeenCalledWith('1');
    });

    it('should close modal when cancel is clicked', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} />);

      // Click delete button to open modal
      const deleteButton = screen.getByTestId('trash2-icon').closest('button');
      fireEvent.click(deleteButton!);

      // Click cancel
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);

      expect(screen.queryByText('Delete Service')).not.toBeInTheDocument();
    });
  });

  describe('Currency formatting', () => {
    it('should use custom currency symbol', () => {
      render(<ServiceItem {...defaultProps} service={mockActiveService} currencySymbol="€" />);

      // The mock formatCurrency function should use the custom symbol
      expect(screen.getByText('€50.00')).toBeInTheDocument();
    });

    it('should format costs with two decimal places', () => {
      const serviceWithDecimal = { ...mockActiveService, cost: 123.456 };
      render(<ServiceItem {...defaultProps} service={serviceWithDecimal} />);

      expect(screen.getByText('$123.46')).toBeInTheDocument();
    });
  });
});

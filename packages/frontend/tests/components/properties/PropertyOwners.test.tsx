import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PropertyOwners } from '../../../components/properties/PropertyOwners';
import { usePartners } from '../../../hooks/usePartners';
import { authAPI } from '../../../lib/api';
import { IOwner } from '@alom-rentals/shared';

// Mock the hooks and API
vi.mock('../../../hooks/usePartners');
vi.mock('../../../lib/api');

// Mock the PartnerAddModal component
vi.mock('../../../components/partners/PartnerAddModal', () => ({
  PartnerAddModal: ({ isOpen, onClose, onSuccess, title, submitButtonText }: any) =>
    isOpen ? (
      <div data-testid="partner-add-modal">
        <div>{title}</div>
        <button onClick={() => onSuccess({ email: '<EMAIL>', name: 'New Partner' })}>{submitButtonText}</button>
        <button onClick={onClose}>Close</button>
      </div>
    ) : null,
}));

const mockPartners = [
  { _id: '1', email: '<EMAIL>', name: 'Partner One', createdBy: 'user1', createdAt: new Date(), updatedAt: new Date() },
  { _id: '2', email: '<EMAIL>', name: 'Partner Two', createdBy: 'user1', createdAt: new Date(), updatedAt: new Date() },
];

const mockUsers = [
  { _id: 'user1', email: '<EMAIL>', fullName: 'User One' },
  { _id: 'user2', email: '<EMAIL>', fullName: 'User Two' },
];

const defaultOwners: IOwner[] = [
  {
    ownerType: 'User',
    email: '<EMAIL>',
    name: 'Current User',
    ownershipPercentage: 100,
  },
];

const mockOnChange = vi.fn();

describe('PropertyOwners', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    vi.mocked(usePartners).mockReturnValue({
      partners: mockPartners,
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: vi.fn(),
      deletePartner: vi.fn(),
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    vi.mocked(authAPI.searchUsers).mockResolvedValue({
      data: mockUsers,
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    });
  });

  it('should render property owners section with current user', () => {
    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    expect(screen.getByText('Property Owners')).toBeInTheDocument();
    expect(screen.getByText('Total: 100.0%')).toBeInTheDocument();
    expect(screen.getByText('Current User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Current User (You)')).toBeInTheDocument();
  });

  it("should show validation error when percentages don't sum to 100%", () => {
    const invalidOwners: IOwner[] = [
      {
        ownerType: 'User',
        email: '<EMAIL>',
        name: 'Current User',
        ownershipPercentage: 75,
      },
    ];

    render(<PropertyOwners owners={invalidOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    expect(screen.getByText('Total: 75.0%')).toBeInTheDocument();
    expect(screen.getByText('Must equal 100%')).toBeInTheDocument();
  });

  it('should allow updating current user ownership percentage', () => {
    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    const percentageInput = screen.getByDisplayValue('100');
    fireEvent.change(percentageInput, { target: { value: '75' } });

    expect(mockOnChange).toHaveBeenCalledWith([
      {
        ownerType: 'User',
        email: '<EMAIL>',
        name: 'Current User',
        ownershipPercentage: 75,
      },
    ]);
  });

  it('should render additional owners with all fields', () => {
    const ownersWithAdditional: IOwner[] = [
      ...defaultOwners,
      {
        ownerType: 'Partner',
        email: '<EMAIL>',
        name: 'Test Partner',
        ownershipPercentage: 25,
      },
    ];

    render(<PropertyOwners owners={ownersWithAdditional} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test Partner')).toBeInTheDocument();
    expect(screen.getByDisplayValue('25')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Partner')).toBeInTheDocument();
  });

  it('should allow adding a new owner', () => {
    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    const addOwnerButton = screen.getByText('Add Owner');
    fireEvent.click(addOwnerButton);

    expect(mockOnChange).toHaveBeenCalledWith([
      ...defaultOwners,
      {
        ownerType: 'Partner',
        email: '',
        name: '',
        ownershipPercentage: 0,
      },
    ]);
  });

  it('should allow removing additional owners but not current user', () => {
    const ownersWithAdditional: IOwner[] = [
      ...defaultOwners,
      {
        ownerType: 'Partner',
        email: '<EMAIL>',
        name: 'Test Partner',
        ownershipPercentage: 25,
      },
    ];

    render(<PropertyOwners owners={ownersWithAdditional} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    // Should have remove button for additional owner
    const removeButton = screen.getByTitle('Remove owner');
    fireEvent.click(removeButton);

    expect(mockOnChange).toHaveBeenCalledWith(defaultOwners);
  });

  it('should handle user search and display results', async () => {
    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    const searchInput = screen.getByPlaceholderText('Search by email or name...');
    fireEvent.change(searchInput, { target: { value: 'user' } });

    await waitFor(() => {
      expect(authAPI.searchUsers).toHaveBeenCalledWith('user');
    });

    await waitFor(() => {
      expect(screen.getByText('User One')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('User Two')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });
  });

  it('should add user as owner from search results', async () => {
    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    const searchInput = screen.getByPlaceholderText('Search by email or name...');
    fireEvent.change(searchInput, { target: { value: 'user' } });

    await waitFor(() => {
      expect(screen.getByText('User One')).toBeInTheDocument();
    });

    const userButton = screen.getByText('User One').closest('button');
    fireEvent.click(userButton!);

    expect(mockOnChange).toHaveBeenCalledWith([
      ...defaultOwners,
      {
        ownerType: 'User',
        email: '<EMAIL>',
        name: 'User One',
        ownershipPercentage: 0,
      },
    ]);
  });

  it('should display existing partners for quick add', () => {
    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    expect(screen.getByText('Add Existing Partner')).toBeInTheDocument();
    expect(screen.getByText('Partner One')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Partner Two')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('should add partner as owner from existing partners', () => {
    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    const partnerButton = screen.getByText('Partner One').closest('button');
    fireEvent.click(partnerButton!);

    expect(mockOnChange).toHaveBeenCalledWith([
      ...defaultOwners,
      {
        ownerType: 'Partner',
        email: '<EMAIL>',
        name: 'Partner One',
        ownershipPercentage: 0,
      },
    ]);
  });

  it('should open partner add modal when create partner button is clicked', () => {
    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    const createPartnerButton = screen.getByText('Create Partner');
    fireEvent.click(createPartnerButton);

    expect(screen.getByTestId('partner-add-modal')).toBeInTheDocument();
    expect(screen.getByText('Create New Partner')).toBeInTheDocument();
    expect(screen.getByText('Create & Add')).toBeInTheDocument();
  });

  it('should handle partner creation and add as owner', () => {
    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    const createPartnerButton = screen.getByText('Create Partner');
    fireEvent.click(createPartnerButton);

    const createAndAddButton = screen.getByText('Create & Add');
    fireEvent.click(createAndAddButton);

    expect(mockOnChange).toHaveBeenCalledWith([
      ...defaultOwners,
      {
        ownerType: 'Partner',
        email: '<EMAIL>',
        name: 'New Partner',
        ownershipPercentage: 0,
      },
    ]);
  });

  it('should handle owner field updates', () => {
    const ownersWithAdditional: IOwner[] = [
      ...defaultOwners,
      {
        ownerType: 'Partner',
        email: '<EMAIL>',
        name: 'Test Partner',
        ownershipPercentage: 25,
      },
    ];

    render(<PropertyOwners owners={ownersWithAdditional} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    // Update email
    const emailInput = screen.getByDisplayValue('<EMAIL>');
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    expect(mockOnChange).toHaveBeenCalledWith([
      defaultOwners[0],
      {
        ownerType: 'Partner',
        email: '<EMAIL>',
        name: 'Test Partner',
        ownershipPercentage: 25,
      },
    ]);
  });

  it('should handle search with short query', async () => {
    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    const searchInput = screen.getByPlaceholderText('Search by email or name...');
    fireEvent.change(searchInput, { target: { value: 'u' } }); // Short query

    // Should not call API for short queries
    await waitFor(() => {
      expect(authAPI.searchUsers).not.toHaveBeenCalled();
    });
  });

  it('should handle search API error gracefully', async () => {
    vi.mocked(authAPI.searchUsers).mockRejectedValue(new Error('Search failed'));
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    const searchInput = screen.getByPlaceholderText('Search by email or name...');
    fireEvent.change(searchInput, { target: { value: 'user' } });

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Error searching users:', expect.any(Error));
    });

    consoleSpy.mockRestore();
  });

  it('should not show existing partners section when no partners available', () => {
    vi.mocked(usePartners).mockReturnValue({
      partners: [], // No partners
      isLoading: false,
      error: null,
      useGetPartner: vi.fn(),
      createPartner: vi.fn(),
      updatePartner: vi.fn(),
      deletePartner: vi.fn(),
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });

    render(<PropertyOwners owners={defaultOwners} onChange={mockOnChange} currentUserEmail="<EMAIL>" currentUserName="Current User" />);

    expect(screen.queryByText('Add Existing Partner')).not.toBeInTheDocument();
  });
});

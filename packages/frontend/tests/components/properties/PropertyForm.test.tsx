// React import removed - using JSX transform
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { PropertyForm } from '../../../components/properties/PropertyForm';
import { useAuth } from '../../../hooks/useAuth';
import { useProperties } from '../../../hooks/useProperties';
import { useCurrencies } from '../../../hooks/useCurrencies';
import { render } from '../../utils';

// Mock the hooks
vi.mock('../../../hooks/useAuth');
vi.mock('../../../hooks/useProperties');
vi.mock('../../../hooks/useCurrencies');

// Mock the PropertyOwners component
vi.mock('../../../components/properties/PropertyOwners', () => ({
  PropertyOwners: ({ owners, onChange }: any) => {
    const totalPercentage = owners.reduce((sum: number, owner: any) => sum + owner.ownershipPercentage, 0);
    return (
      <div data-testid="property-owners">
        <div>Owners: {owners.length}</div>
        <div>Total: {totalPercentage.toFixed(1)}%</div>
        {Math.abs(totalPercentage - 100) > 0.01 && <div>Must equal 100%</div>}
        <button
          onClick={() => {
            // When adding a new owner, reduce the first owner's percentage to make room
            const updatedOwners = [...owners];
            if (updatedOwners.length > 0) {
              updatedOwners[0] = {
                ...updatedOwners[0],
                ownershipPercentage: 50,
              };
            }
            onChange([
              ...updatedOwners,
              {
                ownerType: 'Partner',
                email: '<EMAIL>',
                name: 'Test Partner',
                ownershipPercentage: 25, // This will make total = 75% (50 + 25)
              },
            ]);
          }}
        >
          Add Owner
        </button>
      </div>
    );
  },
}));

const mockUser = {
  _id: 'user123',
  email: '<EMAIL>',
  fullName: 'Test User',
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockOnCancel = vi.fn();
const mockOnSuccess = vi.fn();
const mockCreateProperty = vi.fn();

describe('PropertyForm', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    vi.mocked(useAuth).mockReturnValue({
      user: { ...mockUser, id: mockUser._id },
      loading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    // Ensure createProperty always returns a proper structure
    mockCreateProperty.mockResolvedValue({ success: true, error: null });

    vi.mocked(useProperties).mockReturnValue({
      properties: [],
      isLoading: false,
      error: null,
      createProperty: mockCreateProperty,
      updateProperty: vi.fn(),
      // deleteProperty: vi.fn(), // Not in useProperties interface
      // getProperty: vi.fn(), // Not in useProperties interface
      isCreating: false,
      isUpdating: false,
    });

    vi.mocked(useCurrencies).mockReturnValue({
      currencies: [
        { _id: 'currency-1', name: 'US Dollar', code: 'USD', symbol: '$', createdBy: 'user1', createdAt: new Date(), updatedAt: new Date() },
        { _id: 'currency-2', name: 'Euro', code: 'EUR', symbol: '€', createdBy: 'user1', createdAt: new Date(), updatedAt: new Date() },
      ],
      loading: false,
      error: null,
      createCurrency: vi.fn(),
      updateCurrency: vi.fn(),
      deleteCurrency: vi.fn(),
      refetch: vi.fn(),
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });
  });

  it('should render the form with all required fields', () => {
    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    expect(screen.getByText('Add New Property')).toBeInTheDocument();
    expect(screen.getByText('Create a new property and set ownership details')).toBeInTheDocument();

    // Property details section
    expect(screen.getByText('Property Details')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('e.g., Downtown Apartment')).toBeInTheDocument();
    expect(screen.getAllByRole('combobox')).toHaveLength(2); // Property type and currency selects
    expect(screen.getByPlaceholderText('Full property address')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Additional details about the property')).toBeInTheDocument();
    expect(screen.getByRole('checkbox')).toBeInTheDocument();

    // Property owners section
    expect(screen.getByTestId('property-owners')).toBeInTheDocument();

    // Action buttons
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /create property/i })).toBeInTheDocument();
  });

  it('should initialize with current user as default owner', () => {
    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Check that PropertyOwners component receives the current user as initial owner
    expect(screen.getByText('Owners: 1')).toBeInTheDocument();
  });

  it('should handle form field changes', () => {
    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const nameInput = screen.getByPlaceholderText('e.g., Downtown Apartment');
    const typeSelect = screen.getAllByRole('combobox')[0]; // First combobox is property type
    const addressInput = screen.getByPlaceholderText('Full property address');
    const descriptionInput = screen.getByPlaceholderText('Additional details about the property');
    const rentedCheckbox = screen.getByRole('checkbox');

    fireEvent.change(nameInput, { target: { value: 'Test Property' } });
    fireEvent.change(typeSelect, { target: { value: 'commercial' } });
    fireEvent.change(addressInput, { target: { value: '123 Test St' } });
    fireEvent.change(descriptionInput, {
      target: { value: 'Test description' },
    });
    fireEvent.click(rentedCheckbox);

    expect(nameInput).toHaveValue('Test Property');
    expect(typeSelect).toHaveValue('commercial');
    expect(addressInput).toHaveValue('123 Test St');
    expect(descriptionInput).toHaveValue('Test description');
    expect(rentedCheckbox).toBeChecked();
  });

  it('should handle cancel button click', () => {
    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });

  it('should handle back arrow button click', () => {
    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const backButton = screen.getByRole('button', { name: '' }); // ArrowLeft icon button
    fireEvent.click(backButton);

    expect(mockOnCancel).toHaveBeenCalledTimes(1);
  });

  it('should validate ownership percentages and disable submit button', () => {
    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Fill required fields
    const nameInput = screen.getByPlaceholderText('e.g., Downtown Apartment');
    fireEvent.change(nameInput, { target: { value: 'Test Property' } });

    // Add an owner with invalid percentage (total won't be 100%)
    const addOwnerButton = screen.getByText('Add Owner');
    fireEvent.click(addOwnerButton);

    // Should show validation error in the PropertyOwners component
    expect(screen.getByText('Must equal 100%')).toBeInTheDocument();
    expect(screen.getByText('Total: 75.0%')).toBeInTheDocument();

    // Submit button should be disabled when percentages don't sum to 100%
    const submitButton = screen.getByRole('button', {
      name: /create property/i,
    });
    expect(submitButton).toBeDisabled();
  });

  it('should submit form successfully with valid data', async () => {
    mockCreateProperty.mockResolvedValue({ success: true, error: null });

    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Fill required fields
    const nameInput = screen.getByPlaceholderText('e.g., Downtown Apartment');
    fireEvent.change(nameInput, { target: { value: 'Test Property' } });

    // Submit form (default owner has 100% ownership)
    const submitButton = screen.getByRole('button', {
      name: /create property/i,
    });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCreateProperty).toHaveBeenCalledWith({
        name: 'Test Property',
        address: '',
        description: '',
        propertyType: 'residential',
        isRented: false,
        owners: [
          {
            ownerType: 'User',
            email: '<EMAIL>',
            name: 'Test User',
            ownershipPercentage: 100,
          },
        ],
      });
    });

    expect(mockOnSuccess).toHaveBeenCalledTimes(1);
  });

  it('should handle form submission error', async () => {
    const errorMessage = 'Failed to create property';
    mockCreateProperty.mockResolvedValue({ error: { message: errorMessage } });

    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Fill required fields
    const nameInput = screen.getByPlaceholderText('e.g., Downtown Apartment');
    fireEvent.change(nameInput, { target: { value: 'Test Property' } });

    // Submit form
    const submitButton = screen.getByRole('button', {
      name: /create property/i,
    });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    expect(mockOnSuccess).not.toHaveBeenCalled();
  });

  it('should disable submit button when creating', () => {
    vi.mocked(useProperties).mockReturnValue({
      properties: [],
      isLoading: false,
      error: null,
      createProperty: mockCreateProperty,
      updateProperty: vi.fn(),
      // deleteProperty: vi.fn(), // Not in useProperties interface
      // getProperty: vi.fn(), // Not in useProperties interface
      isCreating: true, // Set to true
      isUpdating: false,
    });

    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const submitButton = screen.getByRole('button', { name: /creating.../i });
    expect(submitButton).toBeDisabled();
  });

  // Note: This test is covered by the validation test above

  it('should handle owners change from PropertyOwners component', () => {
    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Initially shows 1 owner
    expect(screen.getByText('Owners: 1')).toBeInTheDocument();

    // Add owner through the mocked component
    const addOwnerButton = screen.getByText('Add Owner');
    fireEvent.click(addOwnerButton);

    // Should now show 2 owners
    expect(screen.getByText('Owners: 2')).toBeInTheDocument();
  });

  it('should render all property type options', () => {
    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Just check that the options exist, don't need to get the select element

    expect(screen.getByRole('option', { name: 'Residential' })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: 'Commercial' })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: 'Industrial' })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: 'Land' })).toBeInTheDocument();
  });

  it('should clear error when form is resubmitted', async () => {
    // First submission with error
    mockCreateProperty.mockResolvedValueOnce({
      success: false,
      error: { message: 'First error' },
    });

    render(<PropertyForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const nameInput = screen.getByPlaceholderText('e.g., Downtown Apartment');
    fireEvent.change(nameInput, { target: { value: 'Test Property' } });

    const submitButton = screen.getByRole('button', {
      name: /create property/i,
    });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('First error')).toBeInTheDocument();
    });

    // Second submission should clear the error
    mockCreateProperty.mockResolvedValueOnce({ success: true, error: null });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.queryByText('First error')).not.toBeInTheDocument();
    });
  });
});

// React import removed - using JS<PERSON> transform
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { PropertyEditForm } from '../../../components/properties/PropertyEditForm';
import { useAuth } from '../../../hooks/useAuth';
import { useProperties } from '../../../hooks/useProperties';
import { useCurrencies } from '../../../hooks/useCurrencies';
import { render } from '../../utils';

// Mock hooks
vi.mock('../../../hooks/useAuth');
vi.mock('../../../hooks/useProperties');
vi.mock('../../../hooks/useCurrencies');

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  ArrowLeft: () => <div data-testid="arrow-left-icon" />,
  Save: () => <div data-testid="save-icon" />,
  X: () => <div data-testid="x-icon" />,
  Plus: () => <div data-testid="plus-icon" />,
  AlertCircle: () => <div data-testid="alert-circle-icon" />,
  AlertTriangle: () => <div data-testid="alert-triangle-icon" />,
  Info: () => <div data-testid="info-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  Trash2: () => <div data-testid="trash-icon" />,
  User: () => <div data-testid="user-icon" />,
  Search: () => <div data-testid="search-icon" />,
}));

const mockProperty = {
  _id: 'property-1',
  name: 'Test Property',
  address: '123 Test St',
  description: 'Test description',
  propertyType: 'residential' as const,
  isRented: false,
  currencyId: 'currency-1',
  createdBy: 'user-1',
  owners: [
    {
      ownerType: 'User' as const,
      owner: {
        id: 'user-1',
        email: '<EMAIL>',
        fullName: 'Test User',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      ownershipPercentage: 100,
    },
  ],
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockCurrencies = [
  {
    _id: 'currency-1',
    name: 'US Dollar',
    symbol: '$',
    code: 'USD',
    createdBy: 'user-1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  {
    _id: 'currency-2',
    name: 'Euro',
    symbol: '€',
    code: 'EUR',
    createdBy: 'user-1',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
];

const mockUser = {
  _id: 'user-1',
  email: '<EMAIL>',
  fullName: 'Test User',
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('PropertyEditForm', () => {
  const mockUpdateProperty = vi.fn();
  const mockOnCancel = vi.fn();
  const mockOnSuccess = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    vi.mocked(useAuth).mockReturnValue({
      user: { ...mockUser, id: mockUser._id },
      loading: false,
      signIn: vi.fn(),
      signUp: vi.fn(),
      signOut: vi.fn(),
    });

    vi.mocked(useProperties).mockReturnValue({
      properties: [],
      isLoading: false,
      error: null,
      createProperty: vi.fn(),
      updateProperty: mockUpdateProperty,
      isCreating: false,
      isUpdating: false,
    });

    vi.mocked(useCurrencies).mockReturnValue({
      currencies: mockCurrencies,
      loading: false,
      error: null,
      createCurrency: vi.fn(),
      updateCurrency: vi.fn(),
      deleteCurrency: vi.fn(),
      refetch: vi.fn(),
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
    });
  });

  it('should render edit form with pre-filled data', () => {
    render(<PropertyEditForm property={mockProperty} onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    expect(screen.getByDisplayValue('Test Property')).toBeInTheDocument();
    expect(screen.getByDisplayValue('123 Test St')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Test description')).toBeInTheDocument();

    // Check that the property type select has the correct value
    const propertyTypeSelect = screen.getByLabelText('Property Type');
    expect(propertyTypeSelect).toHaveValue('residential');
  });

  it('should show currency options', () => {
    render(<PropertyEditForm property={mockProperty} onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const currencySelect = screen.getByLabelText('Currency');
    expect(currencySelect).toBeInTheDocument();

    // Check if currency options are present
    expect(screen.getByText('US Dollar ($)')).toBeInTheDocument();
    expect(screen.getByText('Euro (€)')).toBeInTheDocument();
  });

  it('should handle form submission with changes', async () => {
    mockUpdateProperty.mockResolvedValue({ data: mockProperty, error: null });

    render(<PropertyEditForm property={mockProperty} onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Change the property name
    const nameInput = screen.getByDisplayValue('Test Property');
    fireEvent.change(nameInput, { target: { value: 'Updated Property' } });

    // Submit the form
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockUpdateProperty).toHaveBeenCalledWith('property-1', {
        name: 'Updated Property',
      });
    });

    expect(mockOnSuccess).toHaveBeenCalled();
  });

  it('should handle form submission without changes', async () => {
    render(<PropertyEditForm property={mockProperty} onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Submit the form without changes
    const saveButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(mockUpdateProperty).not.toHaveBeenCalled();
      // If no changes, the form should not call onCancel, it should just do nothing
      expect(mockOnCancel).not.toHaveBeenCalled();
    });
  });

  it('should validate ownership percentages', async () => {
    render(<PropertyEditForm property={mockProperty} onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Find and change ownership percentage to invalid value
    const ownershipInput = screen.getByDisplayValue('100');
    fireEvent.change(ownershipInput, { target: { value: '50' } });

    // Submit the form
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText('Ownership percentages must sum to 100%')).toBeInTheDocument();
    });

    expect(mockUpdateProperty).not.toHaveBeenCalled();
  });

  it('should handle update error', async () => {
    mockUpdateProperty.mockResolvedValue({
      data: null,
      error: { message: 'Update failed' },
    });

    render(<PropertyEditForm property={mockProperty} onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Change the property name
    const nameInput = screen.getByDisplayValue('Test Property');
    fireEvent.change(nameInput, { target: { value: 'Updated Property' } });

    // Submit the form
    const saveButton = screen.getByText('Save Changes');
    fireEvent.click(saveButton);

    await waitFor(() => {
      expect(screen.getByText('Update failed')).toBeInTheDocument();
    });

    expect(mockOnSuccess).not.toHaveBeenCalled();
  });

  it('should handle cancel button', () => {
    render(<PropertyEditForm property={mockProperty} onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should disable save button when no changes', () => {
    render(<PropertyEditForm property={mockProperty} onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const saveButton = screen.getByRole('button', { name: /save changes/i });
    expect(saveButton).toBeDisabled();
  });

  it('should enable save button when changes are made', async () => {
    render(<PropertyEditForm property={mockProperty} onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Change the property name
    const nameInput = screen.getByDisplayValue('Test Property');
    fireEvent.change(nameInput, { target: { value: 'Updated Property' } });

    await waitFor(() => {
      const saveButton = screen.getByText('Save Changes');
      expect(saveButton).not.toBeDisabled();
    });
  });

  it('should show loading state during update', async () => {
    vi.mocked(useProperties).mockReturnValue({
      properties: [],
      isLoading: false,
      error: null,
      createProperty: vi.fn(),
      updateProperty: mockUpdateProperty,
      isCreating: false,
      isUpdating: true, // Set updating to true
    });

    render(<PropertyEditForm property={mockProperty} onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    expect(screen.getByText('Saving...')).toBeInTheDocument();
  });
});

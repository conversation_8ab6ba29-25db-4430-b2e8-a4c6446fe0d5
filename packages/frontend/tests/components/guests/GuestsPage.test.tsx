import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../utils';
import { GuestsPage } from '../../../components/guests/GuestsPage';
import { useGuests } from '../../../hooks/useGuests';
import { mockGuest } from '../../utils';

// Mock the useGuests hook
vi.mock('../../../hooks/useGuests', () => ({
  useGuests: vi.fn(),
}));

// Mock the GuestForm component
vi.mock('../../../components/guests/GuestForm', () => ({
  GuestForm: ({ onCancel, onSuccess }: { onCancel: () => void; onSuccess: () => void }) => (
    <div data-testid="guest-form">
      <button onClick={onCancel} data-testid="cancel-button">
        Cancel
      </button>
      <button onClick={onSuccess} data-testid="success-button">
        Success
      </button>
    </div>
  ),
}));

describe('GuestsPage', () => {
  const mockUseGuests = vi.mocked(useGuests);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render loading state', () => {
    mockUseGuests.mockReturnValue({
      guests: [],
      isLoading: true,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    expect(screen.getByText('Loading guests...')).toBeInTheDocument();
    const spinners = screen.getAllByRole('generic').filter((el) => el.className.includes('animate-spin'));
    expect(spinners).toHaveLength(1);
  });

  it('should render empty state when no guests', () => {
    mockUseGuests.mockReturnValue({
      guests: [],
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    expect(screen.getByText('Guests')).toBeInTheDocument();
    expect(screen.getByText('Manage guest information for your rental properties')).toBeInTheDocument();
    expect(screen.getByText('No guests yet')).toBeInTheDocument();
    expect(screen.getByText('Start by adding your first guest to manage rental bookings')).toBeInTheDocument();
    expect(screen.getByText('Add Your First Guest')).toBeInTheDocument();
  });

  it('should render guests list when guests exist', () => {
    const mockGuests = [
      {
        ...mockGuest,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '+*********0',
        documentType: 'National ID' as const,
        documentNumber: '*********',
        relationshipWithOwner: 'Guest',
        isForeigner: false,
        cityOfResidency: 'New York',
      },
      {
        ...mockGuest,
        _id: 'guest2',
        firstName: 'Jane',
        lastName: 'Smith',
        email: '<EMAIL>',
        documentType: 'Passport' as const,
        documentNumber: 'P123456',
        relationshipWithOwner: 'Friend',
        isForeigner: true,
        nationality: 'Canadian',
      },
    ];

    mockUseGuests.mockReturnValue({
      guests: mockGuests,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getAllByText('+*********0')).toHaveLength(2); // Both guests have same phone
    expect(screen.getByText('National ID: *********')).toBeInTheDocument();
    expect(screen.getByText('Passport: P123456')).toBeInTheDocument();
  });

  it('should show guest type badges correctly', () => {
    const mockGuests = [
      {
        ...mockGuest,
        firstName: 'Local',
        lastName: 'Guest',
        isForeigner: false,
      },
      {
        ...mockGuest,
        _id: 'guest2',
        firstName: 'Foreign',
        lastName: 'Guest',
        isForeigner: true,
        nationality: 'Canadian',
      },
    ];

    mockUseGuests.mockReturnValue({
      guests: mockGuests,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    expect(screen.getByText('Local')).toBeInTheDocument();
    expect(screen.getByText('Foreign')).toBeInTheDocument();
  });

  it('should show correct icons for guest types', () => {
    const mockGuests = [
      {
        ...mockGuest,
        firstName: 'Local',
        lastName: 'Guest',
        isForeigner: false,
      },
      {
        ...mockGuest,
        _id: 'guest2',
        firstName: 'Foreign',
        lastName: 'Guest',
        isForeigner: true,
      },
    ];

    mockUseGuests.mockReturnValue({
      guests: mockGuests,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    expect(screen.getAllByTestId('map-pin-icon')).toHaveLength(1); // Local guest
    expect(screen.getAllByTestId('globe-icon')).toHaveLength(1); // Foreign guest
  });

  it('should show guest details correctly', () => {
    const mockGuests = [
      {
        ...mockGuest,
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phoneNumber: '+*********0',
        documentType: 'National ID' as const,
        documentNumber: '*********',
        relationshipWithOwner: 'Guest',
        isForeigner: false,
        cityOfResidency: 'New York',
        dateOfBirth: new Date('1990-01-01'),
      },
    ];

    mockUseGuests.mockReturnValue({
      guests: mockGuests,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('+*********0')).toBeInTheDocument();
    expect(screen.getByText('National ID: *********')).toBeInTheDocument();
    expect(screen.getByText('New York')).toBeInTheDocument();
    expect(screen.getByText('Born: 1/1/1990')).toBeInTheDocument();
  });

  it('should show foreign guest nationality', () => {
    const mockGuests = [
      {
        ...mockGuest,
        firstName: 'Jane',
        lastName: 'Smith',
        isForeigner: true,
        nationality: 'Canadian',
      },
    ];

    mockUseGuests.mockReturnValue({
      guests: mockGuests,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    expect(screen.getByText('Canadian')).toBeInTheDocument();
  });

  it('should open guest form when clicking add guest button', async () => {
    mockUseGuests.mockReturnValue({
      guests: [],
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    const addButton = screen.getByText('Add Guest');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByTestId('guest-form')).toBeInTheDocument();
    });
  });

  it('should open guest form when clicking add first guest button', async () => {
    mockUseGuests.mockReturnValue({
      guests: [],
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    const addFirstGuestButton = screen.getByText('Add Your First Guest');
    fireEvent.click(addFirstGuestButton);

    await waitFor(() => {
      expect(screen.getByTestId('guest-form')).toBeInTheDocument();
    });
  });

  it('should close guest form when cancel is clicked', async () => {
    mockUseGuests.mockReturnValue({
      guests: [],
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    // Open form
    const addButton = screen.getByText('Add Guest');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByTestId('guest-form')).toBeInTheDocument();
    });

    // Cancel form
    const cancelButton = screen.getByTestId('cancel-button');
    fireEvent.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByTestId('guest-form')).not.toBeInTheDocument();
      expect(screen.getByText('Guests')).toBeInTheDocument();
    });
  });

  it('should close guest form when success is triggered', async () => {
    mockUseGuests.mockReturnValue({
      guests: [],
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    // Open form
    const addButton = screen.getByText('Add Guest');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(screen.getByTestId('guest-form')).toBeInTheDocument();
    });

    // Trigger success
    const successButton = screen.getByTestId('success-button');
    fireEvent.click(successButton);

    await waitFor(() => {
      expect(screen.queryByTestId('guest-form')).not.toBeInTheDocument();
      expect(screen.getByText('Guests')).toBeInTheDocument();
    });
  });

  it('should not show phone number if not provided', () => {
    const mockGuests = [
      {
        ...mockGuest,
        firstName: 'John',
        lastName: 'Doe',
        phoneNumber: undefined,
      },
    ];

    mockUseGuests.mockReturnValue({
      guests: mockGuests,
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: vi.fn(),
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestsPage />);

    expect(screen.queryByTestId('phone-icon')).not.toBeInTheDocument();
  });
});

// React import removed - using JS<PERSON> transform
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../utils';
import { GuestForm } from '../../../components/guests/GuestForm';
import { useGuests } from '../../../hooks/useGuests';

// Mock the useGuests hook
vi.mock('../../../hooks/useGuests', () => ({
  useGuests: vi.fn(),
}));

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  ArrowLeft: ({ className }: { className?: string }) => <div data-testid="arrow-left-icon" className={className} />,
  User: ({ className }: { className?: string }) => <div data-testid="user-icon" className={className} />,
  Globe: ({ className }: { className?: string }) => <div data-testid="globe-icon" className={className} />,
  MapPin: ({ className }: { className?: string }) => <div data-testid="map-pin-icon" className={className} />,
}));

describe('GuestForm', () => {
  const mockCreateGuest = vi.fn();
  const mockOnCancel = vi.fn();
  const mockOnSuccess = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useGuests).mockReturnValue({
      guests: [],
      isLoading: false,
      isCreating: false,
      isUpdating: false,
      isDeleting: false,
      createGuest: mockCreateGuest,
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });
  });

  it('should render form with basic fields', () => {
    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    expect(screen.getByText('Create New Guest')).toBeInTheDocument();
    expect(screen.getByText('Add guest information for rental bookings')).toBeInTheDocument();
    expect(screen.getByText('Basic Information')).toBeInTheDocument();

    expect(screen.getByPlaceholderText('Enter first name')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter last name')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByDisplayValue('National ID')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter document number')).toBeInTheDocument();
  });

  it('should render guest type selection', () => {
    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    expect(screen.getByText('Guest Type')).toBeInTheDocument();
    expect(screen.getByText('Local Guest')).toBeInTheDocument();
    expect(screen.getByText('Foreign Guest')).toBeInTheDocument();
  });

  it('should show local guest as default selection', () => {
    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const localRadio = screen.getByRole('radio', {
      name: /local guest/i,
    }) as HTMLInputElement;
    const foreignRadio = screen.getByRole('radio', {
      name: /foreign guest/i,
    }) as HTMLInputElement;

    expect(localRadio.checked).toBe(true);
    expect(foreignRadio.checked).toBe(false);
  });

  it('should switch to foreign guest and show additional fields', async () => {
    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const foreignRadio = screen.getByRole('radio', { name: /foreign guest/i });
    fireEvent.click(foreignRadio);

    await waitFor(() => {
      expect(screen.getByText('Foreign Guest Information')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter nationality')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter country of origin')).toBeInTheDocument();
    });
  });

  it('should handle form input changes', async () => {
    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const firstNameInput = screen.getByPlaceholderText('Enter first name');
    const lastNameInput = screen.getByPlaceholderText('Enter last name');
    const emailInput = screen.getByPlaceholderText('<EMAIL>');

    fireEvent.change(firstNameInput, { target: { value: 'John' } });
    fireEvent.change(lastNameInput, { target: { value: 'Doe' } });
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

    expect(firstNameInput).toHaveValue('John');
    expect(lastNameInput).toHaveValue('Doe');
    expect(emailInput).toHaveValue('<EMAIL>');
  });

  it('should validate required fields on submit', async () => {
    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const submitButton = screen.getByRole('button', { name: /create guest/i });
    fireEvent.click(submitButton);

    // The form should have HTML5 validation that prevents submission
    // We can't easily test for specific error messages, but the form should still be visible
    expect(screen.getByText('Create New Guest')).toBeInTheDocument();
    expect(submitButton).toBeInTheDocument();
    expect(mockCreateGuest).not.toHaveBeenCalled();
  });

  it('should validate foreign guest required fields', async () => {
    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Switch to foreign guest
    const foreignRadio = screen.getByLabelText('Foreign Guest');
    fireEvent.click(foreignRadio);

    // Fill basic required fields using placeholders
    fireEvent.change(screen.getByPlaceholderText('Enter first name'), {
      target: { value: 'John' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter last name'), {
      target: { value: 'Doe' },
    });
    fireEvent.change(screen.getByPlaceholderText('<EMAIL>'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter document number'), {
      target: { value: '123456' },
    });

    const submitButton = screen.getByRole('button', { name: /create guest/i });
    fireEvent.click(submitButton);

    // The form should still be visible since validation failed
    expect(screen.getByText('Create New Guest')).toBeInTheDocument();
    expect(submitButton).toBeInTheDocument();
    expect(mockCreateGuest).not.toHaveBeenCalled();
  });

  it('should submit form with valid local guest data', async () => {
    mockCreateGuest.mockResolvedValue({ data: { _id: '123' }, error: null });

    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Fill required fields using placeholders
    fireEvent.change(screen.getByPlaceholderText('Enter first name'), {
      target: { value: 'John' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter last name'), {
      target: { value: 'Doe' },
    });
    fireEvent.change(screen.getByPlaceholderText('<EMAIL>'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter document number'), {
      target: { value: '123456' },
    });

    const submitButton = screen.getByRole('button', { name: /create guest/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCreateGuest).toHaveBeenCalledWith(
        expect.objectContaining({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          documentNumber: '123456',
          isForeigner: false,
        })
      );
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });

  it('should submit form with valid foreign guest data', async () => {
    mockCreateGuest.mockResolvedValue({ data: { _id: '123' }, error: null });

    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Switch to foreign guest
    const foreignRadio = screen.getByLabelText('Foreign Guest');
    fireEvent.click(foreignRadio);

    await waitFor(() => {
      expect(screen.getByPlaceholderText('Enter nationality')).toBeInTheDocument();
    });

    // Fill required fields using placeholders
    fireEvent.change(screen.getByPlaceholderText('Enter first name'), {
      target: { value: 'Jane' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter last name'), {
      target: { value: 'Smith' },
    });
    fireEvent.change(screen.getByPlaceholderText('<EMAIL>'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter document number'), {
      target: { value: 'P123456' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter nationality'), {
      target: { value: 'Canadian' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter country of origin'), {
      target: { value: 'Canada' },
    });

    const submitButton = screen.getByRole('button', { name: /create guest/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockCreateGuest).toHaveBeenCalledWith(
        expect.objectContaining({
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          documentNumber: 'P123456',
          isForeigner: true,
          nationality: 'Canadian',
          countryOfOrigin: 'Canada',
        })
      );
      expect(mockOnSuccess).toHaveBeenCalled();
    });
  });

  it('should handle API errors', async () => {
    const errorMessage = 'Email already exists';
    mockCreateGuest.mockResolvedValue({
      data: null,
      error: {
        message: errorMessage,
      },
    });

    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Fill required fields using placeholders
    fireEvent.change(screen.getByPlaceholderText('Enter first name'), {
      target: { value: 'John' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter last name'), {
      target: { value: 'Doe' },
    });
    fireEvent.change(screen.getByPlaceholderText('<EMAIL>'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter document number'), {
      target: { value: '123456' },
    });

    const submitButton = screen.getByRole('button', { name: /create guest/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });

    expect(mockOnSuccess).not.toHaveBeenCalled();
  });

  it('should handle generic API errors', async () => {
    mockCreateGuest.mockResolvedValue({
      data: null,
      error: {},
    });

    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Fill required fields using placeholders
    fireEvent.change(screen.getByPlaceholderText('Enter first name'), {
      target: { value: 'John' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter last name'), {
      target: { value: 'Doe' },
    });
    fireEvent.change(screen.getByPlaceholderText('<EMAIL>'), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByPlaceholderText('Enter document number'), {
      target: { value: '123456' },
    });

    const submitButton = screen.getByRole('button', { name: /create guest/i });
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Failed to create guest')).toBeInTheDocument();
    });

    expect(mockOnSuccess).not.toHaveBeenCalled();
  });

  it('should call onCancel when back button is clicked', () => {
    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const backButton = screen.getByText('Back to Guests');
    fireEvent.click(backButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should show loading state when creating guest', async () => {
    vi.mocked(useGuests).mockReturnValue({
      guests: [],
      isLoading: false,
      isCreating: true,
      isUpdating: false,
      isDeleting: false,
      createGuest: mockCreateGuest,
      updateGuest: vi.fn(),
      deleteGuest: vi.fn(),
      searchGuests: vi.fn(),
      refetch: vi.fn(),
    });

    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const submitButton = screen.getByRole('button', { name: /creating/i });
    expect(submitButton).toBeDisabled();
  });

  it('should clear error when switching guest types', async () => {
    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    // Trigger validation error
    const submitButton = screen.getByRole('button', { name: /create guest/i });
    fireEvent.click(submitButton);

    // The form should still be visible since validation failed
    expect(screen.getByText('Create New Guest')).toBeInTheDocument();
    expect(submitButton).toBeInTheDocument();

    // Switch guest type
    const foreignRadio = screen.getByLabelText('Foreign Guest');
    fireEvent.click(foreignRadio);

    // Form should still be functional after switching
    expect(screen.getByText('Create New Guest')).toBeInTheDocument();
    expect(foreignRadio).toBeChecked();
  });

  it('should handle document type selection', async () => {
    render(<GuestForm onCancel={mockOnCancel} onSuccess={mockOnSuccess} />);

    const documentTypeSelect = screen.getByRole('combobox');
    fireEvent.change(documentTypeSelect, { target: { value: 'Passport' } });

    expect(documentTypeSelect).toHaveValue('Passport');
  });
});

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import CurrenciesPage from '../../../components/currencies/CurrenciesPage';
import { useCurrencies } from '../../../hooks/useCurrencies';
import { ICurrency } from '@alom-rentals/shared';

// Mock the hook
vi.mock('../../../hooks/useCurrencies');
const mockUseCurrencies = useCurrencies as any;

// Mock window.confirm
const mockConfirm = vi.fn();
Object.defineProperty(window, 'confirm', { value: mockConfirm });

// Mock window.alert
const mockAlert = vi.fn();
Object.defineProperty(window, 'alert', { value: mockAlert });

const mockCurrencies: ICurrency[] = [
  {
    _id: '1',
    name: 'US Dollar',
    symbol: '$',
    code: 'USD',
    createdBy: 'user1',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    _id: '2',
    name: 'Euro',
    symbol: '€',
    code: 'EUR',
    createdBy: 'user1',
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
  },
];

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return function TestWrapper({ children }: { children: React.ReactNode }) {
    return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
  };
};
describe('CurrenciesPage', () => {
  const mockCreateCurrency = vi.fn();
  const mockUpdateCurrency = vi.fn();
  const mockDeleteCurrency = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    mockUseCurrencies.mockReturnValue({
      currencies: mockCurrencies,
      loading: false,
      error: null,
      createCurrency: mockCreateCurrency,
      updateCurrency: mockUpdateCurrency,
      deleteCurrency: mockDeleteCurrency,
      refetch: vi.fn(),
    });
  });

  describe('Rendering', () => {
    it('should render currencies page with title', () => {
      render(<CurrenciesPage />, { wrapper: createWrapper() });

      expect(screen.getByText('Currency Management')).toBeTruthy();
      expect(screen.getByText('Add Currency')).toBeTruthy();
    });

    it('should show loading state', () => {
      mockUseCurrencies.mockReturnValue({
        currencies: [],
        loading: true,
        error: null,
        createCurrency: mockCreateCurrency,
        updateCurrency: mockUpdateCurrency,
        deleteCurrency: mockDeleteCurrency,
        refetch: vi.fn(),
      });

      render(<CurrenciesPage />, { wrapper: createWrapper() });

      expect(screen.getByRole('status')).toBeTruthy(); // Loading spinner
    });

    it('should show error message', () => {
      const errorMessage = 'Failed to fetch currencies';
      mockUseCurrencies.mockReturnValue({
        currencies: [],
        loading: false,
        error: { message: errorMessage },
        createCurrency: mockCreateCurrency,
        updateCurrency: mockUpdateCurrency,
        deleteCurrency: mockDeleteCurrency,
        refetch: vi.fn(),
      });

      render(<CurrenciesPage />, { wrapper: createWrapper() });

      expect(screen.getByText(errorMessage)).toBeTruthy();
    });

    it('should display currencies list', () => {
      render(<CurrenciesPage />, { wrapper: createWrapper() });

      expect(screen.getByText('US Dollar')).toBeTruthy();
      expect(screen.getByText('USD')).toBeTruthy();
      expect(screen.getByText('$')).toBeTruthy();

      expect(screen.getByText('Euro')).toBeTruthy();
      expect(screen.getByText('EUR')).toBeTruthy();
      expect(screen.getByText('€')).toBeTruthy();
    });

    it('should show empty state when no currencies', () => {
      mockUseCurrencies.mockReturnValue({
        currencies: [],
        loading: false,
        error: null,
        createCurrency: mockCreateCurrency,
        updateCurrency: mockUpdateCurrency,
        deleteCurrency: mockDeleteCurrency,
        refetch: vi.fn(),
      });

      render(<CurrenciesPage />, { wrapper: createWrapper() });

      expect(screen.getByText('No currencies found. Create your first currency to get started.')).toBeTruthy();
    });
  });

  describe('Currency Form', () => {
    it('should open form when Add Currency is clicked', () => {
      render(<CurrenciesPage />, { wrapper: createWrapper() });

      fireEvent.click(screen.getByText('Add Currency'));

      expect(screen.getByText('Add New Currency')).toBeTruthy();
      expect(screen.getByLabelText('Currency Name *')).toBeTruthy();
      expect(screen.getByLabelText('Currency Symbol *')).toBeTruthy();
      expect(screen.getByLabelText('Currency Code')).toBeTruthy();
    });

    it('should close form when Cancel is clicked', () => {
      render(<CurrenciesPage />, { wrapper: createWrapper() });

      fireEvent.click(screen.getByText('Add Currency'));
      expect(screen.getByText('Add New Currency')).toBeTruthy();

      fireEvent.click(screen.getByText('Cancel'));
      expect(screen.queryByText('Add New Currency')).not.toBeTruthy();
    });

    it('should create currency with valid data', async () => {
      mockCreateCurrency.mockResolvedValue({});

      render(<CurrenciesPage />, { wrapper: createWrapper() });

      fireEvent.click(screen.getByText('Add Currency'));

      fireEvent.change(screen.getByLabelText('Currency Name *'), {
        target: { value: 'British Pound' },
      });
      fireEvent.change(screen.getByLabelText('Currency Symbol *'), {
        target: { value: '£' },
      });
      fireEvent.change(screen.getByLabelText('Currency Code'), {
        target: { value: 'gbp' },
      });

      fireEvent.click(screen.getByText('Create'));

      await waitFor(() => {
        expect(mockCreateCurrency).toHaveBeenCalledWith({
          name: 'British Pound',
          symbol: '£',
          code: 'GBP', // Should be converted to uppercase
        });
      });
    });

    it('should handle create currency error', async () => {
      const errorMessage = 'Currency name already exists';
      mockCreateCurrency.mockRejectedValue(new Error(errorMessage));

      render(<CurrenciesPage />, { wrapper: createWrapper() });

      fireEvent.click(screen.getByText('Add Currency'));

      fireEvent.change(screen.getByLabelText('Currency Name *'), {
        target: { value: 'US Dollar' },
      });
      fireEvent.change(screen.getByLabelText('Currency Symbol *'), {
        target: { value: '$' },
      });

      fireEvent.click(screen.getByText('Create'));

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeTruthy();
      });
    });

    it('should open edit form with currency data', () => {
      render(<CurrenciesPage />, { wrapper: createWrapper() });

      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]); // Edit first currency

      expect(screen.getByText('Edit Currency')).toBeTruthy();
      expect(screen.getByDisplayValue('US Dollar')).toBeTruthy();
      expect(screen.getByDisplayValue('$')).toBeTruthy();
      expect(screen.getByDisplayValue('USD')).toBeTruthy();
    });

    it('should update currency', async () => {
      mockUpdateCurrency.mockResolvedValue({});

      render(<CurrenciesPage />, { wrapper: createWrapper() });

      const editButtons = screen.getAllByText('Edit');
      fireEvent.click(editButtons[0]);

      fireEvent.change(screen.getByDisplayValue('US Dollar'), {
        target: { value: 'United States Dollar' },
      });

      fireEvent.click(screen.getByText('Update'));

      await waitFor(() => {
        expect(mockUpdateCurrency).toHaveBeenCalledWith('1', {
          name: 'United States Dollar',
          symbol: '$',
          code: 'USD',
        });
      });
    });
  });

  describe('Currency Deletion', () => {
    it('should delete currency when confirmed', async () => {
      mockConfirm.mockReturnValue(true);
      mockDeleteCurrency.mockResolvedValue({});

      render(<CurrenciesPage />, { wrapper: createWrapper() });

      const deleteButtons = screen.getAllByText('Delete');
      fireEvent.click(deleteButtons[0]);

      expect(mockConfirm).toHaveBeenCalledWith('Are you sure you want to delete this currency?');

      await waitFor(() => {
        expect(mockDeleteCurrency).toHaveBeenCalledWith('1');
      });
    });

    it('should not delete currency when cancelled', () => {
      mockConfirm.mockReturnValue(false);

      render(<CurrenciesPage />, { wrapper: createWrapper() });

      const deleteButtons = screen.getAllByText('Delete');
      fireEvent.click(deleteButtons[0]);

      expect(mockConfirm).toHaveBeenCalled();
      expect(mockDeleteCurrency).not.toHaveBeenCalled();
    });

    it('should handle delete currency error', async () => {
      const errorMessage = 'Cannot delete currency that is being used by properties';
      mockConfirm.mockReturnValue(true);
      mockDeleteCurrency.mockRejectedValue(new Error(errorMessage));

      render(<CurrenciesPage />, { wrapper: createWrapper() });

      const deleteButtons = screen.getAllByText('Delete');
      fireEvent.click(deleteButtons[0]);

      await waitFor(() => {
        expect(mockAlert).toHaveBeenCalledWith(errorMessage);
      });
    });
  });

  describe('Form Validation', () => {
    it('should require currency name', () => {
      render(<CurrenciesPage />, { wrapper: createWrapper() });

      fireEvent.click(screen.getByText('Add Currency'));

      const nameInput = screen.getByLabelText('Currency Name *');
      expect(nameInput.getAttribute('required')).toBeTruthy();
    });

    it('should require currency symbol', () => {
      render(<CurrenciesPage />, { wrapper: createWrapper() });

      fireEvent.click(screen.getByText('Add Currency'));

      const symbolInput = screen.getByLabelText('Currency Symbol *');
      expect(symbolInput.getAttribute('required')).toBeTruthy();
      expect(symbolInput.getAttribute('maxLength')).toBe('3');
    });

    it('should limit currency code length', () => {
      render(<CurrenciesPage />, { wrapper: createWrapper() });

      fireEvent.click(screen.getByText('Add Currency'));

      const codeInput = screen.getByLabelText('Currency Code');
      expect(codeInput.getAttribute('maxLength')).toBe('3');
    });

    it('should convert currency code to uppercase', () => {
      render(<CurrenciesPage />, { wrapper: createWrapper() });

      fireEvent.click(screen.getByText('Add Currency'));

      const codeInput = screen.getByLabelText('Currency Code');
      fireEvent.change(codeInput, { target: { value: 'usd' } });

      expect(codeInput.nodeValue).toBe('USD');
    });
  });
});

import '@testing-library/jest-dom';
import { beforeAll, afterEach, vi, beforeEach } from 'vitest';
import { cleanup } from '@testing-library/react';
import React from 'react';

// Mock lucide-react icons globally
vi.mock('lucide-react', () => {
  const MockIcon = (props: Record<string, unknown>) => {
    const testId = props['data-testid'] || 'mock-icon';
    return React.createElement('div', { 'data-testid': testId, ...props });
  };

  return {
    Home: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'home-icon', ...props }),
    Building: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'building-icon', ...props }),
    Building2: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'building2-icon', ...props }),
    Users: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'users-icon', ...props }),
    User: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'user-icon', ...props }),
    UserCheck: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'usercheck-icon', ...props }),
    UserPlus: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'user-plus-icon', ...props }),
    Settings: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'settings-icon', ...props }),
    LogOut: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'logout-icon', ...props }),
    LogIn: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'login-icon', ...props }),
    Plus: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'plus-icon', ...props }),
    Edit: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'edit-icon', ...props }),
    Edit2: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'edit2-icon', ...props }),
    Trash2: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'trash2-icon', ...props }),
    Search: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'search-icon', ...props }),
    X: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'x-icon', ...props }),
    ArrowLeft: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'arrow-left-icon', ...props }),
    DollarSign: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'dollar-sign-icon', ...props }),
    Calendar: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'calendar-icon', ...props }),
    MapPin: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'map-pin-icon', ...props }),
    Mail: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'mail-icon', ...props }),
    Phone: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'phone-icon', ...props }),
    Eye: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'eye-icon', ...props }),
    EyeOff: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'eye-off-icon', ...props }),
    ChevronDown: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'chevron-down-icon', ...props }),
    Crown: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'crown-icon', ...props }),
    AlertCircle: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'alert-circle-icon', ...props }),
    AlertTriangle: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'alert-triangle-icon', ...props }),
    Info: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'info-icon', ...props }),
    CheckCircle: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'check-circle-icon', ...props }),
    TrendingUp: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'trending-up-icon', ...props }),
    TrendingDown: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'trending-down-icon', ...props }),
    BarChart3: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'bar-chart3-icon', ...props }),
    Calculator: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'calculator-icon', ...props }),
    CreditCard: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'creditcard-icon', ...props }),
    FileText: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'file-text-icon', ...props }),
    Globe: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'globe-icon', ...props }),
    Percent: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'percent-icon', ...props }),
    ChevronLeft: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'chevron-left-icon', ...props }),
    ChevronRight: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'chevron-right-icon', ...props }),
    List: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'list-icon', ...props }),
    Grid: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'grid-icon', ...props }),
    Clock: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'clock-icon', ...props }),
    XCircle: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'x-circle-icon', ...props }),
    Check: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'check-icon', ...props }),
    Minus: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'minus-icon', ...props }),
    Shield: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'shield-icon', ...props }),
    MoreHorizontal: (props: Record<string, unknown>) => MockIcon({ 'data-testid': 'more-horizontal-icon', ...props }),
  };
});

// Global test setup
beforeAll(() => {
  // Mock window.matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));
});

beforeEach(() => {
  vi.clearAllMocks();
});

// Cleanup after each test
afterEach(() => {
  cleanup();
});

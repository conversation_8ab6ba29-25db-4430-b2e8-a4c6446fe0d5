import axios from 'axios';
import {
  IPropertyCreateData,
  IPropertyUpdateData,
  IPropertyServiceCreateData,
  IPropertyServiceUpdateData,
  IPartnerCreateData,
  IPartnerUpdateData,
  ICurrencyCreateData,
  ICurrencyUpdateData,
  IGuestCreateData,
  IGuestUpdateData,
  IShortTermRentalCreateData,
  ILoginCredentials,
  IRegisterData,
  ITransactionUpdateData,
  ITransactionCreateData,
} from '@alom-rentals/shared';
import { UseTransactionsParams } from '../hooks/useTransactions';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance
export const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials: ILoginCredentials) => api.post('/auth/login', credentials),

  register: (data: IRegisterData) => api.post('/auth/register', data),

  getCurrentUser: () => api.get('/auth/me'),

  searchUsers: (query: string) => api.get(`/auth/search?q=${encodeURIComponent(query)}`),
};

// Properties API
export const propertiesAPI = {
  getAll: () => api.get('/properties'),

  getById: (id: string) => api.get(`/properties/${id}`),

  create: (data: IPropertyCreateData, customerId?: string) => {
    const url = customerId ? `/properties?customerId=${customerId}` : '/properties';
    return api.post(url, data);
  },

  update: (id: string, data: IPropertyUpdateData) => api.put(`/properties/${id}`, data),

  delete: (id: string) => api.delete(`/properties/${id}`),

  bulkDelete: (propertyIds: string[]) => api.delete('/properties/bulk', { data: { propertyIds } }),
};

// Property Services API
export const propertyServicesAPI = {
  getAll: (propertyId: string) => api.get(`/properties/${propertyId}/services`),

  getSummary: (propertyId: string) => api.get(`/properties/${propertyId}/services/summary`),

  create: (propertyId: string, data: IPropertyServiceCreateData) => api.post(`/properties/${propertyId}/services`, data),

  update: (propertyId: string, serviceId: string, data: IPropertyServiceUpdateData) => api.put(`/properties/${propertyId}/services/${serviceId}`, data),

  delete: (propertyId: string, serviceId: string) => api.delete(`/properties/${propertyId}/services/${serviceId}`),
};

// Partners API
export const partnersAPI = {
  getAll: () => api.get('/partners'),

  getById: (id: string) => api.get(`/partners/${id}`),

  create: (data: IPartnerCreateData) => api.post('/partners', data),

  update: (id: string, data: IPartnerUpdateData) => api.put(`/partners/${id}`, data),

  delete: (id: string) => api.delete(`/partners/${id}`),

  bulkDelete: (partnerIds: string[]) => api.delete('/partners/bulk', { data: { partnerIds } }),
};

// Guests API
export const guestsAPI = {
  getAll: () => api.get('/guests'),

  getById: (id: string) => api.get(`/guests/${id}`),

  create: (guestData: IGuestCreateData) => api.post('/guests', guestData),

  update: (id: string, guestData: IGuestUpdateData) => api.put(`/guests/${id}`, guestData),

  delete: (id: string) => api.delete(`/guests/${id}`),

  search: (query: string) => api.get(`/guests/search/${encodeURIComponent(query)}`),

  bulkDelete: (guestIds: string[]) => api.delete('/guests/bulk', { data: { guestIds } }),
};

// Dashboard API
export const dashboardAPI = {
  getStats: () => api.get('/dashboard/stats'),
  getMonthlyRentals: () => api.get('/dashboard/monthly-rentals'),
};

// Currencies API
export const currenciesAPI = {
  getAll: () => api.get('/currencies'),

  getById: (id: string) => api.get(`/currencies/${id}`),

  create: (data: ICurrencyCreateData) => api.post('/currencies', data),

  update: (id: string, data: ICurrencyUpdateData) => api.put(`/currencies/${id}`, data),

  delete: (id: string) => api.delete(`/currencies/${id}`),

  bulkDelete: (currencyIds: string[]) => api.delete('/currencies/bulk', { data: { currencyIds } }),
};

// Short-term Rentals API
export const shortTermRentalsAPI = {
  getAll: (params?: { page?: number; limit?: number; status?: string; propertyId?: string }) => {
    const searchParams = new URLSearchParams();
    if (params?.page) searchParams.append('page', params.page.toString());
    if (params?.limit) searchParams.append('limit', params.limit.toString());
    if (params?.status) searchParams.append('status', params.status);
    if (params?.propertyId) searchParams.append('propertyId', params.propertyId);

    const queryString = searchParams.toString();
    return api.get(`/short-term-rentals${queryString ? `?${queryString}` : ''}`);
  },

  getById: (id: string) => api.get(`/short-term-rentals/${id}`),

  calculateCost: (propertyId: string) => api.get(`/short-term-rentals/calculate-cost/${propertyId}`),

  create: (data: IShortTermRentalCreateData) => api.post('/short-term-rentals', data),

  update: (id: string, data: Partial<IShortTermRentalCreateData>) => api.put(`/short-term-rentals/${id}`, data),

  delete: (id: string) => api.delete(`/short-term-rentals/${id}`),

  bulkDelete: (rentalIds: string[]) => api.delete('/short-term-rentals/bulk', { data: { rentalIds } }),
};

export const transactionsAPI = {
  getAll: ({ page = 1, limit = 20, propertyId, type } : UseTransactionsParams = { page: 1, limit: 20 }) => {
    const searchParams = new URLSearchParams();
    if (page) searchParams.append('page', page.toString());
    if (limit) searchParams.append('limit', limit.toString());
    if (type) searchParams.append('type', type.toString());
    if (propertyId) searchParams.append('propertyId', propertyId);

    const queryString = searchParams.toString();
    return api.get(`/transactions${queryString ? `?${queryString}` : ''}`);
  },

  getById: (id: string) => api.get(`/transactions/${id}`),

  create: (data: ITransactionCreateData) => api.post('/transactions', data),

  update: (id: string, data: Partial<ITransactionUpdateData>) => api.put(`/transactions/${id}`, data),

  delete: (id: string) => api.delete(`/transactions/${id}`),

  getSummary: (propertyId: string) => api.post('/transactions/summary', { data: { propertyId } }),
};

export default api;

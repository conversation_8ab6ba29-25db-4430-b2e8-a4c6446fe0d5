import { IPropertyService, ServiceFrequency, IServiceCostSummary } from '@alom-rentals/shared';

/**
 * Convert service cost to monthly equivalent based on frequency
 */
export const getMonthlyEquivalent = (cost: number, frequency: ServiceFrequency): number => {
  switch (frequency) {
    case 'monthly':
      return cost;
    case 'quarterly':
      return cost / 3;
    case 'yearly':
      return cost / 12;
    default:
      return 0;
  }
};

/**
 * Calculate total service costs for a list of services
 */
export const calculateServiceCosts = (services: IPropertyService[]): IServiceCostSummary => {
  const activeServices = services.filter((service) => service.status === 'active');

  let totalMonthlyCost = 0;
  let userMonthlyCost = 0;
  let partnerMonthlyCost = 0;
  let mandatoryMonthlyCost = 0;

  activeServices.forEach((service) => {
    const monthlyEquivalent = getMonthlyEquivalent(service.cost, service.frequency);
    totalMonthlyCost += monthlyEquivalent;

    if (service.paidByUser) {
      userMonthlyCost += monthlyEquivalent;
    }
    if (service.paidByPartner) {
      partnerMonthlyCost += monthlyEquivalent;
    }
    if (service.mandatory) {
      mandatoryMonthlyCost += monthlyEquivalent;
    }
  });

  return {
    totalMonthlyCost: Math.round(totalMonthlyCost * 100) / 100,
    userMonthlyCost: Math.round(userMonthlyCost * 100) / 100,
    partnerMonthlyCost: Math.round(partnerMonthlyCost * 100) / 100,
    mandatoryMonthlyCost: Math.round(mandatoryMonthlyCost * 100) / 100,
    activeServicesCount: activeServices.length,
    totalServicesCount: services.length,
  };
};

/**
 * Format currency amount with symbol
 */
export const formatCurrency = (amount: number, currencySymbol: string = '$'): string => {
  return `${currencySymbol}${amount.toFixed(2)}`;
};

/**
 * Get human-readable frequency label
 */
export const getFrequencyLabel = (frequency: ServiceFrequency): string => {
  switch (frequency) {
    case 'monthly':
      return 'Monthly';
    case 'quarterly':
      return 'Quarterly';
    case 'yearly':
      return 'Yearly';
    default:
      return frequency;
  }
};

/**
 * Calculate annual cost from monthly equivalent
 */
export const getAnnualEquivalent = (monthlyCost: number): number => {
  return Math.round(monthlyCost * 12 * 100) / 100;
};

/**
 * Calculate quarterly cost from monthly equivalent
 */
export const getQuarterlyEquivalent = (monthlyCost: number): number => {
  return Math.round(monthlyCost * 3 * 100) / 100;
};

/**
 * Get cost breakdown by payment responsibility
 */
export const getCostBreakdown = (services: IPropertyService[]) => {
  const activeServices = services.filter((service) => service.status === 'active');

  const userServices = activeServices.filter((service) => service.paidByUser);
  const partnerServices = activeServices.filter((service) => service.paidByPartner);
  const mandatoryServices = activeServices.filter((service) => service.mandatory);

  return {
    userServices: {
      services: userServices,
      totalMonthlyCost: userServices.reduce((sum, service) => sum + getMonthlyEquivalent(service.cost, service.frequency), 0),
      count: userServices.length,
    },
    partnerServices: {
      services: partnerServices,
      totalMonthlyCost: partnerServices.reduce((sum, service) => sum + getMonthlyEquivalent(service.cost, service.frequency), 0),
      count: partnerServices.length,
    },
    mandatoryServices: {
      services: mandatoryServices,
      totalMonthlyCost: mandatoryServices.reduce((sum, service) => sum + getMonthlyEquivalent(service.cost, service.frequency), 0),
      count: mandatoryServices.length,
    },
  };
};

/**
 * Calculate cost projections for different time periods
 */
export const getCostProjections = (services: IPropertyService[]) => {
  const summary = calculateServiceCosts(services);

  return {
    monthly: summary.totalMonthlyCost,
    quarterly: getQuarterlyEquivalent(summary.totalMonthlyCost),
    annual: getAnnualEquivalent(summary.totalMonthlyCost),
    userMonthly: summary.userMonthlyCost,
    userAnnual: getAnnualEquivalent(summary.userMonthlyCost),
    partnerMonthly: summary.partnerMonthlyCost,
    partnerAnnual: getAnnualEquivalent(summary.partnerMonthlyCost),
  };
};

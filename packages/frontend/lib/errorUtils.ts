import { AxiosError } from 'axios';
import { ZodError } from 'zod';

/**
 * Helper function to extract error message from axios error or mock error objects
 * Handles both real AxiosError instances and mock error objects used in tests
 */
export const getErrorMessage = (error: unknown, defaultMessage: string): string => {
  if (error instanceof AxiosError) {
    return error.response?.data?.error || error.message || defaultMessage;
  }
  // Handle mock error objects in tests
  if (error && typeof error === 'object' && 'response' in error) {
    const mockError = error as { response?: { data?: { error?: string } } };
    return mockError.response?.data?.error || defaultMessage;
  }
  return defaultMessage;
};

export const getErrorFromZod = (error: unknown): string => {
  if (typeof error === 'string') {
    return error;
  }
  if (error instanceof ZodError || 'issues' in (error as object)) {
    return (error as ZodError).issues.map((er) => er.message).join(' , ');
  }

  if (error instanceof Error) return error.message || 'Unexpected Error';

  return 'Unexpected Error';
};

import React, { useState } from 'react';
import { usePartners } from '../../hooks/usePartners';
import { <PERSON><PERSON>, Mo<PERSON>Footer, Input, Button, ErrorMessage } from '../ui';
import { IPartner } from '@alom-rentals/shared';

interface PartnerAddModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (partner: IPartner) => void;
  title?: string;
  submitButtonText?: string;
}

export function PartnerAddModal({ isOpen, onClose, onSuccess, title = 'Add New Partner', submitButtonText = 'Add Partner' }: PartnerAddModalProps) {
  const { createPartner, isCreating } = usePartners();
  const [formData, setFormData] = useState({ email: '', name: '' });
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!formData.email || !formData.name) {
      setError('Email and name are required');
      return;
    }

    const result = await createPartner(formData);
    if (result.error) {
      setError(result.error.message || 'Failed to create partner');
    } else {
      // Reset form
      setFormData({ email: '', name: '' });
      setError('');

      // Call success callback if provided
      if (onSuccess && result.data) {
        onSuccess(result.data);
      }

      // Close modal
      onClose();
    }
  };

  const handleClose = () => {
    setFormData({ email: '', name: '' });
    setError('');
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} title={title} size="md">
      {error && <ErrorMessage message={error} className="mb-4" />}

      <form onSubmit={handleSubmit} className="space-y-4">
        <Input
          type="email"
          label="Email"
          required
          value={formData.email}
          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
          placeholder="<EMAIL>"
        />

        <Input
          type="text"
          label="Full Name"
          required
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          placeholder="Partner's full name"
        />

        <ModalFooter>
          <Button type="button" variant="secondary" onClick={handleClose}>
            Cancel
          </Button>
          <Button type="submit" loading={isCreating}>
            {submitButtonText}
          </Button>
        </ModalFooter>
      </form>
    </Modal>
  );
}

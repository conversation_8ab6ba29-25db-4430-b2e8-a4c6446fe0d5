import React, { useState } from 'react';
import { usePartners } from '../../hooks/usePartners';
import { IPartner } from '@alom-rentals/shared';
import { Plus, Edit2, Trash2, Mail, User } from 'lucide-react';
import { PartnerAddModal } from './PartnerAddModal';

export function PartnersPage() {
  const { partners, isLoading, updatePartner, deletePartner, isUpdating, isDeleting, bulkDeletePartners, isBulkDeleting } = usePartners();
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingPartner, setEditingPartner] = useState<IPartner | null>(null);
  const [formData, setFormData] = useState({ email: '', name: '' });
  const [error, setError] = useState('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // Selection state
  const [selectedPartners, setSelectedPartners] = useState<IPartner[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  const handleEditSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!formData.email || !formData.name) {
      setError('Email and name are required');
      return;
    }

    if (editingPartner) {
      const result = await updatePartner(editingPartner._id, formData);
      if (result.error) {
        setError(result.error.message || 'An error occurred');
      } else {
        setFormData({ email: '', name: '' });
        setShowEditForm(false);
        setEditingPartner(null);
      }
    }
  };

  const handleEdit = (partner: IPartner) => {
    setEditingPartner(partner);
    setFormData({ email: partner.email, name: partner.name });
    setShowEditForm(true);
  };

  const handleDelete = async (partner: IPartner) => {
    if (window.confirm(`Are you sure you want to delete ${partner.name}?`)) {
      const result = await deletePartner(partner._id);
      if (result.error) {
        setError(result.error.message || 'Failed to delete partner');
      }
    }
  };

  const handleEditCancel = () => {
    setFormData({ email: '', name: '' });
    setShowEditForm(false);
    setEditingPartner(null);
    setError('');
  };

  // Paginated partners
  const paginatedPartners = partners.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  const totalPages = Math.ceil(partners.length / pageSize);

  // Bulk delete handlers
  const handleBulkDelete = () => {
    if (selectedPartners.length === 0) return;
    setShowBulkDeleteConfirm(true);
  };

  const confirmBulkDelete = async () => {
    if (selectedPartners.length === 0) return;

    const partnerIds = selectedPartners.map((p) => p._id);
    const result = await bulkDeletePartners(partnerIds);

    if (result.success) {
      setSelectedPartners([]);
      setShowBulkDeleteConfirm(false);
      // Reset to first page if current page becomes empty
      if (paginatedPartners.length === selectedPartners.length && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      }
    } else if (result.error) {
      setError(result.error.message);
    }
  };

  const cancelBulkDelete = () => {
    setShowBulkDeleteConfirm(false);
  };

  // Selection handlers
  const handlePartnerSelect = (partner: IPartner, selected: boolean) => {
    if (selected) {
      setSelectedPartners([...selectedPartners, partner]);
    } else {
      setSelectedPartners(selectedPartners.filter((p) => p._id !== partner._id));
    }
  };

  const handleSelectAll = () => {
    setSelectedPartners([...partners]);
  };

  const handleSelectNone = () => {
    setSelectedPartners([]);
  };

  const isPartnerSelected = (partner: IPartner) => {
    return selectedPartners.some((p) => p._id === partner._id);
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Partners</h1>
            <p className="text-gray-600">Manage your property partners and co-owners</p>
          </div>
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Add Partner</span>
          </button>
        </div>

        {error && <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">{error}</div>}

        {showEditForm && (
          <div className="mb-8 bg-white rounded-xl border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Edit Partner</h3>

            <form onSubmit={handleEditSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                  <input
                    type="email"
                    required
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                  <input
                    type="text"
                    required
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Partner's full name"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={handleEditCancel}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isUpdating}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isUpdating ? 'Updating...' : 'Update Partner'}
                </button>
              </div>
            </form>
          </div>
        )}

        {/* Bulk Actions */}
        {selectedPartners.length > 0 && (
          <div className="bg-white rounded-xl border border-gray-200 p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">{selectedPartners.length} selected</span>
                <button onClick={handleSelectAll} className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                  Select All
                </button>
                <button onClick={handleSelectNone} className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                  Select None
                </button>
              </div>
              <button
                onClick={handleBulkDelete}
                disabled={isBulkDeleting}
                className="flex items-center space-x-2 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                <Trash2 className="w-4 h-4" />
                <span>Delete {selectedPartners.length}</span>
              </button>
            </div>
          </div>
        )}

        <div className="bg-white rounded-xl border border-gray-200">
          {partners.length === 0 ? (
            <div className="p-8 text-center">
              <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No partners yet</h3>
              <p className="text-gray-600 mb-4">Add partners to manage property ownership and calculations</p>
              <button
                onClick={() => setShowAddModal(true)}
                className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors mx-auto"
              >
                <Plus className="w-4 h-4" />
                <span>Add Your First Partner</span>
              </button>
            </div>
          ) : (
            <div className="overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        checked={selectedPartners.length === partners.length && partners.length > 0}
                        onChange={(e) => (e.target.checked ? handleSelectAll() : handleSelectNone())}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Partner</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedPartners.map((partner: IPartner) => (
                    <tr key={partner._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={isPartnerSelected(partner)}
                          onChange={(e) => handlePartnerSelect(partner, e.target.checked)}
                          className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <User className="w-4 h-4 text-blue-600" />
                          </div>
                          <div className="text-sm font-medium text-gray-900">{partner.name}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-600">
                          <Mail className="w-4 h-4 mr-2" />
                          {partner.email}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{new Date(partner.createdAt).toLocaleDateString()}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button onClick={() => handleEdit(partner)} className="text-blue-600 hover:text-blue-700 p-1 rounded" title="Edit partner">
                            <Edit2 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDelete(partner)}
                            disabled={isDeleting}
                            className="text-red-600 hover:text-red-700 p-1 rounded disabled:opacity-50"
                            title="Delete partner"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200">
                  <div className="flex justify-center">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        disabled={currentPage === 1}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Previous
                      </button>

                      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                        <button
                          key={page}
                          onClick={() => setCurrentPage(page)}
                          className={`px-3 py-2 text-sm font-medium rounded-md ${
                            page === currentPage
                              ? 'text-blue-600 bg-blue-50 border border-blue-300'
                              : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                          }`}
                        >
                          {page}
                        </button>
                      ))}

                      <button
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        disabled={currentPage === totalPages}
                        className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Next
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Partner Add Modal */}
      <PartnerAddModal isOpen={showAddModal} onClose={() => setShowAddModal(false)} title="Add New Partner" submitButtonText="Add Partner" />

      {/* Bulk Delete Confirmation Modal */}
      {showBulkDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Partners</h3>
            <p className="text-gray-600 mb-4">Are you sure you want to delete {selectedPartners.length} partners? This action cannot be undone.</p>
            <div className="flex justify-end space-x-3">
              <button onClick={cancelBulkDelete} className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                Cancel
              </button>
              <button
                onClick={confirmBulkDelete}
                disabled={isBulkDeleting}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                Delete {selectedPartners.length} Partners
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

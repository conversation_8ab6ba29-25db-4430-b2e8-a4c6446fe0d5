import React, { useState } from 'react';
import { Edit2, Trash2, DollarSign, Clock, Shield, User, Users } from 'lucide-react';
import { IPropertyService } from '@alom-rentals/shared';
import { getMonthlyEquivalent, formatCurrency, getFrequencyLabel } from '../../lib/serviceCalculations';

interface ServiceItemProps {
  service: IPropertyService;
  onEdit: (service: IPropertyService) => void;
  onDelete: (serviceId: string) => void;
  currencySymbol?: string;
}

export const ServiceItem: React.FC<ServiceItemProps> = ({ service, onEdit, onDelete, currencySymbol = '$' }) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleDelete = () => {
    onDelete(service._id);
    setShowDeleteConfirm(false);
  };

  const monthlyEquivalent = getMonthlyEquivalent(service.cost, service.frequency);

  return (
    <>
      <div className={`bg-white rounded-lg border p-4 hover:shadow-md transition-shadow ${service.status === 'inactive' ? 'opacity-60' : ''}`}>
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="font-semibold text-gray-900">{service.name}</h3>
              {service.mandatory && <Shield className="w-4 h-4 text-orange-500" aria-label="Mandatory service" />}
              <span
                className={`px-2 py-1 rounded-full text-xs font-medium ${
                  service.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                }`}
              >
                {service.status}
              </span>
            </div>

            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <div className="flex items-center">
                <DollarSign className="w-4 h-4 mr-1" />
                <span>{formatCurrency(service.cost, currencySymbol)}</span>
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-1" />
                <span>{getFrequencyLabel(service.frequency)}</span>
              </div>
            </div>

            {service.frequency !== 'monthly' && (
              <div className="text-sm text-gray-500 mt-1">Monthly equivalent: {formatCurrency(monthlyEquivalent, currencySymbol)}</div>
            )}
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => onEdit(service)}
              className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
              title="Edit service"
            >
              <Edit2 className="w-4 h-4" />
            </button>
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              title="Delete service"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Payment Responsibility */}
        <div className="flex items-center space-x-4 text-sm">
          <span className="text-gray-500">Paid by:</span>
          {service.paidByUser && (
            <div className="flex items-center text-blue-600">
              <User className="w-4 h-4 mr-1" />
              <span>User</span>
            </div>
          )}
          {service.paidByPartner && (
            <div className="flex items-center text-green-600">
              <Users className="w-4 h-4 mr-1" />
              <span>Partner</span>
            </div>
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Service</h3>
            <p className="text-gray-600 mb-6">Are you sure you want to delete "{service.name}"? This action cannot be undone.</p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button onClick={handleDelete} className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

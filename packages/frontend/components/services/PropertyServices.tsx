/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
import { AlertCircle, CheckCircle } from 'lucide-react';
import { IPropertyService, IPropertyServiceCreateData, IServiceCostSummary } from '@alom-rentals/shared';
import { usePropertyServices } from '../../hooks/usePropertyServices';
import { ServiceList } from './ServiceList';
import { ServiceForm } from './ServiceForm';

interface PropertyServicesProps {
  propertyId: string;
  currencySymbol?: string;
}

export const PropertyServices: React.FC<PropertyServicesProps> = ({ propertyId, currencySymbol = '$' }) => {
  const { services, summary, loading, error, createService, updateService, deleteService } = usePropertyServices(propertyId);

  const [showForm, setShowForm] = useState(false);
  const [editingService, setEditingService] = useState<IPropertyService | undefined>();
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleAddService = () => {
    setEditingService(undefined);
    setShowForm(true);
  };

  const handleEditService = (service: IPropertyService) => {
    setEditingService(service);
    setShowForm(true);
  };

  const handleSaveService = async (data: IPropertyServiceCreateData) => {
    try {
      if (editingService) {
        await updateService(editingService._id, data);
        showNotification('success', 'Service updated successfully');
      } else {
        await createService(data);
        showNotification('success', 'Service created successfully');
      }
      setShowForm(false);
      setEditingService(undefined);
    } catch (error: unknown) {
      // TODO: Improve error type checking
      showNotification('error', (error as any).message);
    }
  };

  const handleDeleteService = async (serviceId: string) => {
    try {
      await deleteService(serviceId);
      showNotification('success', 'Service deleted successfully');
    } catch (error: unknown) {
      // TODO: Improve error type checking
      showNotification('error', (error as any).message);
    }
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setEditingService(undefined);
  };

  return (
    <div className="space-y-6">
      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
            <p className="text-red-800">{error.message || 'An error occurred'}</p>
          </div>
        </div>
      )}

      {/* Success Notification */}
      {notification && (
        <div className={`border rounded-lg p-4 ${notification.type === 'success' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
          <div className="flex items-center">
            {notification.type === 'success' ? <CheckCircle className="w-5 h-5 text-green-600 mr-2" /> : <AlertCircle className="w-5 h-5 text-red-600 mr-2" />}
            <p className={notification.type === 'success' ? 'text-green-800' : 'text-red-800'}>{notification.message}</p>
          </div>
        </div>
      )}

      {/* Services List */}
      <ServiceList
        services={services}
        summary={summary as IServiceCostSummary | null}
        onAddService={handleAddService}
        onEditService={handleEditService}
        onDeleteService={handleDeleteService}
        loading={loading}
        currencySymbol={currencySymbol}
      />

      {/* Service Form Modal */}
      {showForm && <ServiceForm service={editingService} onSave={handleSaveService} onCancel={handleCancelForm} />}
    </div>
  );
};

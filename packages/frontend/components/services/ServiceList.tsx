import React, { useState, useMemo } from 'react';
import { Plus, Search, Filter, DollarSign, TrendingUp } from 'lucide-react';
import { IPropertyService, IServiceCostSummary } from '@alom-rentals/shared';
import { ServiceItem } from './ServiceItem';
import { formatCurrency } from '../../lib/serviceCalculations';

interface ServiceListProps {
  services: IPropertyService[];
  summary: IServiceCostSummary | null;
  onAddService: () => void;
  onEditService: (service: IPropertyService) => void;
  onDeleteService: (serviceId: string) => void;
  loading?: boolean;
  currencySymbol?: string;
}

export const ServiceList: React.FC<ServiceListProps> = ({
  services,
  summary,
  onAddService,
  onEditService,
  onDeleteService,
  loading = false,
  currencySymbol = '$',
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

  const filteredServices = useMemo(() => {
    return services.filter((service) => {
      const matchesSearch = service.name.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || service.status === statusFilter;
      return matchesSearch && matchesStatus;
    });
  }, [services, searchTerm, statusFilter]);

  const groupedServices = useMemo(() => {
    const active = filteredServices.filter((s) => s.status === 'active');
    const inactive = filteredServices.filter((s) => s.status === 'inactive');
    return { active, inactive };
  }, [filteredServices]);

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900">Services</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="bg-gray-200 rounded-lg h-32 animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Services</h2>
        <button onClick={onAddService} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
          <Plus className="w-4 h-4" />
          <span>Add Service</span>
        </button>
      </div>

      {/* Cost Summary */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 font-medium">Total Monthly Cost</p>
                <p className="text-2xl font-bold text-blue-900">{formatCurrency(summary.totalMonthlyCost, currencySymbol)}</p>
              </div>
              <DollarSign className="w-8 h-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-600 font-medium">User Pays</p>
                <p className="text-2xl font-bold text-green-900">{formatCurrency(summary.userMonthlyCost, currencySymbol)}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
          </div>

          <div className="bg-purple-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-purple-600 font-medium">Partner Pays</p>
                <p className="text-2xl font-bold text-purple-900">{formatCurrency(summary.partnerMonthlyCost, currencySymbol)}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-purple-600" />
            </div>
          </div>

          <div className="bg-orange-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-orange-600 font-medium">Mandatory</p>
                <p className="text-2xl font-bold text-orange-900">{formatCurrency(summary.mandatoryMonthlyCost, currencySymbol)}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-orange-600" />
            </div>
          </div>
        </div>
      )}

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search services..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="w-4 h-4 text-gray-400" />
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as 'all' | 'active' | 'inactive')}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Services</option>
            <option value="active">Active Only</option>
            <option value="inactive">Inactive Only</option>
          </select>
        </div>
      </div>

      {/* Services List */}
      {filteredServices.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <DollarSign className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">{services.length === 0 ? 'No services yet' : 'No services found'}</h3>
          <p className="text-gray-600 mb-4">
            {services.length === 0 ? 'Start by adding your first service to track property costs' : 'Try adjusting your search or filter criteria'}
          </p>
          {services.length === 0 && (
            <button onClick={onAddService} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Add Service
            </button>
          )}
        </div>
      ) : (
        <div className="space-y-6">
          {/* Active Services */}
          {groupedServices.active.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Active Services ({groupedServices.active.length})</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {groupedServices.active.map((service) => (
                  <ServiceItem key={service._id} service={service} onEdit={onEditService} onDelete={onDeleteService} currencySymbol={currencySymbol} />
                ))}
              </div>
            </div>
          )}

          {/* Inactive Services */}
          {groupedServices.inactive.length > 0 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">Inactive Services ({groupedServices.inactive.length})</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {groupedServices.inactive.map((service) => (
                  <ServiceItem key={service._id} service={service} onEdit={onEditService} onDelete={onDeleteService} currencySymbol={currencySymbol} />
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

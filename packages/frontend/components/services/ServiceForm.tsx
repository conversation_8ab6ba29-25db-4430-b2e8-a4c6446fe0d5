import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle } from 'lucide-react';
import { IPropertyService, IPropertyServiceCreateData, ServiceFrequency, ServiceStatus } from '@alom-rentals/shared';

interface ServiceFormProps {
  service?: IPropertyService;
  onSave: (data: IPropertyServiceCreateData) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

export const ServiceForm: React.FC<ServiceFormProps> = ({
  service,
  onSave,
  onCancel,
  // loading = false, // Unused parameter
}) => {
  const [formData, setFormData] = useState({
    name: '',
    cost: '',
    frequency: 'monthly' as ServiceFrequency,
    status: 'active' as ServiceStatus,
    mandatory: false,
    paidByUser: true,
    paidByPartner: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (service) {
      setFormData({
        name: service.name,
        cost: service.cost.toString(),
        frequency: service.frequency,
        status: service.status,
        mandatory: service.mandatory,
        paidByUser: service.paidByUser,
        paidByPartner: service.paidByPartner,
      });
    }
  }, [service]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Service name is required';
    } else if (formData.name.length > 100) {
      newErrors.name = 'Service name must be less than 100 characters';
    }

    if (!formData.cost.trim()) {
      newErrors.cost = 'Cost is required';
    } else {
      const cost = parseFloat(formData.cost);
      if (isNaN(cost) || cost < 0) {
        newErrors.cost = 'Cost must be a positive number';
      }
    }

    if (!formData.paidByUser && !formData.paidByPartner) {
      newErrors.payment = 'At least one payment responsibility must be selected';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave({
        name: formData.name.trim(),
        cost: parseFloat(formData.cost),
        frequency: formData.frequency,
        status: formData.status,
        mandatory: formData.mandatory,
        paidByUser: formData.paidByUser,
        paidByPartner: formData.paidByPartner,
      });
    } catch {
      // Error handling is done in the parent component
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string | number | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900">{service ? 'Edit Service' : 'Add New Service'}</h2>
          <button onClick={onCancel} className="text-gray-400 hover:text-gray-600" disabled={isSubmitting}>
            <X className="w-6 h-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Service Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Service Name *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="e.g., Internet, Electricity, Water"
              disabled={isSubmitting}
            />
            {errors.name && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.name}
              </p>
            )}
          </div>

          {/* Cost */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Cost *</label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.cost}
              onChange={(e) => handleInputChange('cost', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.cost ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="0.00"
              disabled={isSubmitting}
            />
            {errors.cost && (
              <p className="text-red-500 text-sm mt-1 flex items-center">
                <AlertCircle className="w-4 h-4 mr-1" />
                {errors.cost}
              </p>
            )}
          </div>

          {/* Frequency */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Frequency *</label>
            <select
              value={formData.frequency}
              onChange={(e) => handleInputChange('frequency', e.target.value as ServiceFrequency)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isSubmitting}
            >
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="yearly">Yearly</option>
            </select>
          </div>

          {/* Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value as ServiceStatus)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={isSubmitting}
            >
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          {/* Checkboxes */}
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="mandatory"
                checked={formData.mandatory}
                onChange={(e) => handleInputChange('mandatory', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                disabled={isSubmitting}
              />
              <label htmlFor="mandatory" className="ml-2 text-sm text-gray-700">
                Mandatory service
              </label>
            </div>

            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">Payment Responsibility *</p>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="paidByUser"
                  checked={formData.paidByUser}
                  onChange={(e) => handleInputChange('paidByUser', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={isSubmitting}
                />
                <label htmlFor="paidByUser" className="ml-2 text-sm text-gray-700">
                  Paid by user
                </label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="paidByPartner"
                  checked={formData.paidByPartner}
                  onChange={(e) => handleInputChange('paidByPartner', e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={isSubmitting}
                />
                <label htmlFor="paidByPartner" className="ml-2 text-sm text-gray-700">
                  Paid by partner
                </label>
              </div>
              {errors.payment && (
                <p className="text-red-500 text-sm flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.payment}
                </p>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onCancel}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <>
                  <Save className="w-4 h-4 mr-2" />
                  {service ? 'Update' : 'Create'} Service
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

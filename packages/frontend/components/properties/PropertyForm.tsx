import React, { useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useProperties } from '../../hooks/useProperties';
import { useCurrencies } from '../../hooks/useCurrencies';
import { ArrowLeft } from 'lucide-react';
import { PropertyOwners } from './PropertyOwners';
import { IOwner, ICurrency } from '@alom-rentals/shared';

interface PropertyFormProps {
  onCancel: () => void;
  onSuccess: () => void;
}

export function PropertyForm({ onCancel, onSuccess }: PropertyFormProps) {
  const { user } = useAuth();
  const { createProperty, isCreating } = useProperties();
  const { currencies } = useCurrencies();
  const [formData, setFormData] = useState({
    name: '',
    address: '',
    description: '',
    propertyType: 'residential',
    isRented: false,
    currencyId: '',
  });
  const [owners, setOwners] = useState<IOwner[]>([
    {
      ownerType: 'User',
      email: user?.email || '',
      name: user?.fullName || '',
      ownershipPercentage: 100,
    },
  ]);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate ownership percentages sum to 100
    const totalPercentage = owners.reduce((sum, owner) => sum + owner.ownershipPercentage, 0);
    if (Math.abs(totalPercentage - 100) > 0.01) {
      setError('Ownership percentages must sum to 100%');
      return;
    }

    // Filter out empty currencyId to avoid validation errors
    const propertyData = {
      ...formData,
      propertyType: formData.propertyType as 'residential' | 'commercial' | 'land' | 'other',
      owners,
    };

    // Remove currencyId if it's empty
    if (!formData.currencyId) {
      delete (propertyData as Record<string, unknown>).currencyId;
    }

    const result = await createProperty(propertyData);

    if (result.error) {
      setError(result.error.message);
    } else {
      onSuccess();
    }
  };

  const handleOwnersChange = (newOwners: IOwner[]) => {
    setOwners(newOwners);
  };

  const totalPercentage = owners.reduce((sum, owner) => sum + owner.ownershipPercentage, 0);

  return (
    <div className="p-6">
      <div className="max-w-2xl mx-auto">
        <div className="flex items-center space-x-4 mb-8">
          <button onClick={onCancel} className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Add New Property</h1>
            <p className="text-gray-600">Create a new property and set ownership details</p>
          </div>
        </div>

        {error && <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">{error}</div>}

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Property Details</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Property Name *</label>
                <input
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Downtown Apartment"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Property Type</label>
                <select
                  value={formData.propertyType}
                  onChange={(e) => setFormData({ ...formData, propertyType: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="residential">Residential</option>
                  <option value="commercial">Commercial</option>
                  <option value="industrial">Industrial</option>
                  <option value="land">Land</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Currency</label>
                <select
                  value={formData.currencyId}
                  onChange={(e) => setFormData({ ...formData, currencyId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">Select a currency (optional)</option>
                  {currencies.map((currency: ICurrency) => (
                    <option key={currency._id} value={currency._id}>
                      {currency.symbol} - {currency.name}
                      {currency.code && ` (${currency.code})`}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
              <input
                type="text"
                value={formData.address}
                onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Full property address"
              />
            </div>

            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                rows={3}
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Additional details about the property"
              />
            </div>

            <div className="mt-4">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={formData.isRented}
                  onChange={(e) => setFormData({ ...formData, isRented: e.target.checked })}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm font-medium text-gray-700">Currently rented</span>
              </label>
            </div>
          </div>

          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <PropertyOwners owners={owners} onChange={handleOwnersChange} currentUserEmail={user?.email || ''} currentUserName={user?.fullName || ''} />
          </div>

          <div className="flex justify-end space-x-4">
            <button type="button" onClick={onCancel} className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              Cancel
            </button>
            <button
              type="submit"
              disabled={isCreating || Math.abs(totalPercentage - 100) > 0.01}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isCreating ? 'Creating...' : 'Create Property'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

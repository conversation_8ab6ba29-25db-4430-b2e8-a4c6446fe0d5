import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useProperties } from '../../hooks/useProperties';
import { useCurrencies } from '../../hooks/useCurrencies';
import { ArrowLeft, Save, X } from 'lucide-react';
import { PropertyOwners } from './PropertyOwners';
import { IOwner, IProperty, IUserPublic, IPartner, IPropertyUpdateData, ICurrency } from '@alom-rentals/shared';

interface PropertyEditFormProps {
  property: IProperty;
  onCancel: () => void;
  onSuccess: () => void;
}

export function PropertyEditForm({ property, onCancel, onSuccess }: PropertyEditFormProps) {
  const { user } = useAuth();
  const { updateProperty, isUpdating } = useProperties();
  const { currencies } = useCurrencies();

  // Helper function to get owner name
  const getOwnerName = (owner: IUserPublic | IPartner): string => {
    if ('fullName' in owner) {
      return owner.fullName; // IUserPublic
    } else {
      return owner.name; // IPartner
    }
  };

  const [formData, setFormData] = useState({
    name: property.name,
    address: property.address,
    description: property.description || '',
    propertyType: property.propertyType,
    isRented: property.isRented,
    currencyId: property.currencyId || '',
  });

  // Convert property owners to the format expected by PropertyOwners component
  const [owners, setOwners] = useState<IOwner[]>(() => {
    return property.owners.map((owner) => ({
      ownerType: owner.ownerType,
      email: owner.owner.email,
      name: getOwnerName(owner.owner),
      ownershipPercentage: owner.ownershipPercentage,
    }));
  });

  const [error, setError] = useState('');
  const [hasChanges, setHasChanges] = useState(false);

  // Track changes
  useEffect(() => {
    const originalOwners = property.owners.map((owner) => ({
      ownerType: owner.ownerType,
      email: owner.owner.email,
      name: getOwnerName(owner.owner),
      ownershipPercentage: owner.ownershipPercentage,
    }));

    const formChanged =
      formData.name !== property.name ||
      formData.address !== property.address ||
      formData.description !== (property.description || '') ||
      formData.propertyType !== property.propertyType ||
      formData.isRented !== property.isRented ||
      formData.currencyId !== (property.currencyId || '');

    const ownersChanged = JSON.stringify(owners) !== JSON.stringify(originalOwners);

    setHasChanges(formChanged || ownersChanged);
  }, [formData, owners, property]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate ownership percentages sum to 100
    const totalPercentage = owners.reduce((sum, owner) => sum + owner.ownershipPercentage, 0);
    if (Math.abs(totalPercentage - 100) > 0.01) {
      setError('Ownership percentages must sum to 100%');
      return;
    }

    const updateData: IPropertyUpdateData = {};

    // Only include changed fields
    if (formData.name !== property.name) updateData.name = formData.name;
    if (formData.address !== property.address) updateData.address = formData.address;
    if (formData.description !== (property.description || '')) updateData.description = formData.description;
    if (formData.propertyType !== property.propertyType) updateData.propertyType = formData.propertyType;
    if (formData.isRented !== property.isRented) updateData.isRented = formData.isRented;
    if (formData.currencyId !== (property.currencyId || '')) updateData.currencyId = formData.currencyId;

    // Check if owners changed
    const originalOwners = property.owners.map((owner) => ({
      ownerType: owner.ownerType,
      email: owner.owner.email,
      name: getOwnerName(owner.owner),
      ownershipPercentage: owner.ownershipPercentage,
    }));

    if (JSON.stringify(owners) !== JSON.stringify(originalOwners)) {
      updateData.owners = owners;
    }

    // If no changes, just close the form
    if (Object.keys(updateData).length === 0) {
      onCancel();
      return;
    }

    const result = await updateProperty(property._id, updateData);

    if (result.error) {
      setError(result.error.message);
    } else {
      onSuccess();
    }
  };

  const handleOwnersChange = (newOwners: IOwner[]) => {
    setOwners(newOwners);
  };

  const totalPercentage = owners.reduce((sum, owner) => sum + owner.ownershipPercentage, 0);

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <button onClick={onCancel} className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900">Edit Property</h1>
            <p className="text-gray-600">Update property information</p>
          </div>
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2"
            >
              <X className="w-4 h-4" />
              <span>Cancel</span>
            </button>
            <button
              type="submit"
              form="edit-property-form"
              disabled={isUpdating || !hasChanges}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>{isUpdating ? 'Saving...' : 'Save Changes'}</span>
            </button>
          </div>
        </div>

        {/* Form */}
        <form id="edit-property-form" onSubmit={handleSubmit} className="space-y-6">
          {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">{error}</div>}

          {/* Basic Information */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Property Name *
                </label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="propertyType" className="block text-sm font-medium text-gray-700 mb-1">
                  Property Type
                </label>
                <select
                  id="propertyType"
                  value={formData.propertyType}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      propertyType: e.target.value as 'residential' | 'commercial' | 'land' | 'other',
                    })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="residential">Residential</option>
                  <option value="commercial">Commercial</option>
                  <option value="land">Land</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="md:col-span-2">
                <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                  Address *
                </label>
                <input
                  type="text"
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div className="md:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Optional description..."
                />
              </div>

              <div>
                <label htmlFor="currency" className="block text-sm font-medium text-gray-700 mb-1">
                  Currency
                </label>
                <select
                  id="currency"
                  value={formData.currencyId}
                  onChange={(e) => setFormData({ ...formData, currencyId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select currency...</option>
                  {currencies.map((currency: ICurrency) => (
                    <option key={currency._id} value={currency._id}>
                      {currency.name} ({currency.symbol})
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isRented"
                  checked={formData.isRented}
                  onChange={(e) => setFormData({ ...formData, isRented: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="isRented" className="ml-2 block text-sm text-gray-700">
                  Currently rented
                </label>
              </div>
            </div>
          </div>

          {/* Property Owners */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Property Owners</h2>
              <div className="text-sm text-gray-600">
                Total: {totalPercentage.toFixed(1)}%{Math.abs(totalPercentage - 100) > 0.01 && <span className="text-red-600 ml-2">(Must equal 100%)</span>}
              </div>
            </div>

            <PropertyOwners owners={owners} onChange={handleOwnersChange} currentUserEmail={user?.email || ''} currentUserName={user?.fullName || ''} />
          </div>
        </form>
      </div>
    </div>
  );
}

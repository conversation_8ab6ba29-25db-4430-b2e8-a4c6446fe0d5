/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from 'react';
import { Plus, X, Search, User, Users } from 'lucide-react';
import { usePartners } from '../../hooks/usePartners';
import { IPartner } from '@alom-rentals/shared';
import { authAPI } from '../../lib/api';
import { PartnerAddModal } from '../partners/PartnerAddModal';
import { IOwner, IUserPublic } from '@alom-rentals/shared';

export type { IOwner };

interface PropertyOwnersProps {
  owners: IOwner[];
  onChange: (owners: IOwner[]) => void;
  currentUserEmail: string;
  currentUserName: string;
}

export function PropertyOwners({ owners, onChange, currentUserEmail, currentUserName }: PropertyOwnersProps) {
  const { partners } = usePartners();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<IUserPublic[]>([]);
  const [, setIsSearching] = useState(false);
  const [showAddModal, setShowAddModal] = useState(false);

  // Search users function (you'll need to implement this API endpoint)
  const searchUsers = async (query: string) => {
    if (query.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const response = await authAPI.searchUsers(query);
      setSearchResults(response.data);
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchUsers(searchQuery);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const addOwner = () => {
    const newOwner: IOwner = {
      ownerType: 'Partner',
      email: '',
      name: '',
      ownershipPercentage: 0,
    };
    onChange([...owners, newOwner]);
  };

  const removeOwner = (index: number) => {
    if (index === 0) return; // Can't remove current user
    const updated = owners.filter((_, i) => i !== index);
    onChange(updated);
  };

  const updateOwner = (index: number, field: keyof IOwner, value: string | number) => {
    const updated = [...owners];
    updated[index] = { ...updated[index], [field]: value };
    onChange(updated);
  };

  const addUserAsOwner = (user: IUserPublic) => {
    const newOwner: IOwner = {
      ownerType: 'User',
      email: user.email,
      name: user.fullName,
      ownershipPercentage: 0,
    };
    onChange([...owners, newOwner]);
    setSearchQuery('');
    setSearchResults([]);
  };

  const addPartnerAsOwner = (partner: IPartner) => {
    const newOwner: IOwner = {
      ownerType: 'Partner',
      email: partner.email,
      name: partner.name,
      ownershipPercentage: 0,
    };
    onChange([...owners, newOwner]);
  };

  const handlePartnerCreated = (partner: any) => {
    // Add the new partner as an owner
    const newOwner: IOwner = {
      ownerType: 'Partner',
      email: partner.email,
      name: partner.name,
      ownershipPercentage: 0,
    };
    onChange([...owners, newOwner]);
  };

  const totalPercentage = owners.reduce((sum, owner) => sum + owner.ownershipPercentage, 0);
  const isValidPercentage = Math.abs(totalPercentage - 100) < 0.01;

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <label className="block text-sm font-medium text-gray-700">Property Owners</label>
        <div className="text-sm text-gray-600">
          Total: {totalPercentage.toFixed(1)}%{!isValidPercentage && <span className="text-red-600 ml-2">Must equal 100%</span>}
        </div>
      </div>

      {/* Current User (First Owner) */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <User className="w-4 h-4 text-blue-600" />
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-900">{currentUserName}</div>
            <div className="text-sm text-gray-600">{currentUserEmail}</div>
            <div className="text-xs text-blue-600">Current User (You)</div>
          </div>
          <div className="w-24">
            <input
              type="number"
              min="0"
              max="100"
              step="0.1"
              value={owners[0]?.ownershipPercentage || 100}
              onChange={(e) => updateOwner(0, 'ownershipPercentage', parseFloat(e.target.value) || 0)}
              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="%"
            />
          </div>
        </div>
      </div>

      {/* Additional Owners */}
      {owners.slice(1).map((owner, index) => (
        <div key={index + 1} className="border border-gray-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
              {owner.ownerType === 'User' ? <User className="w-4 h-4 text-gray-600" /> : <Users className="w-4 h-4 text-gray-600" />}
            </div>

            <div className="flex-1 space-y-3">
              {/* Owner Type Selection */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Owner Type</label>
                <select
                  value={owner.ownerType}
                  onChange={(e) => updateOwner(index + 1, 'ownerType', e.target.value)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="user">Registered User</option>
                  <option value="Partner">Partner</option>
                </select>
              </div>

              {/* Email and Name Fields */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Email</label>
                  <input
                    type="email"
                    required
                    value={owner.email}
                    onChange={(e) => updateOwner(index + 1, 'email', e.target.value)}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">Name</label>
                  <input
                    type="text"
                    required
                    value={owner.name}
                    onChange={(e) => updateOwner(index + 1, 'name', e.target.value)}
                    className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Owner's name"
                  />
                </div>
              </div>

              {/* Ownership Percentage */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Ownership Percentage</label>
                <input
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  required
                  value={owner.ownershipPercentage}
                  onChange={(e) => updateOwner(index + 1, 'ownershipPercentage', parseFloat(e.target.value) || 0)}
                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="0.0"
                />
              </div>
            </div>

            <button type="button" onClick={() => removeOwner(index + 1)} className="p-1 text-red-600 hover:text-red-700 rounded" title="Remove owner">
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      ))}

      {/* Add Owner Section */}
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
        <div className="space-y-4">
          {/* Search Users */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search Registered Users</label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Search by email or name..."
              />
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="mt-2 border border-gray-200 rounded-lg max-h-40 overflow-y-auto">
                {searchResults.map((user) => (
                  <button
                    key={user.id}
                    type="button"
                    onClick={() => addUserAsOwner(user)}
                    className="w-full px-3 py-2 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                  >
                    <div className="text-sm font-medium text-gray-900">{user.fullName}</div>
                    <div className="text-xs text-gray-600">{user.email}</div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Quick Add from Partners */}
          {partners.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Add Existing Partner</label>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {partners.map((partner: IPartner) => (
                  <button
                    key={partner._id}
                    type="button"
                    onClick={() => addPartnerAsOwner(partner)}
                    className="px-3 py-2 text-left border border-gray-200 rounded-lg hover:bg-gray-50 text-sm"
                  >
                    <div className="font-medium text-gray-900">{partner.name}</div>
                    <div className="text-xs text-gray-600">{partner.email}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={addOwner}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
            >
              <Plus className="w-4 h-4" />
              <span>Add Owner</span>
            </button>

            <button
              type="button"
              onClick={() => setShowAddModal(true)}
              className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
            >
              <Plus className="w-4 h-4" />
              <span>Create Partner</span>
            </button>
          </div>
        </div>
      </div>

      {/* Partner Add Modal */}
      <PartnerAddModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSuccess={handlePartnerCreated}
        title="Create New Partner"
        submitButtonText="Create & Add"
      />
    </div>
  );
}

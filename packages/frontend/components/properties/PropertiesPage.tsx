import { useState } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useProperties } from '../../hooks/useProperties';
import { Building2, Plus, Users, DollarSign, Edit } from 'lucide-react';
import { PropertyForm } from './PropertyForm';
import { PropertyDetailPage } from './PropertyDetailPage';
import { PropertyEditForm } from './PropertyEditForm';
import { IPropertyOwner, IProperty, IUserPublic, IPartner } from '@alom-rentals/shared';

export function PropertiesPage() {
  const { user } = useAuth();
  const { properties, isLoading } = useProperties();
  const [showForm, setShowForm] = useState(false);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string | null>(null);
  const [editingProperty, setEditingProperty] = useState<IProperty | null>(null);

  const handlePropertyCreated = () => {
    setShowForm(false);
  };

  const handleEditProperty = (property: IProperty) => {
    setEditingProperty(property);
  };

  const handleEditSuccess = () => {
    setEditingProperty(null);
  };

  const handleEditCancel = () => {
    setEditingProperty(null);
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl border border-gray-200 p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="space-y-2">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (showForm) {
    return <PropertyForm onCancel={() => setShowForm(false)} onSuccess={handlePropertyCreated} />;
  }

  if (editingProperty) {
    return <PropertyEditForm property={editingProperty} onCancel={handleEditCancel} onSuccess={handleEditSuccess} />;
  }

  if (selectedPropertyId) {
    return <PropertyDetailPage propertyId={selectedPropertyId} onBack={() => setSelectedPropertyId(null)} />;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Properties</h1>
          <p className="text-gray-600">Manage your property portfolio</p>
        </div>
        <button
          onClick={() => setShowForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Property</span>
        </button>
      </div>

      {properties.length === 0 ? (
        <div className="text-center py-12">
          <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No properties yet</h3>
          <p className="text-gray-600 mb-4">Start by adding your first property</p>
          <button onClick={() => setShowForm(true)} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
            Add Property
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {properties.map((property: IProperty) => {
            const userOwnership =
              property.owners.find((o: IPropertyOwner) => o.ownerType === 'User' && (o.owner as IUserPublic)?.id === user?.id)?.ownershipPercentage || 0;

            return (
              <div key={property._id} className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                      <Building2 className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{property.name}</h3>
                      <p className="text-sm text-gray-600 capitalize">{property.propertyType}</p>
                    </div>
                  </div>
                  <div
                    className={`px-2 py-1 rounded-full text-xs font-medium ${property.isRented ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}
                  >
                    {property.isRented ? 'Rented' : 'Vacant'}
                  </div>
                </div>

                {property.address && <p className="text-sm text-gray-600 mb-4">{property.address}</p>}

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <DollarSign className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Your ownership</span>
                    </div>
                    <span className="font-medium text-gray-900">{userOwnership}%</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-gray-400" />
                      <span className="text-sm text-gray-600">Total owners</span>
                    </div>
                    <span className="font-medium text-gray-900">{property.owners.length}</span>
                  </div>

                  {property.owners.length > 1 && (
                    <div className="pt-3 border-t border-gray-100">
                      <p className="text-xs text-gray-500 mb-2">Other owners:</p>
                      <div className="space-y-1">
                        {property.owners
                          .filter((owner: IPropertyOwner) =>
                            owner.ownerType === 'User' ? (owner.owner as IUserPublic).id !== user?.id : (owner.owner as IPartner)._id !== user?.id
                          )
                          .map((owner: IPropertyOwner, index: number) => (
                            <div key={index} className="flex justify-between text-xs">
                              <span className="text-gray-600">
                                {owner.ownerType === 'User'
                                  ? (owner.owner as IUserPublic).fullName || owner.owner.email
                                  : (owner.owner as IPartner).name || owner.owner.email}
                              </span>
                              <span className="text-gray-900">{owner.ownershipPercentage}%</span>
                            </div>
                          ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="mt-4 pt-4 border-t border-gray-100 flex space-x-2">
                  <button
                    onClick={() => setSelectedPropertyId(property._id)}
                    className="flex-1 text-center text-sm text-blue-600 hover:text-blue-700 font-medium py-2"
                  >
                    View Details
                  </button>
                  <button
                    onClick={() => handleEditProperty(property)}
                    className="flex items-center justify-center space-x-1 px-3 py-2 text-sm text-gray-600 hover:text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Edit className="w-4 h-4" />
                    <span>Edit</span>
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}

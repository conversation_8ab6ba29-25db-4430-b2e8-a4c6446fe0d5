import React, { useState } from 'react';
import { ArrowLeft, Building2, MapPin, Users, DollarSign, Edit, Calendar } from 'lucide-react';
import { useProperty } from '../../hooks/useProperties';
import { useAuth } from '../../hooks/useAuth';
import { PropertyServices } from '../services/PropertyServices';
import { PropertyEditForm } from './PropertyEditForm';
import { IPropertyOwner, IUserPublic, IPartner } from '@alom-rentals/shared';

interface PropertyDetailPageProps {
  propertyId: string;
  onBack: () => void;
}

export const PropertyDetailPage: React.FC<PropertyDetailPageProps> = ({ propertyId, onBack }) => {
  const { user } = useAuth();
  const { data: property, isLoading, error } = useProperty(propertyId);
  const [activeTab, setActiveTab] = useState<'overview' | 'services'>('overview');
  const [isEditing, setIsEditing] = useState(false);

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="max-w-6xl mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="h-32 bg-gray-200 rounded mb-6"></div>
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !property) {
    return (
      <div className="p-6">
        <div className="max-w-6xl mx-auto">
          <div className="text-center py-12">
            <Building2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Property not found</h3>
            <p className="text-gray-600 mb-4">The property you're looking for doesn't exist or you don't have access to it.</p>
            <button onClick={onBack} className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  const userOwnership =
    property.owners.find((o: IPropertyOwner) => o.ownerType === 'User' && (o.owner as IUserPublic)?.id === user?.id)?.ownershipPercentage || 0;

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleEditSuccess = () => {
    setIsEditing(false);
  };

  const handleEditCancel = () => {
    setIsEditing(false);
  };

  // Show edit form if editing
  if (isEditing) {
    return <PropertyEditForm property={property} onCancel={handleEditCancel} onSuccess={handleEditSuccess} />;
  }

  return (
    <div className="p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex items-center space-x-4 mb-8">
          <button onClick={onBack} className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div className="flex-1">
            <h1 className="text-2xl font-bold text-gray-900">{property.name}</h1>
            <p className="text-gray-600 capitalize">{property.propertyType} Property</p>
          </div>
          <button
            onClick={() => setIsEditing(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Edit className="w-4 h-4" />
            <span>Edit Property</span>
          </button>
        </div>

        {/* Property Overview Card */}
        <div className="bg-white rounded-xl border border-gray-200 p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <Building2 className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Status</p>
                <p className={`font-semibold ${property.isRented ? 'text-green-600' : 'text-gray-600'}`}>{property.isRented ? 'Rented' : 'Vacant'}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Your Ownership</p>
                <p className="font-semibold text-gray-900">{userOwnership}%</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <Users className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Total Owners</p>
                <p className="font-semibold text-gray-900">{property.owners.length}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                <Calendar className="w-6 h-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-600">Created</p>
                <p className="font-semibold text-gray-900">{formatDate(property.createdAt)}</p>
              </div>
            </div>
          </div>

          {property.address && (
            <div className="mt-6 pt-6 border-t border-gray-100">
              <div className="flex items-center space-x-2 text-gray-600">
                <MapPin className="w-4 h-4" />
                <span>{property.address}</span>
              </div>
            </div>
          )}

          {property.description && (
            <div className="mt-4">
              <p className="text-gray-700">{property.description}</p>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('overview')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'overview' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Overview
            </button>
            <button
              onClick={() => setActiveTab('services')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'services' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Services
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Ownership Details */}
            <div className="bg-white rounded-xl border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Ownership Details</h3>
              <div className="space-y-4">
                {property.owners.map((owner: IPropertyOwner, index: number) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${owner.ownerType === 'User' ? 'bg-blue-100' : 'bg-green-100'}`}>
                        {owner.ownerType === 'User' ? (
                          <Users className={`w-5 h-5 ${owner.ownerType === 'User' ? 'text-blue-600' : 'text-green-600'}`} />
                        ) : (
                          <Users className="w-5 h-5 text-green-600" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          {owner.ownerType === 'User'
                            ? (owner.owner as IUserPublic).fullName || owner.owner.email
                            : (owner.owner as IPartner).name || owner.owner.email}
                        </p>
                        <p className="text-sm text-gray-600">{owner.owner.email}</p>
                        <p className="text-xs text-gray-500 capitalize">{owner.ownerType}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">{owner.ownershipPercentage}%</p>
                      <p className="text-sm text-gray-600">Ownership</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'services' && <PropertyServices propertyId={propertyId} currencySymbol={property.currency?.symbol || '$'} />}
      </div>
    </div>
  );
};

/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
import { useCurrencies } from '../../hooks/useCurrencies';
import { ICurrency, ICurrencyCreateData, ICurrencyUpdateData } from '@alom-rentals/shared';

interface CurrencyFormData {
  name: string;
  symbol: string;
  code: string;
}

const CurrenciesPage: React.FC = () => {
  const { currencies, loading, error, createCurrency, updateCurrency, deleteCurrency, bulkDeleteCurrencies, isBulkDeleting } = useCurrencies();
  const [showForm, setShowForm] = useState(false);
  const [editingCurrency, setEditingCurrency] = useState<ICurrency | null>(null);
  const [formData, setFormData] = useState<CurrencyFormData>({
    name: '',
    symbol: '',
    code: '',
  });
  const [formError, setFormError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(10);

  // Selection state
  const [selectedCurrencies, setSelectedCurrencies] = useState<ICurrency[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  const resetForm = () => {
    setFormData({ name: '', symbol: '', code: '' });
    setFormError(null);
    setEditingCurrency(null);
    setShowForm(false);
  };

  const handleEdit = (currency: ICurrency) => {
    setEditingCurrency(currency);
    setFormData({
      name: currency.name,
      symbol: currency.symbol,
      code: currency.code || '',
    });
    setShowForm(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setFormError(null);

    try {
      if (editingCurrency) {
        const updateData: ICurrencyUpdateData = {
          name: formData.name,
          symbol: formData.symbol,
          code: formData.code || undefined,
        };
        await updateCurrency(editingCurrency._id, updateData);
      } else {
        const createData: ICurrencyCreateData = {
          name: formData.name,
          symbol: formData.symbol,
          code: formData.code || undefined,
        };
        await createCurrency(createData);
      }
      resetForm();
    } catch (error: unknown) {
      // TODO: Improve error type checking
      setFormError((error as any).message);
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (currencyId: string) => {
    if (window.confirm('Are you sure you want to delete this currency?')) {
      try {
        await deleteCurrency(currencyId);
      } catch (error: unknown) {
        // TODO: Improve error type checking
        alert((error as any).message);
      }
    }
  };

  // Paginated currencies
  const paginatedCurrencies = currencies.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  const totalPages = Math.ceil(currencies.length / pageSize);

  // Bulk delete handlers
  const handleBulkDelete = () => {
    if (selectedCurrencies.length === 0) return;
    setShowBulkDeleteConfirm(true);
  };

  const confirmBulkDelete = async () => {
    if (selectedCurrencies.length === 0) return;

    const currencyIds = selectedCurrencies.map((c) => c._id);
    const result = await bulkDeleteCurrencies(currencyIds);

    if (result.success) {
      setSelectedCurrencies([]);
      setShowBulkDeleteConfirm(false);
      // Reset to first page if current page becomes empty
      if (paginatedCurrencies.length === selectedCurrencies.length && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      }
    } else if (result.error) {
      alert(result.error.message);
    }
  };

  const cancelBulkDelete = () => {
    setShowBulkDeleteConfirm(false);
  };

  // Selection handlers
  const handleCurrencySelect = (currency: ICurrency, selected: boolean) => {
    if (selected) {
      setSelectedCurrencies([...selectedCurrencies, currency]);
    } else {
      setSelectedCurrencies(selectedCurrencies.filter((c) => c._id !== currency._id));
    }
  };

  const handleSelectAll = () => {
    setSelectedCurrencies([...currencies]);
  };

  const handleSelectNone = () => {
    setSelectedCurrencies([]);
  };

  const isCurrencySelected = (currency: ICurrency) => {
    return selectedCurrencies.some((c) => c._id === currency._id);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div role="status" className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Currency Management</h1>
        <button
          onClick={() => setShowForm(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Add Currency
        </button>
      </div>

      {error && <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">{error.message || 'An error occurred'}</div>}

      {/* Currency Form Modal */}
      {showForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">{editingCurrency ? 'Edit Currency' : 'Add New Currency'}</h3>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="name">
                    Currency Name *
                  </label>
                  <input
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., US Dollar"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="symbol">
                    Currency Symbol *
                  </label>
                  <input
                    id="symbol"
                    type="text"
                    value={formData.symbol}
                    onChange={(e) => setFormData({ ...formData, symbol: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., $"
                    maxLength={3}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="code">
                    Currency Code
                  </label>
                  <input
                    id="code"
                    type="text"
                    value={formData.code}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        code: e.target.value.toUpperCase(),
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., USD"
                    maxLength={3}
                  />
                </div>

                {formError && <div className="text-red-600 text-sm">{formError}</div>}

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
                    disabled={submitting}
                  >
                    Cancel
                  </button>
                  <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50" disabled={submitting}>
                    {submitting ? 'Saving...' : editingCurrency ? 'Update' : 'Create'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Actions */}
      {selectedCurrencies.length > 0 && (
        <div className="bg-white shadow rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">{selectedCurrencies.length} selected</span>
              <button onClick={handleSelectAll} className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                Select All
              </button>
              <button onClick={handleSelectNone} className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                Select None
              </button>
            </div>
            <button
              onClick={handleBulkDelete}
              disabled={isBulkDeleting}
              className="flex items-center space-x-2 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              <span>Delete {selectedCurrencies.length}</span>
            </button>
          </div>
        </div>
      )}

      {/* Currencies List */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Your Currencies</h2>
        </div>

        {currencies.length === 0 ? (
          <div className="px-6 py-8 text-center text-gray-500">No currencies found. Create your first currency to get started.</div>
        ) : (
          <div className="divide-y divide-gray-200">
            {paginatedCurrencies.map((currency: ICurrency) => (
              <div key={currency._id} className="px-6 py-4 flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <input
                    type="checkbox"
                    checked={isCurrencySelected(currency)}
                    onChange={(e) => handleCurrencySelect(currency, e.target.checked)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                  />
                  <div className="flex-shrink-0">
                    <span className="text-2xl font-bold text-gray-900">{currency.symbol}</span>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">{currency.name}</h3>
                    {currency.code && <p className="text-sm text-gray-500">{currency.code}</p>}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button onClick={() => handleEdit(currency)} className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    Edit
                  </button>
                  <button onClick={() => handleDelete(currency._id)} className="text-red-600 hover:text-red-800 text-sm font-medium">
                    Delete
                  </button>
                </div>
              </div>
            ))}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200">
                <div className="flex justify-center">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>

                    {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                      <button
                        key={page}
                        onClick={() => setCurrentPage(page)}
                        className={`px-3 py-2 text-sm font-medium rounded-md ${
                          page === currentPage
                            ? 'text-blue-600 bg-blue-50 border border-blue-300'
                            : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {page}
                      </button>
                    ))}

                    <button
                      onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Bulk Delete Confirmation Modal */}
      {showBulkDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Currencies</h3>
            <p className="text-gray-600 mb-4">Are you sure you want to delete {selectedCurrencies.length} currencies? This action cannot be undone.</p>
            <div className="flex justify-end space-x-3">
              <button onClick={cancelBulkDelete} className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                Cancel
              </button>
              <button
                onClick={confirmBulkDelete}
                disabled={isBulkDeleting}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                Delete {selectedCurrencies.length} Currencies
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CurrenciesPage;

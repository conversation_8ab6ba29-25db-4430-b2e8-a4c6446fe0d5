import React, { useState, useEffect } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTransactions } from '../../hooks/useTransactions';
import { useProperties } from '../../hooks/useProperties';
import { useAuth } from '../../hooks/useAuth';
import { Modal, Input, Select, Button, ErrorMessage, LoadingState } from '../ui';
import { ITransaction, ITransactionCreateData, transactionCreateSchema } from '@alom-rentals/shared';
import { AlertCircle } from 'lucide-react';

interface TransactionFormProps {
  transaction?: ITransaction | null;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  createTransaction?: (data: ITransactionCreateData) => void;
  updateTransaction?: (params: { id: string; data: ITransactionCreateData }) => void;
}

const TRANSACTION_TYPE_OPTIONS = [
  { value: 'property_cost', label: 'Property Cost' },
  { value: 'rental_payment', label: 'Rental Payment' },
  { value: 'dividend_payment', label: 'Dividend Payment' },
  { value: 'manual', label: 'Manual Entry' },
];

const RECURRENT_OPTIONS = [
  { value: 'none', label: 'One-time' },
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'yearly', label: 'Yearly' },
];

export const TransactionForm: React.FC<TransactionFormProps> = ({
  transaction,
  isOpen,
  onClose,
  onSuccess,
  createTransaction: propCreateTransaction,
  updateTransaction: propUpdateTransaction,
}) => {
  const { properties, isLoading: propertiesLoading } = useProperties();
  const { createTransaction: hookCreateTransaction, updateTransaction: hookUpdateTransaction, isCreating, isUpdating } = useTransactions();
  const [error, setError] = useState<string>('');

  // Use passed functions or fallback to hook functions
  const createTransaction = propCreateTransaction || hookCreateTransaction;
  const updateTransaction = propUpdateTransaction || hookUpdateTransaction;

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<ITransactionCreateData>({
    resolver: zodResolver(transactionCreateSchema) as any,
    defaultValues: {
      propertyId: '',
      amount: 0,
      description: '',
      type: 'manual',
      category: '',
      date: new Date(),
      recurrent: 'none',
    },
  });

  useEffect(() => {
    if (transaction) {
      setValue('propertyId', typeof transaction.propertyId === 'string' ? transaction.propertyId : transaction.propertyId._id);
      setValue('amount', transaction.amount);
      setValue('description', transaction.description);
      setValue('type', transaction.type);
      setValue('category', transaction.category || '');
      setValue('date', new Date(transaction.date));
      setValue('recurrent', transaction.recurrent);
    } else {
      reset({
        propertyId: '',
        amount: 0,
        description: '',
        type: 'manual',
        category: '',
        date: new Date(),
        recurrent: 'none',
      });
    }
  }, [transaction, setValue, reset]);

  const onSubmit = async (data: ITransactionCreateData) => {
    try {
      setError('');

      if (transaction) {
        updateTransaction({ id: transaction._id, data });
      } else {
        createTransaction(data);
      }

      onSuccess();
      onClose();
    } catch (err: any) {
      setError(err?.response?.data?.error || err?.message || 'Failed to save transaction');
    }
  };

  const propertyOptions = properties.map((property) => ({
    value: property._id,
    label: property.name,
  }));

  if (propertiesLoading) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} title="Loading...">
        <LoadingState message="Loading properties..." />
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={transaction ? 'Edit Transaction' : 'Create Transaction'} size="lg">
      <form onSubmit={handleSubmit(onSubmit as any)} className="space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Property *</label>
            <Controller
              name="propertyId"
              control={control}
              render={({ field }) => <Select {...field} placeholder="Select property" options={propertyOptions} />}
            />
            {errors.propertyId?.message && <ErrorMessage message={errors.propertyId.message} />}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Type *</label>
            <Controller
              name="type"
              control={control}
              render={({ field }) => <Select {...field} placeholder="Select type" options={TRANSACTION_TYPE_OPTIONS} />}
            />
            {errors.type?.message && <ErrorMessage message={errors.type.message} />}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Description *</label>
          <Controller name="description" control={control} render={({ field }) => <Input {...field} placeholder="Enter transaction description" />} />
          {errors.description?.message && <ErrorMessage message={errors.description.message} />}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Amount *</label>
            <Controller
              name="amount"
              control={control}
              render={({ field }) => (
                <Input {...field} type="number" step="0.01" placeholder="0.00" onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)} />
              )}
            />
            {errors.amount?.message && <ErrorMessage message={errors.amount.message} />}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
            <Controller name="category" control={control} render={({ field }) => <Input {...field} placeholder="Optional category" />} />
            {errors.category?.message && <ErrorMessage message={errors.category.message} />}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Date *</label>
            <Controller
              name="date"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  type="date"
                  value={field.value instanceof Date ? field.value.toISOString().split('T')[0] : field.value}
                  onChange={(e) => field.onChange(new Date(e.target.value))}
                />
              )}
            />
            {errors.date?.message && <ErrorMessage message={errors.date.message} />}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Frequency</label>
            <Controller
              name="recurrent"
              control={control}
              render={({ field }) => <Select {...field} placeholder="Select frequency" options={RECURRENT_OPTIONS} />}
            />
            {errors.recurrent?.message && <ErrorMessage message={errors.recurrent.message} />}
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t">
          <Button type="button" variant="secondary" onClick={onClose} disabled={isCreating || isUpdating}>
            Cancel
          </Button>
          <Button type="submit" disabled={isCreating || isUpdating}>
            {isCreating || isUpdating ? (transaction ? 'Updating...' : 'Creating...') : transaction ? 'Update Transaction' : 'Create Transaction'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

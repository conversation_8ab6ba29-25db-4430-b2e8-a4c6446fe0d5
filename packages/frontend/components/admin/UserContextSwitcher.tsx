import React, { useState, useEffect } from 'react';
import { useUsers } from '../../hooks/useRoleManagement';
import { IUserPublic } from '@alom-rentals/shared';
import { ChevronDown, User, Users, X } from 'lucide-react';

interface UserContextSwitcherProps {
  selectedUserId?: string;
  onUserChange: (userId?: string) => void;
  className?: string;
  showAllUsersOption?: boolean;
}

export const UserContextSwitcher: React.FC<UserContextSwitcherProps> = ({ selectedUserId, onUserChange, className = '', showAllUsersOption = true }) => {
  const [isOpen, setIsOpen] = useState(false);
  const { data: users, isLoading } = useUsers();
  const [selectedUser, setSelectedUser] = useState<IUserPublic | null>(null);

  useEffect(() => {
    if (selectedUserId && users) {
      const user = users.find((u) => u.id === selectedUserId);
      setSelectedUser(user || null);
    } else {
      setSelectedUser(null);
    }
  }, [selectedUserId, users]);

  const handleUserSelect = (userId?: string) => {
    onUserChange(userId);
    setIsOpen(false);
  };

  const clearSelection = (e: React.MouseEvent) => {
    e.stopPropagation();
    handleUserSelect(undefined);
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-10 bg-gray-200 rounded-lg w-48"></div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-between w-full px-4 py-2 text-left bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      >
        <div className="flex items-center space-x-2">
          {selectedUser ? (
            <>
              <User className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-900">{selectedUser.fullName}</span>
              <span className="text-xs text-gray-500">({selectedUser.email})</span>
            </>
          ) : (
            <>
              <Users className="w-4 h-4 text-gray-500" />
              <span className="text-sm text-gray-700">All Users</span>
            </>
          )}
        </div>
        <div className="flex items-center space-x-1">
          {selectedUser && (
            <button onClick={clearSelection} className="p-1 text-gray-400 hover:text-gray-600 rounded" title="Clear selection">
              <X className="w-3 h-3" />
            </button>
          )}
          <ChevronDown className="w-4 h-4 text-gray-400" />
        </div>
      </button>

      {isOpen && (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-auto">
          {showAllUsersOption && (
            <button
              onClick={() => handleUserSelect(undefined)}
              className={`w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-2 ${
                !selectedUserId ? 'bg-blue-50 text-blue-700' : 'text-gray-900'
              }`}
            >
              <Users className="w-4 h-4" />
              <span className="text-sm font-medium">All Users</span>
            </button>
          )}

          {users?.map((user) => (
            <button
              key={user.id}
              onClick={() => handleUserSelect(user.id)}
              className={`w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-2 ${
                selectedUserId === user.id ? 'bg-blue-50 text-blue-700' : 'text-gray-900'
              }`}
            >
              <User className="w-4 h-4" />
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">{user.fullName}</div>
                <div className="text-xs text-gray-500 truncate">
                  {user.email} • {user.role}
                </div>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

interface UserContextBadgeProps {
  selectedUserId?: string;
  className?: string;
}

export const UserContextBadge: React.FC<UserContextBadgeProps> = ({ selectedUserId, className = '' }) => {
  const { data: users } = useUsers();
  const selectedUser = selectedUserId && users ? users.find((u) => u.id === selectedUserId) : null;

  if (!selectedUser) {
    return (
      <div className={`inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded-full ${className}`}>
        <Users className="w-3 h-3 mr-1" />
        All Users
      </div>
    );
  }

  return (
    <div className={`inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full ${className}`}>
      <User className="w-3 h-3 mr-1" />
      {selectedUser.fullName}
    </div>
  );
};

interface UserContextProviderProps {
  children: (context: {
    selectedUserId?: string;
    setSelectedUserId: (userId?: string) => void;
    selectedUser?: IUserPublic;
    isAllUsers: boolean;
  }) => React.ReactNode;
}

export const UserContextProvider: React.FC<UserContextProviderProps> = ({ children }) => {
  const [selectedUserId, setSelectedUserId] = useState<string | undefined>();
  const { data: users } = useUsers();

  const selectedUser = selectedUserId && users ? users.find((u) => u.id === selectedUserId) : undefined;
  const isAllUsers = !selectedUserId;

  return (
    <>
      {children({
        selectedUserId,
        setSelectedUserId,
        selectedUser,
        isAllUsers,
      })}
    </>
  );
};

export default UserContextSwitcher;

import React, { useState } from 'react';
import { AdminOnly } from './RoleGuard';
import { useOperatorsWithAssignments, useAvailableCustomers } from '../../hooks/useOperatorAssignments';
import { OperatorAssignmentModal } from './OperatorAssignmentModal';
import { BulkAssignmentModal } from './BulkAssignmentModal';
import { IOperatorWithAssignments, IAssignedCustomer } from '@alom-rentals/shared';

export const OperatorAssignments: React.FC = () => {
  const [selectedOperator, setSelectedOperator] = useState<IOperatorWithAssignments | null>(null);
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [showBulkModal, setShowBulkModal] = useState(false);

  const { data: operatorsData, isLoading: operatorsLoading, error: operatorsError } = useOperatorsWithAssignments();
  const { data: customersData, isLoading: customersLoading } = useAvailableCustomers();

  const operators = operatorsData?.data || [];
  const customers = customersData?.data || [];

  const handleRemoveAssignment = async (operatorId: string, customerId: string) => {
    // Find the assignment ID (this would need to be tracked or fetched separately)
    // For now, we'll need to implement a way to get the assignment ID
    console.log('Remove assignment:', { operatorId, customerId });
    // TODO: Implement assignment removal
  };

  if (operatorsLoading || customersLoading) {
    return (
      <AdminOnly>
        <div className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </AdminOnly>
    );
  }

  if (operatorsError) {
    return (
      <AdminOnly>
        <div className="p-6">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <h3 className="text-red-800 font-medium">Error loading operator assignments</h3>
            <p className="text-red-600 text-sm mt-1">{operatorsError.message || 'Failed to load data'}</p>
          </div>
        </div>
      </AdminOnly>
    );
  }

  return (
    <AdminOnly>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Operator Assignments</h1>
          <div className="flex space-x-3">
            <button
              onClick={() => setShowBulkModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Bulk Assign
            </button>
            <button
              onClick={() => setShowAssignmentModal(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              New Assignment
            </button>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Total Operators</h3>
            <p className="text-3xl font-bold text-blue-600">{operators.length}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Total Customers</h3>
            <p className="text-3xl font-bold text-green-600">{customers.length}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Total Assignments</h3>
            <p className="text-3xl font-bold text-purple-600">
              {operators.reduce((sum: number, op: IOperatorWithAssignments) => sum + op.totalAssignments, 0)}
            </p>
          </div>
        </div>

        {/* Operators List */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Operators and Their Assignments</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {operators.length === 0 ? (
              <div className="p-6 text-center text-gray-500">No operators found. Create operator users first.</div>
            ) : (
              operators.map((operator: IOperatorWithAssignments) => (
                <div key={operator.id} className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{operator.fullName}</h3>
                      <p className="text-sm text-gray-600">{operator.email}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {operator.totalAssignments} assignments
                      </span>
                      <button
                        onClick={() => {
                          setSelectedOperator(operator);
                          setShowAssignmentModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                      >
                        Add Assignment
                      </button>
                    </div>
                  </div>

                  {operator.assignedCustomers.length === 0 ? (
                    <div className="text-sm text-gray-500 italic">No customers assigned</div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {operator.assignedCustomers.map((customer: IAssignedCustomer) => (
                        <div key={customer.id} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium text-gray-900">{customer.fullName}</h4>
                            <button
                              onClick={() => handleRemoveAssignment(operator.id, customer.id)}
                              className="text-red-600 hover:text-red-800 text-sm"
                              title="Remove assignment"
                            >
                              ×
                            </button>
                          </div>
                          <p className="text-sm text-gray-600 mb-3">{customer.email}</p>
                          <div className="flex flex-wrap gap-1">
                            {customer.permissions.canCreateProperties && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Create</span>
                            )}
                            {customer.permissions.canEditProperties && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Edit</span>
                            )}
                            {customer.permissions.canDeleteProperties && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Delete</span>
                            )}
                            {customer.permissions.canManageRentals && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Rentals</span>
                            )}
                            {customer.permissions.canViewFinancials && (
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                Financials
                              </span>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>

        {/* Modals */}
        {showAssignmentModal && (
          <OperatorAssignmentModal
            isOpen={showAssignmentModal}
            onClose={() => {
              setShowAssignmentModal(false);
              setSelectedOperator(null);
            }}
            operators={operators}
            customers={customers}
            selectedOperator={selectedOperator}
          />
        )}

        {showBulkModal && <BulkAssignmentModal isOpen={showBulkModal} onClose={() => setShowBulkModal(false)} operators={operators} customers={customers} />}
      </div>
    </AdminOnly>
  );
};

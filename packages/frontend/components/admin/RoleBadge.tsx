import React from 'react';
import { UserRole } from '@alom-rentals/shared';
import { getRoleDisplayName, getRoleColor } from '../../api/admin';

interface RoleBadgeProps {
  role: UserRole;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outline' | 'solid';
  className?: string;
}

/**
 * RoleBadge component for displaying user roles with appropriate styling
 */
export const RoleBadge: React.FC<RoleBadgeProps> = ({ role, size = 'md', variant = 'default', className = '' }) => {
  const displayName = getRoleDisplayName(role);
  const colorClasses = getRoleColor(role);

  // Size classes
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-1 text-sm',
    lg: 'px-4 py-2 text-base',
  };

  // Variant classes
  const variantClasses = {
    default: `${colorClasses} rounded-full font-medium`,
    outline: `border-2 ${getBorderColor(role)} ${getTextColor(role)} bg-transparent rounded-full font-medium`,
    solid: `${getSolidColor(role)} text-white rounded-full font-medium`,
  };

  const combinedClasses = `
    inline-flex items-center justify-center
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${className}
  `.trim();

  return <span className={combinedClasses}>{displayName}</span>;
};

// Helper functions for role-specific styling
const getBorderColor = (role: UserRole): string => {
  switch (role) {
    case 'ADMIN':
      return 'border-red-300';
    case 'OPERATOR':
      return 'border-blue-300';
    case 'CUSTOMER':
      return 'border-green-300';
    default:
      return 'border-gray-300';
  }
};

const getTextColor = (role: UserRole): string => {
  switch (role) {
    case 'ADMIN':
      return 'text-red-700';
    case 'OPERATOR':
      return 'text-blue-700';
    case 'CUSTOMER':
      return 'text-green-700';
    default:
      return 'text-gray-700';
  }
};

const getSolidColor = (role: UserRole): string => {
  switch (role) {
    case 'ADMIN':
      return 'bg-red-600';
    case 'OPERATOR':
      return 'bg-blue-600';
    case 'CUSTOMER':
      return 'bg-green-600';
    default:
      return 'bg-gray-600';
  }
};

// Role icon component
interface RoleIconProps {
  role: UserRole;
  size?: number;
  className?: string;
}

export const RoleIcon: React.FC<RoleIconProps> = ({ role, size = 16, className = '' }) => {
  const iconColor = getTextColor(role);

  const getIcon = () => {
    switch (role) {
      case 'ADMIN':
        return (
          <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor" className={`${iconColor} ${className}`}>
            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" />
          </svg>
        );
      case 'OPERATOR':
        return (
          <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor" className={`${iconColor} ${className}`}>
            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7V9C15 11.8 12.8 14 10 14S5 11.8 5 9V7H3V9C3 12.9 6.1 16 10 16V18H8V20H16V18H14V16C17.9 16 21 12.9 21 9Z" />
          </svg>
        );
      case 'CUSTOMER':
        return (
          <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor" className={`${iconColor} ${className}`}>
            <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" />
          </svg>
        );
      default:
        return (
          <svg width={size} height={size} viewBox="0 0 24 24" fill="currentColor" className={`${iconColor} ${className}`}>
            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" />
          </svg>
        );
    }
  };

  return getIcon();
};

// Combined role badge with icon
interface RoleBadgeWithIconProps extends RoleBadgeProps {
  showIcon?: boolean;
  iconSize?: number;
}

export const RoleBadgeWithIcon: React.FC<RoleBadgeWithIconProps> = ({
  role,
  size = 'md',
  variant = 'default',
  className = '',
  showIcon = true,
  iconSize = 14,
}) => {
  const displayName = getRoleDisplayName(role);
  const colorClasses = getRoleColor(role);

  // Size classes
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs gap-1',
    md: 'px-3 py-1 text-sm gap-1.5',
    lg: 'px-4 py-2 text-base gap-2',
  };

  // Variant classes
  const variantClasses = {
    default: `${colorClasses} rounded-full font-medium`,
    outline: `border-2 ${getBorderColor(role)} ${getTextColor(role)} bg-transparent rounded-full font-medium`,
    solid: `${getSolidColor(role)} text-white rounded-full font-medium`,
  };

  const combinedClasses = `
    inline-flex items-center justify-center
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${className}
  `.trim();

  return (
    <span className={combinedClasses}>
      {showIcon && <RoleIcon role={role} size={iconSize} />}
      {displayName}
    </span>
  );
};

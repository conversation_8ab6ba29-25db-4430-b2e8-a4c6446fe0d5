import React, { useState } from 'react';
import { useBulkCreateOperatorAssignments } from '../../hooks/useOperatorAssignments';
import { IOperatorWithAssignments } from '@alom-rentals/shared';

interface BulkAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  operators: IOperatorWithAssignments[];
  customers: Array<{ id: string; email: string; fullName: string; createdAt: Date }>;
}

export const BulkAssignmentModal: React.FC<BulkAssignmentModalProps> = ({ isOpen, onClose, operators, customers }) => {
  const [operatorId, setOperatorId] = useState('');
  const [selectedCustomerIds, setSelectedCustomerIds] = useState<string[]>([]);
  const [permissions, setPermissions] = useState({
    canCreateProperties: true,
    canEditProperties: true,
    canDeleteProperties: false,
    canManageRentals: true,
    canViewFinancials: true,
  });

  const bulkCreateMutation = useBulkCreateOperatorAssignments();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!operatorId || selectedCustomerIds.length === 0) {
      return;
    }

    try {
      await bulkCreateMutation.mutateAsync({
        operatorId,
        customerIds: selectedCustomerIds,
        permissions,
      });
      onClose();
      // Reset form
      setOperatorId('');
      setSelectedCustomerIds([]);
      setPermissions({
        canCreateProperties: true,
        canEditProperties: true,
        canDeleteProperties: false,
        canManageRentals: true,
        canViewFinancials: true,
      });
    } catch (error) {
      console.error('Failed to create bulk assignments:', error);
    }
  };

  const handleCustomerToggle = (customerId: string) => {
    setSelectedCustomerIds((prev) => (prev.includes(customerId) ? prev.filter((id) => id !== customerId) : [...prev, customerId]));
  };

  const handleSelectAll = () => {
    const availableCustomers = getAvailableCustomers();
    if (selectedCustomerIds.length === availableCustomers.length) {
      setSelectedCustomerIds([]);
    } else {
      setSelectedCustomerIds(availableCustomers.map((c) => c.id));
    }
  };

  const getAvailableCustomers = () => {
    if (!operatorId) return customers;

    const operator = operators.find((op) => op.id === operatorId);
    return customers.filter((customer) => !operator?.assignedCustomers.some((assigned) => assigned.id === customer.id));
  };

  const availableCustomers = getAvailableCustomers();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Bulk Assign Customers to Operator</h3>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Operator Selection */}
            <div>
              <label htmlFor="operator" className="block text-sm font-medium text-gray-700 mb-1">
                Operator
              </label>
              <select
                id="operator"
                value={operatorId}
                onChange={(e) => {
                  setOperatorId(e.target.value);
                  setSelectedCustomerIds([]); // Reset customer selection when operator changes
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Select an operator...</option>
                {operators.map((operator) => (
                  <option key={operator.id} value={operator.id}>
                    {operator.fullName} ({operator.email})
                  </option>
                ))}
              </select>
            </div>

            {/* Customer Selection */}
            {operatorId && (
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">Customers ({selectedCustomerIds.length} selected)</label>
                  {availableCustomers.length > 0 && (
                    <button type="button" onClick={handleSelectAll} className="text-sm text-blue-600 hover:text-blue-800">
                      {selectedCustomerIds.length === availableCustomers.length ? 'Deselect All' : 'Select All'}
                    </button>
                  )}
                </div>

                {availableCustomers.length === 0 ? (
                  <p className="text-sm text-gray-500 p-3 bg-gray-50 rounded-md">All customers are already assigned to this operator.</p>
                ) : (
                  <div className="max-h-60 overflow-y-auto border border-gray-300 rounded-md">
                    {availableCustomers.map((customer) => (
                      <label key={customer.id} className="flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-200 last:border-b-0">
                        <input
                          type="checkbox"
                          checked={selectedCustomerIds.includes(customer.id)}
                          onChange={() => handleCustomerToggle(customer.id)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <div className="ml-3">
                          <div className="text-sm font-medium text-gray-900">{customer.fullName}</div>
                          <div className="text-sm text-gray-500">{customer.email}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Permissions */}
            {operatorId && selectedCustomerIds.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Permissions (applied to all selected customers)</label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={permissions.canCreateProperties}
                      onChange={(e) => setPermissions({ ...permissions, canCreateProperties: e.target.checked })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Can create properties</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={permissions.canEditProperties}
                      onChange={(e) => setPermissions({ ...permissions, canEditProperties: e.target.checked })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Can edit properties</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={permissions.canDeleteProperties}
                      onChange={(e) => setPermissions({ ...permissions, canDeleteProperties: e.target.checked })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Can delete properties</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={permissions.canManageRentals}
                      onChange={(e) => setPermissions({ ...permissions, canManageRentals: e.target.checked })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Can manage rentals</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={permissions.canViewFinancials}
                      onChange={(e) => setPermissions({ ...permissions, canViewFinancials: e.target.checked })}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Can view financials</span>
                  </label>
                </div>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={bulkCreateMutation.isPending || !operatorId || selectedCustomerIds.length === 0}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {bulkCreateMutation.isPending ? 'Creating...' : `Create ${selectedCustomerIds.length} Assignment${selectedCustomerIds.length !== 1 ? 's' : ''}`}
              </button>
            </div>
          </form>

          {bulkCreateMutation.error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{bulkCreateMutation.error.message || 'Failed to create bulk assignments'}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

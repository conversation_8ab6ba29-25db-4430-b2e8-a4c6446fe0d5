import React, { useState } from 'react';
import { IUserPublic } from '@alom-rentals/shared';
import { usePasswordManagement, usePasswordValidation } from '../../hooks/usePasswordManagement';
import { Button } from '../ui/Button';
import { Key, Eye, EyeOff, RefreshCw, Shield, AlertTriangle, Copy, Check } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface PasswordManagementProps {
  user: IUserPublic;
  onClose?: () => void;
}

export const PasswordManagement: React.FC<PasswordManagementProps> = ({ user, onClose }) => {
  const [temporaryPassword, setTemporaryPassword] = useState('');
  const [forceChange, setForceChange] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [copiedToken, setCopiedToken] = useState(false);
  const [resetToken, setResetToken] = useState<string | null>(null);

  const { resetPassword, forceChange: forceChangeHook, generateToken, isLoading } = usePasswordManagement();
  const { validatePassword, generateSecurePassword } = usePasswordValidation();

  const handleResetPassword = async () => {
    if (!temporaryPassword.trim()) {
      toast.error('Please enter a temporary password');
      return;
    }

    const validation = validatePassword(temporaryPassword);
    if (!validation.isValid) {
      toast.error(`Password validation failed: ${validation.errors[0]}`);
      return;
    }

    await resetPassword.mutateAsync({
      userId: user.id,
      passwordData: {
        temporaryPassword,
        forceChange,
      },
    });
    setTemporaryPassword('');
    onClose?.();
  };

  const handleForcePasswordChange = async (force: boolean) => {
    await forceChangeHook.mutateAsync({
      userId: user.id,
      forceChangeData: { forceChange: force },
    });
  };

  const handleGenerateToken = async () => {
    const result = await generateToken.mutateAsync(user.id);
    setResetToken(result.resetToken);
  };

  const handleGenerateSecurePassword = () => {
    const newPassword = generateSecurePassword(12);
    setTemporaryPassword(newPassword);
    setShowPassword(true);
  };

  const handleCopyToken = async () => {
    if (resetToken) {
      try {
        await navigator.clipboard.writeText(resetToken);
        setCopiedToken(true);
        toast.success('Reset token copied to clipboard');
        setTimeout(() => setCopiedToken(false), 2000);
      } catch {
        toast.error('Failed to copy token');
      }
    }
  };

  const validation = validatePassword(temporaryPassword);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <Key className="w-6 h-6 text-blue-600" />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Password Management</h3>
          <p className="text-sm text-gray-600">
            Manage password for {user.fullName} ({user.email})
          </p>
        </div>
      </div>

      {/* Reset Password Section */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
          <Shield className="w-4 h-4 mr-2" />
          Reset Password
        </h4>

        <div className="space-y-4">
          {/* Password Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Temporary Password</label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={temporaryPassword}
                onChange={(e) => setTemporaryPassword(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pr-20"
                placeholder="Enter temporary password"
              />
              <div className="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                <button type="button" onClick={() => setShowPassword(!showPassword)} className="text-gray-400 hover:text-gray-600">
                  {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
                <button type="button" onClick={handleGenerateSecurePassword} className="text-blue-600 hover:text-blue-800" title="Generate secure password">
                  <RefreshCw className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Password Validation */}
            {temporaryPassword && (
              <div className="mt-2">
                {validation.isValid ? (
                  <div className="flex items-center text-green-600 text-sm">
                    <Check className="w-4 h-4 mr-1" />
                    Password meets security requirements
                  </div>
                ) : (
                  <div className="space-y-1">
                    {validation.errors.map((error, index) => (
                      <div key={index} className="flex items-center text-red-600 text-sm">
                        <AlertTriangle className="w-4 h-4 mr-1" />
                        {error}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Force Change Checkbox */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="forceChange"
              checked={forceChange}
              onChange={(e) => setForceChange(e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="forceChange" className="ml-2 block text-sm text-gray-700">
              Force password change on next login
            </label>
          </div>

          {/* Reset Button */}
          <Button onClick={handleResetPassword} disabled={!temporaryPassword || !validation.isValid || isLoading} className="w-full">
            {resetPassword.isPending ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Resetting Password...
              </>
            ) : (
              <>
                <Key className="w-4 h-4 mr-2" />
                Reset Password
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Force Password Change Section */}
      <div className="bg-yellow-50 p-4 rounded-lg">
        <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
          <AlertTriangle className="w-4 h-4 mr-2 text-yellow-600" />
          Force Password Change
        </h4>

        <p className="text-sm text-gray-600 mb-3">Require the user to change their password on their next login.</p>

        <div className="flex space-x-2">
          <Button onClick={() => handleForcePasswordChange(true)} disabled={isLoading} variant="secondary" size="sm">
            {forceChangeHook.isPending ? <RefreshCw className="w-4 h-4 mr-2 animate-spin" /> : <Shield className="w-4 h-4 mr-2" />}
            Enable Force Change
          </Button>

          <Button onClick={() => handleForcePasswordChange(false)} disabled={isLoading} variant="secondary" size="sm">
            Disable Force Change
          </Button>
        </div>
      </div>

      {/* Generate Reset Token Section */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
          <Key className="w-4 h-4 mr-2 text-blue-600" />
          Password Reset Token
        </h4>

        <p className="text-sm text-gray-600 mb-3">Generate a secure token that can be used for password reset.</p>

        <Button onClick={handleGenerateToken} disabled={isLoading} variant="secondary" size="sm" className="mb-3">
          {generateToken.isPending ? (
            <>
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              Generating Token...
            </>
          ) : (
            <>
              <Key className="w-4 h-4 mr-2" />
              Generate Reset Token
            </>
          )}
        </Button>

        {resetToken && (
          <div className="mt-3 p-3 bg-white border border-blue-200 rounded-md">
            <div className="flex items-center justify-between">
              <div className="flex-1 min-w-0">
                <p className="text-xs font-medium text-gray-700 mb-1">Reset Token:</p>
                <p className="text-sm font-mono text-gray-900 break-all">{resetToken}</p>
              </div>
              <button onClick={handleCopyToken} className="ml-2 p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-100 rounded" title="Copy token">
                {copiedToken ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-2">This token expires in 10 minutes. In production, this should be sent via email.</p>
          </div>
        )}
      </div>

      {/* Actions */}
      {onClose && (
        <div className="flex justify-end space-x-2 pt-4 border-t">
          <Button onClick={onClose} variant="secondary">
            Close
          </Button>
        </div>
      )}
    </div>
  );
};

export default PasswordManagement;

import React from 'react';
import { UserRole } from '@alom-rentals/shared';
import { useAuth } from '../../hooks/useAuth';

interface RoleGuardProps {
  allowedRoles: UserRole[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireAll?: boolean; // If true, user must have ALL roles (for future multi-role support)
}

/**
 * RoleGuard component for conditional rendering based on user roles
 *
 * @param allowedRoles - Array of roles that are allowed to see the content
 * @param children - Content to render if user has required role
 * @param fallback - Content to render if user doesn't have required role
 * @param requireAll - If true, user must have all specified roles (default: false)
 */
export const RoleGuard: React.FC<RoleGuardProps> = ({ allowedRoles, children, fallback = null, requireAll = false }) => {
  const { user } = useAuth();

  // If no user is authenticated, don't render anything
  if (!user || !user.role) {
    return <>{fallback}</>;
  }

  const userRole = user.role as UserRole;

  // Check if user has required role(s)
  const hasRequiredRole = requireAll
    ? allowedRoles.every((role) => userRole === role) // For future multi-role support
    : allowedRoles.includes(userRole);

  if (!hasRequiredRole) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

// Specific role guard components for common use cases
export const AdminOnly: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback = null }) => (
  <RoleGuard allowedRoles={['ADMIN']} fallback={fallback}>
    {children}
  </RoleGuard>
);

export const OperatorOrAdmin: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback = null }) => (
  <RoleGuard allowedRoles={['ADMIN', 'OPERATOR']} fallback={fallback}>
    {children}
  </RoleGuard>
);

export const CustomerOnly: React.FC<{ children: React.ReactNode; fallback?: React.ReactNode }> = ({ children, fallback = null }) => (
  <RoleGuard allowedRoles={['CUSTOMER']} fallback={fallback}>
    {children}
  </RoleGuard>
);

// Note: withRoleGuard and useRoleGuard moved to utils/roleGuardUtils.ts to avoid Fast Refresh warnings

// Component for displaying role-based messages
interface RoleMessageProps {
  role: UserRole;
  messages: Partial<Record<UserRole, React.ReactNode>>;
  defaultMessage?: React.ReactNode;
}

export const RoleMessage: React.FC<RoleMessageProps> = ({ role, messages, defaultMessage = null }) => {
  const message = messages[role] || defaultMessage;
  return <>{message}</>;
};

// Component for role-based styling
interface RoleStyledProps {
  role: UserRole;
  children: React.ReactNode;
  className?: string;
  roleStyles?: Partial<Record<UserRole, string>>;
}

export const RoleStyled: React.FC<RoleStyledProps> = ({ role, children, className = '', roleStyles = {} }) => {
  const defaultStyles: Record<UserRole, string> = {
    ADMIN: 'border-red-200 bg-red-50',
    OPERATOR: 'border-blue-200 bg-blue-50',
    CUSTOMER: 'border-green-200 bg-green-50',
  };

  const roleStyle = roleStyles[role] || defaultStyles[role] || '';
  const combinedClassName = `${className} ${roleStyle}`.trim();

  return <div className={combinedClassName}>{children}</div>;
};

import React, { useState, useEffect } from 'react';
import { useCreateOperatorAssignment } from '../../hooks/useOperatorAssignments';
import { IOperatorWithAssignments } from '@alom-rentals/shared';

interface OperatorAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  operators: IOperatorWithAssignments[];
  customers: Array<{ id: string; email: string; fullName: string; createdAt: Date }>;
  selectedOperator?: IOperatorWithAssignments | null;
}

export const OperatorAssignmentModal: React.FC<OperatorAssignmentModalProps> = ({ isOpen, onClose, operators, customers, selectedOperator }) => {
  const [operatorId, setOperatorId] = useState(selectedOperator?.id || '');
  const [customerId, setCustomerId] = useState('');
  const [permissions, setPermissions] = useState({
    canCreateProperties: true,
    canEditProperties: true,
    canDeleteProperties: false,
    canManageRentals: true,
    canViewFinancials: true,
  });

  const createAssignmentMutation = useCreateOperatorAssignment();

  useEffect(() => {
    if (selectedOperator) {
      setOperatorId(selectedOperator.id);
    }
  }, [selectedOperator]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!operatorId || !customerId) {
      return;
    }

    try {
      await createAssignmentMutation.mutateAsync({
        operatorId,
        customerId,
        permissions,
      });
      onClose();
      // Reset form
      setOperatorId(selectedOperator?.id || '');
      setCustomerId('');
      setPermissions({
        canCreateProperties: true,
        canEditProperties: true,
        canDeleteProperties: false,
        canManageRentals: true,
        canViewFinancials: true,
      });
    } catch (error) {
      console.error('Failed to create assignment:', error);
    }
  };

  const availableCustomers = customers.filter((customer) => {
    const operator = operators.find((op) => op.id === operatorId);
    return !operator?.assignedCustomers.some((assigned) => assigned.id === customer.id);
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div className="mt-3">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Create Operator Assignment</h3>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Operator Selection */}
            <div>
              <label htmlFor="operator" className="block text-sm font-medium text-gray-700 mb-1">
                Operator
              </label>
              <select
                id="operator"
                value={operatorId}
                onChange={(e) => setOperatorId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
                disabled={!!selectedOperator}
              >
                <option value="">Select an operator...</option>
                {operators.map((operator) => (
                  <option key={operator.id} value={operator.id}>
                    {operator.fullName} ({operator.email})
                  </option>
                ))}
              </select>
            </div>

            {/* Customer Selection */}
            <div>
              <label htmlFor="customer" className="block text-sm font-medium text-gray-700 mb-1">
                Customer
              </label>
              <select
                id="customer"
                value={customerId}
                onChange={(e) => setCustomerId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              >
                <option value="">Select a customer...</option>
                {availableCustomers.map((customer) => (
                  <option key={customer.id} value={customer.id}>
                    {customer.fullName} ({customer.email})
                  </option>
                ))}
              </select>
              {operatorId && availableCustomers.length === 0 && (
                <p className="text-sm text-gray-500 mt-1">All customers are already assigned to this operator.</p>
              )}
            </div>

            {/* Permissions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Permissions</label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={permissions.canCreateProperties}
                    onChange={(e) => setPermissions({ ...permissions, canCreateProperties: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Can create properties</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={permissions.canEditProperties}
                    onChange={(e) => setPermissions({ ...permissions, canEditProperties: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Can edit properties</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={permissions.canDeleteProperties}
                    onChange={(e) => setPermissions({ ...permissions, canDeleteProperties: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Can delete properties</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={permissions.canManageRentals}
                    onChange={(e) => setPermissions({ ...permissions, canManageRentals: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Can manage rentals</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={permissions.canViewFinancials}
                    onChange={(e) => setPermissions({ ...permissions, canViewFinancials: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Can view financials</span>
                </label>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={createAssignmentMutation.isPending || !operatorId || !customerId}
                className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {createAssignmentMutation.isPending ? 'Creating...' : 'Create Assignment'}
              </button>
            </div>
          </form>

          {createAssignmentMutation.error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{createAssignmentMutation.error.message || 'Failed to create assignment'}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

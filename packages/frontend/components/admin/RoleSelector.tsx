import React from 'react';
import { UserRole } from '@alom-rentals/shared';
import { getRoleDisplayName, getAvailableRoles } from '../../api/admin';
import { useRolePermissions } from '../../hooks/useRoleManagement';
import { RoleBadgeWithIcon } from './RoleBadge';

interface RoleSelectorProps {
  currentRole: UserRole;
  onRoleChange: (role: UserRole) => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  showCurrentRoleBadge?: boolean;
}

/**
 * RoleSelector component for changing user roles
 */
export const RoleSelector: React.FC<RoleSelectorProps> = ({
  currentRole,
  onRoleChange,
  disabled = false,
  size = 'md',
  className = '',
  showCurrentRoleBadge = true,
}) => {
  const { currentUserRole } = useRolePermissions();
  const availableRoles = getAvailableRoles(currentUserRole);

  // Size classes for select element
  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base',
  };

  const selectClasses = `
    border border-gray-300 rounded-md shadow-sm
    focus:ring-2 focus:ring-blue-500 focus:border-blue-500
    disabled:bg-gray-100 disabled:cursor-not-allowed
    ${sizeClasses[size]}
    ${className}
  `.trim();

  if (availableRoles.length === 0) {
    return showCurrentRoleBadge ? (
      <RoleBadgeWithIcon role={currentRole} size={size} />
    ) : (
      <span className="text-gray-500 text-sm">No permission to change roles</span>
    );
  }

  return (
    <div className="flex items-center gap-2">
      {showCurrentRoleBadge && <RoleBadgeWithIcon role={currentRole} size={size} variant="outline" />}
      <select value={currentRole} onChange={(e) => onRoleChange(e.target.value as UserRole)} disabled={disabled} className={selectClasses}>
        {availableRoles.map((role) => (
          <option key={role} value={role}>
            {getRoleDisplayName(role)}
          </option>
        ))}
      </select>
    </div>
  );
};

// Bulk role selector for multiple users
interface BulkRoleSelectorProps {
  selectedCount: number;
  onRoleChange: (role: UserRole) => void;
  disabled?: boolean;
  className?: string;
}

export const BulkRoleSelector: React.FC<BulkRoleSelectorProps> = ({ selectedCount, onRoleChange, disabled = false, className = '' }) => {
  const { currentUserRole } = useRolePermissions();
  const availableRoles = getAvailableRoles(currentUserRole);

  if (availableRoles.length === 0 || selectedCount === 0) {
    return null;
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <span className="text-sm text-gray-600">
        Change role for {selectedCount} selected user{selectedCount !== 1 ? 's' : ''}:
      </span>
      <select
        onChange={(e) => {
          if (e.target.value) {
            onRoleChange(e.target.value as UserRole);
            e.target.value = ''; // Reset selection
          }
        }}
        disabled={disabled}
        className="px-3 py-1 text-sm border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
        defaultValue=""
      >
        <option value="" disabled>
          Select new role...
        </option>
        {availableRoles.map((role) => (
          <option key={role} value={role}>
            {getRoleDisplayName(role)}
          </option>
        ))}
      </select>
    </div>
  );
};

// Role comparison component
interface RoleComparisonProps {
  oldRole: UserRole;
  newRole: UserRole;
  className?: string;
}

export const RoleComparison: React.FC<RoleComparisonProps> = ({ oldRole, newRole, className = '' }) => {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <RoleBadgeWithIcon role={oldRole} size="sm" variant="outline" />
      <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
      </svg>
      <RoleBadgeWithIcon role={newRole} size="sm" />
    </div>
  );
};

// Role permissions display
interface RolePermissionsProps {
  role: UserRole;
  className?: string;
}

export const RolePermissions: React.FC<RolePermissionsProps> = ({ role, className = '' }) => {
  const getPermissions = (role: UserRole): string[] => {
    switch (role) {
      case 'ADMIN':
        return ['Full system access', 'Manage all users and roles', 'View all data across users', 'System administration', 'Audit log access'];
      case 'OPERATOR':
        return ['Manage properties for assigned customers', 'Create and edit rentals', 'Manage guests and services', 'Limited to assigned customers only'];
      case 'CUSTOMER':
        return ['View own properties and rentals', 'Read-only access to own data', 'Export personal data', 'Update profile settings'];
      default:
        return [];
    }
  };

  const permissions = getPermissions(role);

  return (
    <div className={className}>
      <div className="flex items-center gap-2 mb-2">
        <RoleBadgeWithIcon role={role} size="sm" />
        <span className="font-medium text-gray-900">Permissions</span>
      </div>
      <ul className="text-sm text-gray-600 space-y-1">
        {permissions.map((permission, index) => (
          <li key={index} className="flex items-start gap-2">
            <svg className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
            {permission}
          </li>
        ))}
      </ul>
    </div>
  );
};

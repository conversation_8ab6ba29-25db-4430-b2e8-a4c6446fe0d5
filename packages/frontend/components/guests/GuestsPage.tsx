import { useState } from 'react';
import { useGuests } from '../../hooks/useGuests';
import { GuestForm } from './GuestForm';
import { Plus, User, Mail, Phone, FileText, Globe, MapPin, Edit, Trash2, Search } from 'lucide-react';
import { Guest } from '../../hooks/useGuests';
import { IGuest } from '@alom-rentals/shared';

export function GuestsPage() {
  const { guests, isLoading, deleteGuest, isDeleting, bulkDeleteGuests, isBulkDeleting } = useGuests();
  const [showForm, setShowForm] = useState(false);
  const [editingGuest, setEditingGuest] = useState<Guest | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(12); // 12 cards per page for nice grid layout

  // Selection state
  const [selectedGuests, setSelectedGuests] = useState<IGuest[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Filter guests based on search query
  const filteredGuests = guests.filter((guest: IGuest) => {
    if (!searchQuery) return true;
    const query = searchQuery.toLowerCase();
    return (
      guest.firstName.toLowerCase().includes(query) ||
      guest.lastName.toLowerCase().includes(query) ||
      guest.email.toLowerCase().includes(query) ||
      guest.documentNumber.toLowerCase().includes(query) ||
      (guest.phoneNumber && guest.phoneNumber.toLowerCase().includes(query))
    );
  });

  // Paginated guests
  const paginatedGuests = filteredGuests.slice((currentPage - 1) * pageSize, currentPage * pageSize);
  const totalPages = Math.ceil(filteredGuests.length / pageSize);

  // Bulk delete handlers
  const handleBulkDelete = () => {
    if (selectedGuests.length === 0) return;
    setShowBulkDeleteConfirm(true);
  };

  const confirmBulkDelete = async () => {
    if (selectedGuests.length === 0) return;

    const guestIds = selectedGuests.map((g) => g._id);
    const result = await bulkDeleteGuests(guestIds);

    if (result.success) {
      setSelectedGuests([]);
      setShowBulkDeleteConfirm(false);
      // Reset to first page if current page becomes empty
      if (paginatedGuests.length === selectedGuests.length && currentPage > 1) {
        setCurrentPage(currentPage - 1);
      }
    }
  };

  const cancelBulkDelete = () => {
    setShowBulkDeleteConfirm(false);
  };

  // Selection handlers
  const handleGuestSelect = (guest: IGuest, selected: boolean) => {
    if (selected) {
      setSelectedGuests([...selectedGuests, guest]);
    } else {
      setSelectedGuests(selectedGuests.filter((g) => g._id !== guest._id));
    }
  };

  const handleSelectAll = () => {
    setSelectedGuests([...filteredGuests]);
  };

  const handleSelectNone = () => {
    setSelectedGuests([]);
  };

  const isGuestSelected = (guest: IGuest) => {
    return selectedGuests.some((g) => g._id === guest._id);
  };

  const handleEditGuest = (guest: Guest) => {
    setEditingGuest(guest);
    setShowForm(true);
  };

  const handleDeleteGuest = async (guestId: string) => {
    const result = await deleteGuest(guestId);
    if (!result.error) {
      setDeleteConfirmId(null);
    }
  };

  const handleFormSuccess = () => {
    setShowForm(false);
    setEditingGuest(null);
  };

  const handleFormCancel = () => {
    setShowForm(false);
    setEditingGuest(null);
  };

  if (showForm) {
    return <GuestForm guest={editingGuest} onCancel={handleFormCancel} onSuccess={handleFormSuccess} />;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading guests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Guests</h1>
            <p className="text-gray-600 mt-2">Manage guest information for your rental properties</p>
          </div>
          <button
            onClick={() => setShowForm(true)}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="w-4 h-4" />
            <span>Add Guest</span>
          </button>
        </div>

        {/* Search */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search guests by name, email, or document number..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Bulk Actions */}
        {selectedGuests.length > 0 && (
          <div className="bg-white rounded-xl border border-gray-200 p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-600">{selectedGuests.length} selected</span>
                <button onClick={handleSelectAll} className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                  Select All
                </button>
                <button onClick={handleSelectNone} className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                  Select None
                </button>
              </div>
              <button
                onClick={handleBulkDelete}
                disabled={isBulkDeleting}
                className="flex items-center space-x-2 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                <Trash2 className="w-4 h-4" />
                <span>Delete {selectedGuests.length}</span>
              </button>
            </div>
          </div>
        )}

        {/* Guests List */}
        {filteredGuests.length === 0 ? (
          searchQuery ? (
            <div className="bg-white rounded-xl border border-gray-200 p-12 text-center">
              <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No guests found</h3>
              <p className="text-gray-600 mb-6">No guests match your search criteria. Try a different search term.</p>
              <button onClick={() => setSearchQuery('')} className="text-blue-600 hover:text-blue-700 font-medium">
                Clear search
              </button>
            </div>
          ) : guests.length === 0 ? (
            <div className="bg-white rounded-xl border border-gray-200 p-12 text-center">
              <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No guests yet</h3>
              <p className="text-gray-600 mb-6">Start by adding your first guest to manage rental bookings</p>
              <button
                onClick={() => setShowForm(true)}
                className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors mx-auto"
              >
                <Plus className="w-4 h-4" />
                <span>Add Your First Guest</span>
              </button>
            </div>
          ) : (
            <div className="bg-white rounded-xl border border-gray-200 p-12 text-center">
              <User className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No guests yet</h3>
              <p className="text-gray-600 mb-6">Start by adding your first guest to manage rental bookings</p>
              <button
                onClick={() => setShowForm(true)}
                className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors mx-auto"
              >
                <Plus className="w-4 h-4" />
                <span>Add Your First Guest</span>
              </button>
            </div>
          )
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {paginatedGuests.map((guest: IGuest) => (
                <div key={guest._id} className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow relative">
                  {/* Selection Checkbox */}
                  <div className="absolute top-4 left-4">
                    <input
                      type="checkbox"
                      checked={isGuestSelected(guest)}
                      onChange={(e) => handleGuestSelect(guest, e.target.checked)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                  </div>

                  {/* Guest Header */}
                  <div className="flex items-start justify-between mb-4 ml-8">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        {guest.isForeigner ? <Globe className="w-6 h-6 text-blue-600" /> : <MapPin className="w-6 h-6 text-blue-600" />}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {guest.firstName} {guest.lastName}
                        </h3>
                        <p className="text-sm text-gray-600">{guest.relationshipWithOwner}</p>
                      </div>
                    </div>
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${guest.isForeigner ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}
                    >
                      {guest.isForeigner ? 'Foreign' : 'Local'}
                    </span>
                  </div>

                  {/* Guest Details */}
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Mail className="w-4 h-4" />
                      <span>{guest.email}</span>
                    </div>

                    {guest.phoneNumber && (
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <Phone className="w-4 h-4" />
                        <span>{guest.phoneNumber}</span>
                      </div>
                    )}

                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <FileText className="w-4 h-4" />
                      <span>
                        {guest.documentType}: {guest.documentNumber}
                      </span>
                    </div>

                    {guest.isForeigner && guest.nationality && (
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <Globe className="w-4 h-4" />
                        <span>{guest.nationality}</span>
                      </div>
                    )}

                    {!guest.isForeigner && guest.cityOfResidency && (
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <MapPin className="w-4 h-4" />
                        <span>{guest.cityOfResidency}</span>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-4 pt-4 border-t border-gray-100 flex justify-between items-center">
                    <p className="text-xs text-gray-500">Born: {new Date(guest.dateOfBirth).toLocaleDateString()}</p>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleEditGuest(guest)}
                        className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Edit guest"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => setDeleteConfirmId(guest._id)}
                        className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                        title="Delete guest"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center mt-8">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>

                  {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`px-3 py-2 text-sm font-medium rounded-md ${
                        page === currentPage
                          ? 'text-blue-600 bg-blue-50 border border-blue-300'
                          : 'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  ))}

                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        )}

        {/* Delete Confirmation Modal */}
        {deleteConfirmId && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Delete Guest</h3>
              <p className="text-gray-600 mb-6">Are you sure you want to delete this guest? This action cannot be undone.</p>
              <div className="flex justify-end space-x-4">
                <button
                  onClick={() => setDeleteConfirmId(null)}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => handleDeleteGuest(deleteConfirmId)}
                  disabled={isDeleting}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Bulk Delete Confirmation Modal */}
        {showBulkDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Guests</h3>
              <p className="text-gray-600 mb-4">Are you sure you want to delete {selectedGuests.length} guests? This action cannot be undone.</p>
              <div className="flex justify-end space-x-3">
                <button onClick={cancelBulkDelete} className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                  Cancel
                </button>
                <button
                  onClick={confirmBulkDelete}
                  disabled={isBulkDeleting}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
                >
                  Delete {selectedGuests.length} Guests
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

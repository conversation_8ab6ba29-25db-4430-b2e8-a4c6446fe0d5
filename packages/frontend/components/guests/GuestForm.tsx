import { useState, useEffect } from 'react';
import { ArrowLeft, User, Globe, MapPin } from 'lucide-react';
import { useGuests, Guest } from '../../hooks/useGuests';
import { DocumentType, IGuestCreateData, IGuestUpdateData } from '@alom-rentals/shared';

interface GuestFormProps {
  guest?: Guest | null;
  onCancel: () => void;
  onSuccess: (guest?: Guest) => void;
}

export function GuestForm({ guest, onCancel, onSuccess }: GuestFormProps) {
  const { createGuest, updateGuest, isCreating, isUpdating } = useGuests();
  const isEditing = !!guest;
  const [formData, setFormData] = useState<IGuestCreateData>({
    firstName: '',
    lastName: '',
    dateOfBirth: new Date(),
    email: '',
    documentType: 'National ID' as DocumentType,
    documentNumber: '',
    phoneNumber: '',
    relationshipWithOwner: 'Guest',
    isForeigner: false,
    nationality: '',
    countryOfOrigin: '',
    nextDestination: '',
    cityOfResidency: '',
    cityOfBirth: '',
    tripMotivation: '',
  });
  const [error, setError] = useState('');

  // Populate form data when editing
  useEffect(() => {
    if (guest) {
      setFormData({
        firstName: guest.firstName,
        lastName: guest.lastName,
        dateOfBirth: new Date(guest.dateOfBirth),
        email: guest.email,
        documentType: guest.documentType,
        documentNumber: guest.documentNumber,
        phoneNumber: guest.phoneNumber || '',
        relationshipWithOwner: guest.relationshipWithOwner,
        isForeigner: guest.isForeigner,
        nationality: guest.nationality || '',
        countryOfOrigin: guest.countryOfOrigin || '',
        nextDestination: guest.nextDestination || '',
        cityOfResidency: guest.cityOfResidency || '',
        cityOfBirth: guest.cityOfBirth || '',
        tripMotivation: guest.tripMotivation || '',
      });
    }
  }, [guest]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    // Validate required fields
    if (!formData.firstName || !formData.lastName || !formData.email || !formData.documentNumber) {
      setError('Please fill in all required fields');
      return;
    }

    // Validate foreigner fields
    if (formData.isForeigner && (!formData.nationality || !formData.countryOfOrigin)) {
      setError('Nationality and country of origin are required for foreign guests');
      return;
    }

    let result;
    if (isEditing && guest) {
      // Update existing guest
      const updateData: IGuestUpdateData = { ...formData };
      result = await updateGuest(guest._id, updateData);
      if (result.error) {
        setError(result.error.message || 'Failed to update guest');
      } else if (result.data) {
        onSuccess(result.data);
      }
    } else {
      // Create new guest
      result = await createGuest(formData);
      if (result.error) {
        setError(result.error.message || 'Failed to create guest');
      } else if (result.data) {
        onSuccess(result.data);
      }
    }
  };

  const handleInputChange = (field: keyof IGuestCreateData, value: string | boolean | Date) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev) => ({ ...prev, dateOfBirth: new Date(e.target.value) }));
  };

  const formatDateForInput = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <button onClick={onCancel} className="flex items-center text-gray-600 hover:text-gray-900 mb-4">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Guests
          </button>
          <h1 className="text-3xl font-bold text-gray-900">{isEditing ? 'Edit Guest' : 'Create New Guest'}</h1>
          <p className="text-gray-600 mt-2">{isEditing ? 'Update guest information' : 'Add guest information for rental bookings'}</p>
        </div>

        {error && <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">{error}</div>}

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <div className="flex items-center mb-6">
              <User className="w-5 h-5 text-blue-600 mr-2" />
              <h2 className="text-xl font-semibold text-gray-900">Basic Information</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                <input
                  type="text"
                  required
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter first name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                <input
                  type="text"
                  required
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter last name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Date of Birth *</label>
                <input
                  type="date"
                  required
                  value={formatDateForInput(formData.dateOfBirth)}
                  onChange={handleDateChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                <input
                  type="email"
                  required
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                <input
                  type="tel"
                  value={formData.phoneNumber || ''}
                  onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="+****************"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Relationship with Owner</label>
                <input
                  type="text"
                  value={formData.relationshipWithOwner}
                  onChange={(e) => handleInputChange('relationshipWithOwner', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Guest"
                />
              </div>
            </div>
          </div>

          {/* Document Information */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Document Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Document Type *</label>
                <select
                  required
                  value={formData.documentType}
                  onChange={(e) => handleInputChange('documentType', e.target.value as DocumentType)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="National ID">National ID</option>
                  <option value="Visa">Visa</option>
                  <option value="Passport">Passport</option>
                  <option value="Foreigner ID">Foreigner ID</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Document Number *</label>
                <input
                  type="text"
                  required
                  value={formData.documentNumber}
                  onChange={(e) => handleInputChange('documentNumber', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter document number"
                />
              </div>
            </div>
          </div>

          {/* Guest Type Selection */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Guest Type</h2>

            <div className="flex items-center space-x-6 mb-6">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="guestType"
                  checked={!formData.isForeigner}
                  onChange={() => handleInputChange('isForeigner', false)}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">Local Guest</span>
              </label>
              <label className="flex items-center">
                <input type="radio" name="guestType" checked={formData.isForeigner} onChange={() => handleInputChange('isForeigner', true)} className="mr-2" />
                <span className="text-sm font-medium text-gray-700">Foreign Guest</span>
              </label>
            </div>

            {formData.isForeigner ? (
              /* Foreign Guest Fields */
              <div className="space-y-4">
                <div className="flex items-center mb-4">
                  <Globe className="w-5 h-5 text-green-600 mr-2" />
                  <h3 className="text-lg font-medium text-gray-900">Foreign Guest Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Nationality *</label>
                    <input
                      type="text"
                      required={formData.isForeigner}
                      value={formData.nationality}
                      onChange={(e) => handleInputChange('nationality', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter nationality"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Country of Origin *</label>
                    <input
                      type="text"
                      required={formData.isForeigner}
                      value={formData.countryOfOrigin}
                      onChange={(e) => handleInputChange('countryOfOrigin', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter country of origin"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Next Destination</label>
                    <input
                      type="text"
                      value={formData.nextDestination}
                      onChange={(e) => handleInputChange('nextDestination', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter next destination"
                    />
                  </div>
                </div>
              </div>
            ) : (
              /* Local Guest Fields */
              <div className="space-y-4">
                <div className="flex items-center mb-4">
                  <MapPin className="w-5 h-5 text-blue-600 mr-2" />
                  <h3 className="text-lg font-medium text-gray-900">Local Guest Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">City of Residency</label>
                    <input
                      type="text"
                      value={formData.cityOfResidency || ''}
                      onChange={(e) => handleInputChange('cityOfResidency', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter city of residency"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">City of Birth</label>
                    <input
                      type="text"
                      value={formData.cityOfBirth}
                      onChange={(e) => handleInputChange('cityOfBirth', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter city of birth"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Trip Motivation</label>
                    <textarea
                      value={formData.tripMotivation}
                      onChange={(e) => handleInputChange('tripMotivation', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Enter trip motivation"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4">
            <button type="button" onClick={onCancel} className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
              Cancel
            </button>
            <button
              type="submit"
              disabled={isCreating || isUpdating}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isCreating || isUpdating ? (isEditing ? 'Updating...' : 'Creating...') : isEditing ? 'Update Guest' : 'Create Guest'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

import { NavLink, useNavigate } from 'react-router-dom';
import { Home, Building2, Users, UserCheck, Calendar, CreditCard, TrendingUp, Settings, LogOut, DollarSign, Shield } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { CustomerSelectorCompact } from '../operator/CustomerSelector';
import { useCustomerContext } from '../../hooks/useCustomerUtils';

export function Sidebar() {
  const { signOut, user } = useAuth();
  const navigate = useNavigate();
  const { selectedCustomer } = useCustomerContext();

  // Define menu items based on user role
  const getMenuItems = () => {
    const baseItems = [
      { path: '/dashboard', label: 'Dashboard', icon: Home },
      { path: '/properties', label: 'Properties', icon: Building2 },
      { path: '/transactions', label: 'Transactions', icon: DollarSign },
    ];

    // Role-specific menu items
    if (user?.role === 'ADMIN') {
      return [
        ...baseItems,
        { path: '/partners', label: 'Partners', icon: Users },
        { path: '/guests', label: 'Guests', icon: UserCheck },
        { path: '/currencies', label: 'Currencies', icon: DollarSign },
        { path: '/rentals', label: 'Rentals', icon: Calendar },
        { path: '/recurrent-costs', label: 'Recurrent Costs', icon: CreditCard },
        { path: '/personal-finance', label: 'Personal Finance', icon: TrendingUp },
        { path: '/admin', label: 'Admin Dashboard', icon: Shield },
        { path: '/settings', label: 'Settings', icon: Settings },
      ];
    }

    if (user?.role === 'OPERATOR') {
      return [
        ...baseItems,
        { path: '/guests', label: 'Guests', icon: UserCheck },
        { path: '/rentals', label: 'Rentals', icon: Calendar },
        { path: '/settings', label: 'Settings', icon: Settings },
      ];
    }

    if (user?.role === 'CUSTOMER') {
      return [
        ...baseItems,
        { path: '/partners', label: 'Partners', icon: Users },
        { path: '/guests', label: 'Guests', icon: UserCheck },
        { path: '/currencies', label: 'Currencies', icon: DollarSign },
        { path: '/rentals', label: 'Rentals', icon: Calendar },
        { path: '/recurrent-costs', label: 'Recurrent Costs', icon: CreditCard },
        { path: '/personal-finance', label: 'Personal Finance', icon: TrendingUp },
        { path: '/settings', label: 'Settings', icon: Settings },
        
      ];
    }

    return baseItems;
  };

  const menuItems = getMenuItems();

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col" data-testid="sidebar">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <Building2 className="w-5 h-5 text-white" />
          </div>
          <h1 className="text-xl font-bold text-gray-900">PropertyFin</h1>
        </div>

        {/* Customer selector for operators */}
        {user?.role === 'OPERATOR' && (
          <div className="mt-4">
            <CustomerSelectorCompact className="w-full" data-testid="customer-selector" />
            {!selectedCustomer && <p className="text-xs text-amber-600 mt-1">Please select a customer to manage their data</p>}
          </div>
        )}

        {/* User info */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-sm font-medium text-gray-900">{user?.fullName}</div>
          <div className="text-xs text-gray-500">{user?.email}</div>
          <div className="text-xs text-gray-500 capitalize">{user?.role?.toLowerCase()}</div>
        </div>
      </div>

      <nav className="flex-1 p-4 space-y-2">
        {menuItems.map((item) => {
          const Icon = item.icon;
          const isDisabled = user?.role === 'OPERATOR' && !selectedCustomer && item.path !== '/settings';

          if (isDisabled) {
            return (
              <div
                key={item.path}
                className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-400 cursor-not-allowed"
                title="Select a customer first"
              >
                <Icon className="w-5 h-5" />
                <span className="font-medium">{item.label}</span>
              </div>
            );
          }

          return (
            <NavLink
              key={item.path}
              to={item.path}
              data-testid={`nav-${item.label.toLowerCase().replace(/\s+/g, '-')}`}
              className={({ isActive }) =>
                `w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  isActive ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`
              }
            >
              <Icon className="w-5 h-5" />
              <span className="font-medium">{item.label}</span>
            </NavLink>
          );
        })}
      </nav>

      <div className="p-4 border-t border-gray-200">
        <button
          onClick={() => {
            signOut();
            navigate('/auth');
          }}
          data-testid="logout-btn"
          className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-50 hover:text-gray-900 transition-colors"
        >
          <LogOut className="w-5 h-5" />
          <span className="font-medium">Sign Out</span>
        </button>
      </div>
    </div>
  );
}

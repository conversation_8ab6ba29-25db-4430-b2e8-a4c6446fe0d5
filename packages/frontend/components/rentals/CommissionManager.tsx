import React from 'react';
import { Controller, useFieldArray, Control, FieldErrors } from 'react-hook-form';
import { IShortTermRentalCreateData } from '@alom-rentals/shared';
import { usePartners } from '../../hooks/usePartners';
import { DollarSign, Percent, Users, Plus, Trash2 } from 'lucide-react';

interface CommissionManagerProps {
  control: Control<IShortTermRentalCreateData>;
  rentalAmount: number;
  errors: FieldErrors<IShortTermRentalCreateData>;
}

export const CommissionManager: React.FC<CommissionManagerProps> = ({ control, rentalAmount, errors }) => {
  const { partners, isLoading: partnersLoading } = usePartners();
  const { fields, append, remove } = useFieldArray({
    control,
    name: 'commissions',
  });

  const addCommission = () => {
    append({
      amount: 0,
      type: 'fixed',
      receiverType: 'Partner',
      receiverId: '',
    });
  };

  const calculatePercentageAmount = (percentage: number) => {
    return (rentalAmount * percentage) / 100;
  };

  const getTotalCommissions = () => {
    return fields.reduce((total, _, index) => {
      const commission = control._getWatch(`commissions.${index}`);
      if (commission?.type === 'percentage') {
        return total + calculatePercentageAmount(commission.amount || 0);
      }
      return total + (commission?.amount || 0);
    }, 0);
  };

  if (partnersLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 flex items-center">
          <Users className="w-5 h-5 mr-2 text-blue-600" />
          Commissions (Optional)
        </h3>
        <button type="button" onClick={addCommission} className="flex items-center px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
          <Plus className="w-4 h-4 mr-1" />
          Add Commission
        </button>
      </div>

      {fields.length === 0 ? (
        <p className="text-sm text-gray-500 text-center py-4">No commissions added. Click "Add Commission" to add one.</p>
      ) : (
        <div className="space-y-4">
          {fields.map((field, index) => (
            <div key={field.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-sm font-medium text-gray-700">Commission #{index + 1}</h4>
                <button type="button" onClick={() => remove(index)} className="text-red-600 hover:text-red-800">
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Commission Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Type</label>
                  <Controller
                    name={`commissions.${index}.type`}
                    control={control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      >
                        <option value="fixed">Fixed Amount</option>
                        <option value="percentage">Percentage</option>
                      </select>
                    )}
                  />
                </div>

                {/* Commission Amount */}
                <div>
                  <Controller
                    name={`commissions.${index}.type`}
                    control={control}
                    render={({ field: typeField }) => (
                      <>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          {typeField.value === 'percentage' ? 'Percentage (%)' : 'Amount ($)'}
                        </label>
                        <div className="relative">
                          {typeField.value === 'percentage' ? (
                            <Percent className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          ) : (
                            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                          )}
                          <Controller
                            name={`commissions.${index}.amount`}
                            control={control}
                            render={({ field }) => (
                              <input
                                {...field}
                                type="number"
                                step="0.01"
                                min="0"
                                max={typeField.value === 'percentage' ? 100 : undefined}
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                placeholder={typeField.value === 'percentage' ? '0.00' : '0.00'}
                              />
                            )}
                          />
                        </div>
                      </>
                    )}
                  />
                </div>

                {/* Receiver Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Receiver Type</label>
                  <Controller
                    name={`commissions.${index}.receiverType`}
                    control={control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      >
                        <option value="Partner">Partner</option>
                        <option value="User">User</option>
                      </select>
                    )}
                  />
                </div>

                {/* Receiver */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Receiver</label>
                  <Controller
                    name={`commissions.${index}.receiverId`}
                    control={control}
                    render={({ field }) => (
                      <select
                        {...field}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      >
                        <option value="">Select receiver...</option>
                        {partners?.map((partner) => (
                          <option key={partner._id} value={partner._id}>
                            {partner.name} ({partner.email})
                          </option>
                        ))}
                      </select>
                    )}
                  />
                </div>
              </div>

              {/* Show calculated amount for percentage commissions */}
              <Controller
                name={`commissions.${index}.type`}
                control={control}
                render={({ field: typeField }) => (
                  <Controller
                    name={`commissions.${index}.amount`}
                    control={control}
                    render={({ field: amountField }) => {
                      if (typeField.value === 'percentage' && amountField.value > 0) {
                        const calculatedAmount = calculatePercentageAmount(amountField.value);
                        return <div className="mt-2 text-sm text-blue-600">Calculated amount: ${calculatedAmount.toFixed(2)}</div>;
                      }
                      return <></>;
                    }}
                  />
                )}
              />

              {errors?.commissions?.[index] && <div className="mt-2 text-sm text-red-600">{Object.values(errors.commissions[index]).join(', ')}</div>}
            </div>
          ))}

          {/* Commission Summary */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <div className="flex justify-between items-center text-sm">
              <span className="font-medium text-gray-700">Total Commissions:</span>
              <span className="font-medium text-blue-600">${getTotalCommissions().toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center text-sm mt-1">
              <span className="text-gray-600">Remaining after commissions:</span>
              <span className="text-gray-900">${(rentalAmount - getTotalCommissions()).toFixed(2)}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CommissionManager;

import React, { useState } from 'react';
import { <PERSON>, User<PERSON><PERSON><PERSON>, Trash2, Crown, User } from 'lucide-react';
import { GuestSelectionModal } from './GuestSelectionModal';
import { IGuest } from '@alom-rentals/shared';

interface RentalGuest {
  guestId?: string;
  guest?: {
    _id?: string;
    firstName: string;
    lastName: string;
    email: string;
    documentType: string;
    documentNumber: string;
    phoneNumber?: string;
  };
  isPrincipalContact: boolean;
}

interface RentalGuestManagerProps {
  guests: RentalGuest[];
  onGuestsChange: (guests: RentalGuest[]) => void;
  error?: string;
}

export const RentalGuestManager: React.FC<RentalGuestManagerProps> = ({ guests, onGuestsChange, error }) => {
  const [showGuestModal, setShowGuestModal] = useState(false);

  const addGuest = (guestData: RentalGuest) => {
    const newGuests = [...guests, guestData];
    onGuestsChange(newGuests);
  };

  const removeGuest = (index: number) => {
    const newGuests = guests.filter((_, i) => i !== index);

    // If we removed the principal contact, make the first remaining guest the principal
    if (newGuests.length > 0 && !newGuests.some((g) => g.isPrincipalContact)) {
      newGuests[0].isPrincipalContact = true;
    }

    onGuestsChange(newGuests);
  };

  const setPrincipalContact = (index: number) => {
    const newGuests = guests.map((guest, i) => ({
      ...guest,
      isPrincipalContact: i === index,
    }));
    onGuestsChange(newGuests);
  };

  const getGuestDisplayName = (guest: RentalGuest) => {
    if (guest.guestId && guest.guest) {
      return `${guest.guest.firstName} ${guest.guest.lastName}`;
    }
    if (guest.guest) {
      return `${guest.guest.firstName} ${guest.guest.lastName}`;
    }
    return 'Unknown Guest';
  };

  const getGuestEmail = (guest: RentalGuest) => {
    return guest.guest?.email || 'No email';
  };

  const getGuestDocument = (guest: RentalGuest) => {
    if (guest.guest?.documentType && guest.guest?.documentNumber) {
      return `${guest.guest.documentType}: ${guest.guest.documentNumber}`;
    }
    return 'No document info';
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-medium text-gray-900">Guests</h3>
          <span className="text-sm text-gray-500">
            ({guests.length} guest{guests.length !== 1 ? 's' : ''})
          </span>
        </div>
        <button
          type="button"
          onClick={() => setShowGuestModal(true)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 flex items-center gap-2"
        >
          <Users className="h-4 w-4" />
          Add Guest
        </button>
      </div>

      {/* Guest List */}
      {guests.length === 0 ? (
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
          <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500 mb-4">No guests added yet</p>
          <p className="text-sm text-gray-400 mb-4">At least one guest is required for the rental</p>
          <button type="button" onClick={() => setShowGuestModal(true)} className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
            Add First Guest
          </button>
        </div>
      ) : (
        <div className="space-y-3">
          {guests.map((guest, index) => (
            <div key={index} className={`border rounded-lg p-4 ${guest.isPrincipalContact ? 'border-blue-300 bg-blue-50' : 'border-gray-200 bg-white'}`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${guest.isPrincipalContact ? 'bg-blue-100' : 'bg-gray-100'}`}>
                    {guest.isPrincipalContact ? <Crown className="h-5 w-5 text-blue-600" /> : <User className="h-5 w-5 text-gray-600" />}
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-gray-900">{getGuestDisplayName(guest)}</h4>
                      {guest.isPrincipalContact && <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Principal Contact</span>}
                    </div>
                    <p className="text-sm text-gray-600">{getGuestEmail(guest)}</p>
                    <p className="text-xs text-gray-500">{getGuestDocument(guest)}</p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  {!guest.isPrincipalContact && (
                    <button
                      type="button"
                      onClick={() => setPrincipalContact(index)}
                      className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
                      title="Set as principal contact"
                    >
                      <UserCheck className="h-4 w-4" />
                    </button>
                  )}
                  <button
                    type="button"
                    onClick={() => removeGuest(index)}
                    className="px-3 py-1 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
                    title="Remove guest"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Error Message */}
      {error && (
        <p className="text-sm text-red-600 flex items-center gap-2">
          <span className="w-4 h-4 rounded-full bg-red-100 flex items-center justify-center">!</span>
          {error}
        </p>
      )}

      {/* Requirements Info */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Guest Requirements:</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          <li className="flex items-center gap-2">
            <span className={`w-2 h-2 rounded-full ${guests.length >= 1 ? 'bg-green-500' : 'bg-gray-300'}`}></span>
            At least one guest is required
          </li>
          <li className="flex items-center gap-2">
            <span className={`w-2 h-2 rounded-full ${guests.some((g) => g.isPrincipalContact) ? 'bg-green-500' : 'bg-gray-300'}`}></span>
            One guest must be designated as principal contact
          </li>
        </ul>
      </div>

      {/* Guest Selection Modal */}
      <GuestSelectionModal
        isOpen={showGuestModal}
        onClose={() => setShowGuestModal(false)}
        onSelectGuest={addGuest}
        existingGuests={
          guests as Array<{
            guestId?: string;
            guest?: IGuest;
            isPrincipalContact: boolean;
          }>
        }
      />
    </div>
  );
};

export default RentalGuestManager;

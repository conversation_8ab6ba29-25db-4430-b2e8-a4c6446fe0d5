import React, { useState } from 'react';
import { Search, Plus, X, User } from 'lucide-react';
import { useGuests } from '../../hooks/useGuests';
import { IGuest } from '@alom-rentals/shared';
import { GuestForm } from '../guests/GuestForm';

interface GuestSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectGuest: (guest: { guestId?: string; guest?: IGuest; isPrincipalContact: boolean }) => void;
  existingGuests?: Array<{
    guestId?: string;
    guest?: IGuest;
    isPrincipalContact: boolean;
  }>;
}

export const GuestSelectionModal: React.FC<GuestSelectionModalProps> = ({ isOpen, onClose, onSelectGuest, existingGuests = [] }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewGuestForm, setShowNewGuestForm] = useState(false);
  const { guests, isLoading: loading } = useGuests();

  // Filter guests based on search query
  const filteredGuests = searchQuery
    ? guests.filter(
        (guest: IGuest) =>
          guest.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          guest.lastName.toLowerCase().includes(searchQuery.toLowerCase()) ||
          guest.email.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : guests;

  // Exclude already selected guests
  const availableGuests = filteredGuests.filter((guest: IGuest) => !existingGuests.some((existing) => existing.guestId === guest._id));

  const handleSelectGuest = (guest: IGuest) => {
    const hasPrincipalContact = existingGuests.some((g) => g.isPrincipalContact);

    onSelectGuest({
      guestId: guest._id,
      isPrincipalContact: !hasPrincipalContact, // First guest becomes principal contact
    });

    onClose();
  };

  const handleNewGuestSuccess = (newGuest?: IGuest) => {
    if (!newGuest) return;

    const hasPrincipalContact = existingGuests.some((g) => g.isPrincipalContact);

    onSelectGuest({
      guest: newGuest,
      isPrincipalContact: !hasPrincipalContact, // First guest becomes principal contact
    });

    setShowNewGuestForm(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold text-gray-900">{showNewGuestForm ? 'Add New Guest' : 'Select Guest'}</h2>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600" aria-label="Close modal">
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {showNewGuestForm ? (
            <div>
              <button onClick={() => setShowNewGuestForm(false)} className="mb-4 text-blue-600 hover:text-blue-800 flex items-center gap-2">
                ← Back to guest selection
              </button>
              <div className="[&_.min-h-screen]:min-h-0 [&_.bg-gray-50]:bg-transparent [&_.py-8]:py-0">
                <GuestForm onSuccess={handleNewGuestSuccess} onCancel={() => setShowNewGuestForm(false)} />
              </div>
            </div>
          ) : (
            <div>
              {/* Search and Add New Button */}
              <div className="flex gap-4 mb-6">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search guests by name or email..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <button
                  onClick={() => setShowNewGuestForm(true)}
                  className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add New Guest
                </button>
              </div>

              {/* Guest List */}
              {loading ? (
                <div className="flex items-center justify-center py-8" role="status" aria-label="Loading guests">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : availableGuests.length === 0 ? (
                <div className="text-center py-8">
                  <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">{searchQuery ? 'No guests found matching your search.' : 'No guests available.'}</p>
                  <button onClick={() => setShowNewGuestForm(true)} className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Create New Guest
                  </button>
                </div>
              ) : (
                <div className="grid gap-4">
                  {availableGuests.map((guest: IGuest) => (
                    <div
                      key={guest._id}
                      className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all cursor-pointer"
                      onClick={() => handleSelectGuest(guest)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="font-medium text-gray-900">
                              {guest.firstName} {guest.lastName}
                            </h3>
                            <p className="text-sm text-gray-500">{guest.email}</p>
                            <p className="text-xs text-gray-400">
                              {guest.documentType}: {guest.documentNumber}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-500">{guest.phoneNumber || 'No phone'}</p>
                          <p className="text-xs text-gray-400">{guest.isForeigner ? 'Foreigner' : 'Local'}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {!showNewGuestForm && (
          <div className="flex justify-end gap-4 p-6 border-t bg-gray-50">
            <button onClick={onClose} className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50">
              Cancel
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default GuestSelectionModal;

import React from 'react';
import { Modal, Button } from '../ui';
import { IShortTermRental } from '@alom-rentals/shared';
import { formatDate, formatCurrency, RENTAL_STATUS_LABELS } from '../../utils';
import { getDaysBetween, getRentalStatusColorLight } from '../../utils/calendar';
import { MapPin, Users, Calendar, DollarSign, Edit2, CheckCircle, XCircle, Clock, Percent } from 'lucide-react';

export interface RentalDetailModalProps {
  rental: IShortTermRental | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (rental: IShortTermRental) => void;
}

export const RentalDetailModal: React.FC<RentalDetailModalProps> = ({ rental, isOpen, onClose, onEdit }) => {
  if (!rental) return null;

  const totalNights = getDaysBetween(new Date(rental.startDate), new Date(rental.endDate));
  const profit = rental.rentalAmount - rental.costAmount - (rental.discountAmount || 0);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-5 h-5 text-green-600" />;
      case 'ended':
        return <Clock className="w-5 h-5 text-blue-600" />;
      case 'cancelled':
        return <XCircle className="w-5 h-5 text-red-600" />;
      default:
        return <Clock className="w-5 h-5 text-gray-600" />;
    }
  };

  const footerContent = (
    <div className="flex justify-end space-x-4">
      <Button variant="secondary" onClick={onClose}>
        Close
      </Button>
      {onEdit && (
        <Button icon={Edit2} onClick={() => onEdit(rental)}>
          Edit Rental
        </Button>
      )}
    </div>
  );

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Rental Details" size="3xl" maxHeight={true} footer={footerContent}>
      <div className="space-y-6">
        {/* Header with Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon(rental.status)}
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{rental.property?.name}</h3>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRentalStatusColorLight(rental.status)}`}>
                {RENTAL_STATUS_LABELS[rental.status as keyof typeof RENTAL_STATUS_LABELS]}
              </span>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">Reference</div>
            <div className="font-mono text-sm">{rental.referenceNumber}</div>
          </div>
        </div>

        {/* Property Information */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <MapPin className="w-5 h-5 text-gray-600" />
            <h4 className="font-medium text-gray-900">Property</h4>
          </div>
          <div className="space-y-2">
            <div>
              <span className="text-sm font-medium text-gray-700">Name: </span>
              <span className="text-sm text-gray-900">{rental.property?.name}</span>
            </div>
            <div>
              <span className="text-sm font-medium text-gray-700">Address: </span>
              <span className="text-sm text-gray-900">{rental.property?.address}</span>
            </div>
          </div>
        </div>

        {/* Guest Information */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Users className="w-5 h-5 text-gray-600" />
            <h4 className="font-medium text-gray-900">Guests ({rental.guests.length})</h4>
          </div>
          <div className="space-y-3">
            {rental.guests.map((rentalGuest, index) => (
              <div key={index} className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-gray-900">
                    {rentalGuest.guest?.firstName} {rentalGuest.guest?.lastName}
                  </div>
                  <div className="text-sm text-gray-500">{rentalGuest.guest?.email}</div>
                </div>
                {rentalGuest.isPrincipalContact && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Principal Contact</span>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Rental Dates */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Calendar className="w-5 h-5 text-gray-600" />
            <h4 className="font-medium text-gray-900">Dates</h4>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm font-medium text-gray-700">Check-in</div>
              <div className="text-sm text-gray-900">{formatDate(rental.startDate)}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-700">Check-out</div>
              <div className="text-sm text-gray-900">{formatDate(rental.endDate)}</div>
            </div>
            <div className="col-span-2">
              <div className="text-sm font-medium text-gray-700">Duration</div>
              <div className="text-sm text-gray-900">{totalNights} nights</div>
            </div>
          </div>
        </div>

        {/* Financial Information */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-3">
            <DollarSign className="w-5 h-5 text-gray-600" />
            <h4 className="font-medium text-gray-900">Financial Details</h4>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-700">Rental Amount:</span>
              <span className="text-sm text-gray-900">{formatCurrency(rental.rentalAmount)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-700">Cost Amount:</span>
              <span className="text-sm text-gray-900">{formatCurrency(rental.costAmount)}</span>
            </div>
            {rental.discountAmount && rental.discountAmount > 0 && (
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-700">Discount:</span>
                <span className="text-sm text-red-600">-{formatCurrency(rental.discountAmount)}</span>
              </div>
            )}
            <div className="border-t border-gray-200 pt-2">
              <div className="flex justify-between">
                <span className="text-sm font-semibold text-gray-900">Net Profit:</span>
                <span className={`text-sm font-semibold ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>{formatCurrency(profit)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Status */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">Payment Status</h4>
              <div className="text-sm text-gray-600 mt-1">
                {rental.isPaid ? 'Paid' : 'Unpaid'}
                {rental.paidDate && <span className="ml-2">on {formatDate(rental.paidDate)}</span>}
              </div>
            </div>
            <div className={`w-3 h-3 rounded-full ${rental.isPaid ? 'bg-green-500' : 'bg-red-500'}`} />
          </div>
        </div>

        {/* Discount Reason */}
        {rental.discountReason && (
          <div className="bg-yellow-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Discount Reason</h4>
            <p className="text-sm text-gray-700">{rental.discountReason}</p>
          </div>
        )}

        {/* Commissions */}
        {rental.commissions && rental.commissions.length > 0 && (
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-3">
              <Percent className="w-5 h-5 text-blue-600" />
              <h4 className="font-medium text-gray-900">Commissions ({rental.commissions.length})</h4>
            </div>
            <div className="space-y-3">
              {rental.commissions.map((commission, index) => (
                <div key={commission._id || index} className="bg-white border border-blue-200 rounded p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-700">Commission #{index + 1}</span>
                      <span
                        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          commission.status === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {commission.status === 'paid' ? 'Paid' : 'Pending'}
                      </span>
                    </div>
                    <div className="text-sm font-medium text-gray-900">
                      {commission.type === 'percentage'
                        ? `${commission.amount}% (${formatCurrency((rental.rentalAmount * commission.amount) / 100)})`
                        : formatCurrency(commission.amount)}
                    </div>
                  </div>
                  <div className="text-sm text-gray-600">
                    <div>Type: {commission.type === 'percentage' ? 'Percentage' : 'Fixed Amount'}</div>
                    <div>
                      Receiver: {commission.receiverType} -{' '}
                      {commission.receiver && typeof commission.receiver === 'object'
                        ? (commission.receiver as { name?: string; fullName?: string }).name ||
                          (commission.receiver as { name?: string; fullName?: string }).fullName ||
                          'Unknown'
                        : 'Unknown'}
                    </div>
                    {commission.paidDate && <div>Paid on: {formatDate(commission.paidDate)}</div>}
                  </div>
                </div>
              ))}

              {/* Commission Summary */}
              <div className="bg-white border border-blue-200 rounded p-3 border-t-2 border-t-blue-400">
                <div className="flex justify-between items-center text-sm">
                  <span className="font-medium text-gray-700">Total Commissions:</span>
                  <span className="font-medium text-blue-600">
                    {formatCurrency(
                      rental.commissions.reduce((total, commission) => {
                        if (commission.type === 'percentage') {
                          return total + (rental.rentalAmount * commission.amount) / 100;
                        }
                        return total + commission.amount;
                      }, 0)
                    )}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default RentalDetailModal;

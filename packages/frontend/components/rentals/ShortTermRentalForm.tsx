/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { IShortTermRentalCreateData, IShortTermRental, shortTermRentalCreateSchema } from '@alom-rentals/shared';
import { useProperties } from '../../hooks/useProperties';
import { useShortTermRentals } from '../../hooks/useShortTermRentals';
import { useShortTermRentalCost } from '../../hooks/useShortTermRentalCost';
import { Calendar, DollarSign, AlertCircle, Calculator, Percent } from 'lucide-react';
import { RentalGuestManager } from './RentalGuestManager';
import { CommissionManager } from './CommissionManager';

interface ShortTermRentalFormProps {
  rental?: IShortTermRental;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const ShortTermRentalForm: React.FC<ShortTermRentalFormProps> = ({ rental, onSuccess, onCancel }) => {
  const [selectedPropertyId, setSelectedPropertyId] = useState<string>('');
  const [calculatedCost, setCalculatedCost] = useState<number>(0);

  const { properties, isLoading: propertiesLoading } = useProperties();
  const { createRental, updateRental, isCreating, isUpdating, createError, updateError } = useShortTermRentals();

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    trigger,
    formState: { errors },
    reset,
  } = useForm<IShortTermRentalCreateData>({
    resolver: zodResolver(shortTermRentalCreateSchema) as any, // TODO: Fix type mismatch
    defaultValues: {
      property: typeof rental?.property === 'string' ? rental.property : rental?.property?._id || '',
      startDate: rental?.startDate ? new Date(rental.startDate).toISOString().split('T')[0] : '',
      endDate: rental?.endDate ? new Date(rental.endDate).toISOString().split('T')[0] : '',
      rentalAmount: rental?.rentalAmount || 0,
      costAmount: rental?.costAmount || 0,
      status: rental?.status || 'active',
      isPaid: rental?.isPaid || false,
      discountAmount: rental?.discountAmount || 0,
      discountReason: rental?.discountReason || '',
      guests: rental?.guests || [],
      commissions: rental?.commissions || [],
    },
  });

  const watchedPropertyId = watch('property');
  const watchedGuests = watch('guests');
  const watchedRentalAmount = watch('rentalAmount');
  const watchedCostAmount = watch('costAmount');
  const watchedDiscountAmount = watch('discountAmount');

  // Calculate cost when property is selected - always call hook but control with enabled
  const costQuery = useShortTermRentalCost(selectedPropertyId || '');

  // Update selected property and cost when form value changes
  useEffect(() => {
    if (watchedPropertyId) {
      setSelectedPropertyId(watchedPropertyId);
    }
  }, [watchedPropertyId]);

  // Update calculated cost when cost query data changes
  useEffect(() => {
    if (costQuery.isSuccess && costQuery.data) {
      setCalculatedCost(costQuery.data.costAmount);
      setValue('costAmount', costQuery.data.costAmount);
    }
  }, [costQuery.isSuccess, costQuery.data, setValue]);

  const onSubmit = (data: IShortTermRentalCreateData) => {
    if (rental) {
      // Update existing rental
      updateRental(
        { id: rental._id, data },
        {
          onSuccess: () => {
            onSuccess?.();
          },
        }
      );
    } else {
      // Create new rental
      createRental(data, {
        onSuccess: () => {
          reset();
          onSuccess?.();
        },
      });
    }
  };

  const handleGuestsChange = (guests: Array<{ guestId?: string; guest?: any; isPrincipalContact: boolean }>) => {
    setValue('guests', guests, { shouldValidate: true, shouldDirty: true });
    // Trigger validation to ensure form state updates
    trigger('guests');
  };

  // Calculate profit for display
  const calculateProfit = () => {
    const rentalAmount = watchedRentalAmount || 0;
    const costAmount = watchedCostAmount || 0;
    const discountAmount = watchedDiscountAmount || 0;
    return rentalAmount - costAmount - discountAmount;
  };

  const maxDiscount = Math.max(0, (watchedRentalAmount || 0) - (watchedCostAmount || 0));

  if (propertiesLoading) {
    return (
      <div className="flex items-center justify-center p-8" role="status" aria-label="Loading properties">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center gap-2 mb-6">
        <Calendar className="h-6 w-6 text-blue-600" />
        <h2 className="text-2xl font-bold text-gray-900">{rental ? 'Edit' : 'Create'} Short-Term Rental</h2>
      </div>

      {(createError || updateError) && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md flex items-center gap-2">
          <AlertCircle className="h-5 w-5 text-red-500" />
          <span className="text-red-700">
            {((createError || updateError) as any)?.response?.data?.error ||
              (createError || updateError)?.message ||
              `Failed to ${rental ? 'update' : 'create'} rental`}
          </span>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Property Selection */}
        <div>
          <label htmlFor="property" className="block text-sm font-medium text-gray-700 mb-2">
            Property *
          </label>
          <Controller
            name="property"
            control={control}
            render={({ field }) => (
              <select
                {...field}
                id="property"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select a property</option>
                {properties.map((property) => (
                  <option key={property._id} value={property._id}>
                    {property.name} - {property.address}
                  </option>
                ))}
              </select>
            )}
          />
          {errors.property && <p className="mt-1 text-sm text-red-600">{errors.property.message}</p>}
        </div>

        {/* Date Range */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="startDate" className="block text-sm font-medium text-gray-700 mb-2">
              Start Date *
            </label>
            <Controller
              name="startDate"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  id="startDate"
                  type="date"
                  value={field.value instanceof Date ? field.value.toISOString().split('T')[0] : field.value}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              )}
            />
            {errors.startDate && <p className="mt-1 text-sm text-red-600">{errors.startDate.message}</p>}
          </div>

          <div>
            <label htmlFor="endDate" className="block text-sm font-medium text-gray-700 mb-2">
              End Date *
            </label>
            <Controller
              name="endDate"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  id="endDate"
                  type="date"
                  value={field.value instanceof Date ? field.value.toISOString().split('T')[0] : field.value}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              )}
            />
            {errors.endDate && <p className="mt-1 text-sm text-red-600">{errors.endDate.message}</p>}
          </div>
        </div>

        {/* Amounts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="rentalAmount" className="block text-sm font-medium text-gray-700 mb-2">
              Rental Amount *
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Controller
                name="rentalAmount"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    id="rentalAmount"
                    type="number"
                    step="0.01"
                    min="0"
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0.00"
                  />
                )}
              />
            </div>
            {errors.rentalAmount && <p className="mt-1 text-sm text-red-600">{errors.rentalAmount.message}</p>}
          </div>

          <div>
            <label htmlFor="costAmount" className="block text-sm font-medium text-gray-700 mb-2">
              Cost Amount *
              {selectedPropertyId && (
                <button type="button" onClick={() => setValue('costAmount', calculatedCost)} className="ml-2 text-xs text-blue-600 hover:text-blue-800">
                  <Calculator className="inline h-3 w-3 mr-1" />
                  Auto-calculate
                </button>
              )}
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Controller
                name="costAmount"
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    id="costAmount"
                    type="number"
                    step="0.01"
                    min="0"
                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="0.00"
                  />
                )}
              />
            </div>
            {errors.costAmount && <p className="mt-1 text-sm text-red-600">{errors.costAmount.message}</p>}
          </div>
        </div>

        {/* Discount Section */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <Percent className="w-5 h-5 mr-2 text-yellow-600" />
            Discount (Optional)
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="discountAmount" className="block text-sm font-medium text-gray-700 mb-2">
                Discount Amount
                <span className="text-xs text-gray-500 ml-2">(Max: ${maxDiscount.toFixed(2)})</span>
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Controller
                  name="discountAmount"
                  control={control}
                  render={({ field }) => (
                    <input
                      {...field}
                      id="discountAmount"
                      type="number"
                      step="0.01"
                      min="0"
                      max={maxDiscount}
                      onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="0.00"
                    />
                  )}
                />
              </div>
              {errors.discountAmount && <p className="mt-1 text-sm text-red-600">{errors.discountAmount.message}</p>}
            </div>

            <div>
              <label htmlFor="discountReason" className="block text-sm font-medium text-gray-700 mb-2">
                Discount Reason
              </label>
              <Controller
                name="discountReason"
                control={control}
                render={({ field }) => (
                  <textarea
                    {...field}
                    id="discountReason"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Reason for discount..."
                  />
                )}
              />
              {errors.discountReason && <p className="mt-1 text-sm text-red-600">{errors.discountReason.message}</p>}
            </div>
          </div>

          {/* Profit Calculation Display */}
          <div className="mt-4 p-3 bg-white rounded border">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Profit Calculation</h4>
            <div className="space-y-1 text-sm">
              <div className="flex justify-between">
                <span>Rental Amount:</span>
                <span>${(watchedRentalAmount || 0).toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Cost Amount:</span>
                <span>-${(watchedCostAmount || 0).toFixed(2)}</span>
              </div>
              {(watchedDiscountAmount || 0) > 0 && (
                <div className="flex justify-between text-yellow-600">
                  <span>Discount:</span>
                  <span>-${(watchedDiscountAmount || 0).toFixed(2)}</span>
                </div>
              )}
              <div className="border-t pt-1 flex justify-between font-medium">
                <span>Final Profit:</span>
                <span className={calculateProfit() >= 0 ? 'text-green-600' : 'text-red-600'}>${calculateProfit().toFixed(2)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Status and Payment */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <select
                  {...field}
                  id="status"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="active">Active</option>
                  <option value="ended">Ended</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              )}
            />
          </div>

          <div className="flex items-center">
            <Controller
              name="isPaid"
              control={control}
              render={({ field }) => (
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={field.value}
                    onChange={field.onChange}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">Rental has been paid</span>
                </label>
              )}
            />
          </div>
        </div>

        {/* Guests Section */}
        <RentalGuestManager guests={watchedGuests || []} onGuestsChange={handleGuestsChange} error={errors.guests?.message} />

        {/* Commissions Section */}
        <CommissionManager control={control} rentalAmount={watchedRentalAmount || 0} errors={errors} />

        {/* Form Actions */}
        <div className="flex justify-end gap-4 pt-6 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isCreating || isUpdating}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isCreating || isUpdating ? `${rental ? 'Updating' : 'Creating'}...` : `${rental ? 'Update' : 'Create'} Rental`}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ShortTermRentalForm;

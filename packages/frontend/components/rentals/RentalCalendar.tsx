import React, { useState, useMemo } from 'react';
import { Calendar, Select } from '../ui';
import { useShortTermRentals } from '../../hooks/useShortTermRentals';
import { useProperties } from '../../hooks/useProperties';
import { IShortTermRental } from '@alom-rentals/shared';
import { getRentalsForDate, getRentalStatusColorLight, isSameDay } from '../../utils/calendar';
import { formatCurrency } from '../../utils';
import { MapPin, Users, Calendar as CalendarIcon } from 'lucide-react';

export interface RentalCalendarProps {
  onRentalClick?: (rental: IShortTermRental) => void;
  className?: string;
}

export const RentalCalendar: React.FC<RentalCalendarProps> = ({ onRentalClick, className = '' }) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedPropertyId, setSelectedPropertyId] = useState('');

  const { rentals, loading: rentalsLoading } = useShortTermRentals();
  const { properties, isLoading: propertiesLoading } = useProperties();

  // Filter rentals by selected property
  const filteredRentals = useMemo(() => {
    if (!rentals) return [];
    if (!selectedPropertyId) return rentals;
    return rentals.filter((rental) => rental.propertyId === selectedPropertyId);
  }, [rentals, selectedPropertyId]);

  // Property options for filter
  const propertyOptions = useMemo(() => {
    const options = [{ value: '', label: 'All Properties' }];
    if (properties) {
      options.push(
        ...properties.map((property) => ({
          value: property._id,
          label: property.name,
        }))
      );
    }
    return options;
  }, [properties]);

  const renderCalendarDay = (date: Date, isCurrentMonth: boolean, isToday: boolean) => {
    const dayRentals = getRentalsForDate(date, filteredRentals);

    return (
      <div className="w-full h-full flex flex-col">
        {/* Day number */}
        <div
          className={`
          flex items-center justify-center text-xs font-medium h-6
          ${isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}
          ${isToday ? 'bg-blue-600 text-white rounded-full w-6 mx-auto' : ''}
        `}
        >
          {date.getDate()}
        </div>

        {/* Rentals for this day */}
        <div className="flex-1 p-1 space-y-1 overflow-hidden">
          {dayRentals.slice(0, 3).map((rental, index) => {
            const isStartDate = isSameDay(date, new Date(rental.startDate));
            const isEndDate = isSameDay(date, new Date(rental.endDate));
            const principalGuest = rental.guests.find((g) => g.isPrincipalContact);

            return (
              <div
                key={`${rental._id}-${index}`}
                className={`
                  text-xs p-1 rounded border cursor-pointer transition-all hover:shadow-sm
                  ${getRentalStatusColorLight(rental.status)}
                  ${!isCurrentMonth ? 'opacity-50' : ''}
                `}
                onClick={() => onRentalClick?.(rental)}
                title={`${rental.property?.name} - ${principalGuest?.guest?.firstName} ${principalGuest?.guest?.lastName}`}
              >
                <div className="flex items-center space-x-1 mb-1">
                  <MapPin className="w-3 h-3 flex-shrink-0" />
                  <span className="truncate font-medium">{rental.property?.name}</span>
                </div>

                {principalGuest && (
                  <div className="flex items-center space-x-1 mb-1">
                    <Users className="w-3 h-3 flex-shrink-0" />
                    <span className="truncate">
                      {principalGuest.guest?.firstName} {principalGuest.guest?.lastName}
                    </span>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-xs font-medium">{formatCurrency(rental.rentalAmount)}</span>
                  <div className="flex space-x-1">
                    {isStartDate && <span className="w-2 h-2 bg-green-500 rounded-full" title="Check-in" />}
                    {isEndDate && <span className="w-2 h-2 bg-red-500 rounded-full" title="Check-out" />}
                  </div>
                </div>
              </div>
            );
          })}

          {/* Show count if more rentals exist */}
          {dayRentals.length > 3 && <div className="text-xs text-gray-500 text-center py-1">+{dayRentals.length - 3} more</div>}
        </div>
      </div>
    );
  };

  if (rentalsLoading || propertiesLoading) {
    return (
      <div className="flex items-center justify-center h-96 bg-white rounded-lg border border-gray-200">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading calendar...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Calendar Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <CalendarIcon className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Rental Calendar</h3>
        </div>

        <div className="w-64">
          <Select placeholder="Filter by property" value={selectedPropertyId} onChange={setSelectedPropertyId} options={propertyOptions} />
        </div>
      </div>

      {/* Calendar Legend */}
      <div className="flex items-center space-x-6 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-100 border border-green-200 rounded" />
          <span className="text-gray-600">Active</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-100 border border-blue-200 rounded" />
          <span className="text-gray-600">Ended</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-100 border border-red-200 rounded" />
          <span className="text-gray-600">Cancelled</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-green-500 rounded-full" />
          <span className="text-gray-600">Check-in</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-red-500 rounded-full" />
          <span className="text-gray-600">Check-out</span>
        </div>
      </div>

      {/* Calendar */}
      <Calendar currentDate={currentDate} onDateChange={setCurrentDate} renderDay={renderCalendarDay} className="shadow-sm" />

      {/* Summary */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{filteredRentals.filter((r) => r.status === 'active').length}</div>
            <div className="text-gray-600">Active Rentals</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{filteredRentals.length}</div>
            <div className="text-gray-600">Total Rentals</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {formatCurrency(filteredRentals.filter((r) => r.status === 'active').reduce((sum, r) => sum + r.rentalAmount, 0))}
            </div>
            <div className="text-gray-600">Active Revenue</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RentalCalendar;

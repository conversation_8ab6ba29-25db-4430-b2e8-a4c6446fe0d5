// React import removed - using JSX transform
import { useDashboard } from '../../hooks/useDashboard';
import { useMonthlyRentals } from '../../hooks/useMonthlyRentals';
import { Building2, TrendingUp, DollarSign, Calendar, BarChart3, Plus } from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { useNavigate } from 'react-router-dom';

export function Dashboard() {
  const { stats, isLoading } = useDashboard();
  const { monthlyData, isLoading: isLoadingMonthly } = useMonthlyRentals();
  const navigate = useNavigate();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Calculate additional stats from monthly data
  const totalRentalsThisYear = monthlyData.reduce((sum, month) => sum + month.totalRentals, 0);
  const totalRevenueThisYear = monthlyData.reduce((sum, month) => sum + month.totalRevenue, 0);
  const averageMonthlyRevenue = monthlyData.length > 0 ? totalRevenueThisYear / monthlyData.length : 0;

  // Prepare chart data with proper formatting
  const chartData = monthlyData.map((month) => ({
    ...month,
    monthDisplay: `${month.monthName.substring(0, 3)} ${month.year}`,
  }));

  const statCards = [
    {
      title: 'Total Properties',
      value: stats.totalProperties.toString(),
      icon: Building2,
      color: 'blue',
    },

    {
      title: 'Rentals This Year',
      value: totalRentalsThisYear.toString(),
      icon: Calendar,
      color: 'blue',
    },
    {
      title: 'Revenue This Year',
      value: formatCurrency(totalRevenueThisYear),
      icon: BarChart3,
      color: 'green',
    },
    {
      title: 'Avg Monthly Revenue',
      value: formatCurrency(averageMonthlyRevenue),
      icon: TrendingUp,
      color: 'green',
    },
    {
      title: 'This Month Income',
      value: formatCurrency(stats.totalMonthlyIncome),
      icon: TrendingUp,
      color: 'green',
    },
    {
      title: 'This Month Net Income',
      value: formatCurrency(stats.netPropertyIncome),
      icon: DollarSign,
      color: stats.netPropertyIncome >= 0 ? 'green' : 'red',
    },
  ];

  if (isLoading || isLoadingMonthly) {
    return (
      <div className="p-6">
        <div className="mb-8">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-2 animate-pulse"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="bg-white rounded-xl border border-gray-200 p-6 animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white rounded-xl border border-gray-200 p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
          <div className="bg-white rounded-xl border border-gray-200 p-6 animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Overview of your property portfolio and finances</p>
        <p className="text-sm text-gray-500 mt-1">Current month data reflects {new Date().toLocaleString('default', { month: 'long', year: 'numeric' })}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
        {statCards.map((card) => {
          const Icon = card.icon;
          const colorClasses = {
            blue: 'bg-blue-500 text-white',
            green: 'bg-green-500 text-white',
            red: 'bg-red-500 text-white',
          };

          return (
            <div key={card.title} className="bg-white rounded-xl border border-gray-200 p-6 hover:shadow-lg transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-xl font-bold text-gray-900 mt-1">{card.value}</p>
                </div>
                <div className={`p-2 rounded-lg ${colorClasses[card.color as keyof typeof colorClasses]}`}>
                  <Icon className="w-5 h-5" />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Monthly Rental Chart */}
      <div className="mb-8">
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Monthly Rental Performance (By Month)</h3>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <BarChart3 className="w-4 h-4" />
              <span>Revenue vs Costs by Month</span>
            </div>
          </div>

          {chartData.length > 0 ? (
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="monthDisplay" />
                  <YAxis tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
                  <Tooltip formatter={(value: number, name: string) => [formatCurrency(value), name]} labelFormatter={(label) => `Month: ${label}`} />
                  <Legend />
                  <Bar dataKey="totalRevenue" fill="#10b981" name="Revenue" />
                  <Bar dataKey="totalCosts" fill="#ef4444" name="Costs" />
                  <Bar dataKey="netIncome" fill="#3b82f6" name="Net Income" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-80 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <BarChart3 className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No monthly rental data available</p>
                <p className="text-sm mt-1">Chart will show data for each month when you create rentals</p>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button
              onClick={() => navigate('/properties/new')}
              className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center">
                <Plus className="w-5 h-5 text-blue-600 mr-3" />
                <div>
                  <div className="font-medium text-gray-900">Add New Property</div>
                  <div className="text-sm text-gray-600">Create a new property and set ownership</div>
                </div>
              </div>
            </button>
            <button
              onClick={() => navigate('/rentals/new')}
              className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center">
                <Calendar className="w-5 h-5 text-green-600 mr-3" />
                <div>
                  <div className="font-medium text-gray-900">Create Rental</div>
                  <div className="text-sm text-gray-600">Set up a new rental agreement</div>
                </div>
              </div>
            </button>
            <button
              onClick={() => navigate('/guests/new')}
              className="w-full text-left p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center">
                <Plus className="w-5 h-5 text-purple-600 mr-3" />
                <div>
                  <div className="font-medium text-gray-900">Add Guest</div>
                  <div className="text-sm text-gray-600">Register a new guest</div>
                </div>
              </div>
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Trends (Over Time)</h3>
          {chartData.length > 0 ? (
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="monthDisplay" />
                  <YAxis tickFormatter={(value) => `$${(value / 1000).toFixed(0)}k`} />
                  <Tooltip formatter={(value: number, name: string) => [formatCurrency(value), name]} labelFormatter={(label) => `Month: ${label}`} />
                  <Legend />
                  <Line type="monotone" dataKey="netIncome" stroke="#3b82f6" strokeWidth={2} name="Net Income" />
                  <Line type="monotone" dataKey="totalRentals" stroke="#10b981" strokeWidth={2} name="Total Rentals" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <div className="h-64 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <TrendingUp className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>No monthly trend data available</p>
                <p className="text-sm mt-1">Trends will show month-to-month changes over time</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

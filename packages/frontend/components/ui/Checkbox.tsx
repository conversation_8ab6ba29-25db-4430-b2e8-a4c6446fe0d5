import React from 'react';
import { clsx } from 'clsx';
import { Check, Minus } from 'lucide-react';

export interface CheckboxProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type' | 'size'> {
  label?: string;
  error?: string;
  helperText?: string;
  indeterminate?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const checkboxSizes = {
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
};

const iconSizes = {
  sm: 'w-3 h-3',
  md: 'w-4 h-4',
  lg: 'w-5 h-5',
};

export const Checkbox: React.FC<CheckboxProps> = ({ label, error, helperText, indeterminate = false, size = 'md', className, id, checked, ...props }) => {
  const checkboxId = id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;
  const hasError = !!error;

  return (
    <div className="flex flex-col">
      <div className="flex items-center">
        <div className="relative">
          <input id={checkboxId} type="checkbox" checked={checked} className="sr-only" {...props} />
          <label
            htmlFor={checkboxId}
            data-testid={`checkbox-${checkboxId}`}
            className={clsx(
              'flex items-center justify-center border-2 rounded cursor-pointer transition-colors',
              'focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2',
              checkboxSizes[size],
              hasError
                ? 'border-red-500 bg-red-50'
                : checked || indeterminate
                  ? 'border-blue-600 bg-blue-600'
                  : 'border-gray-300 bg-white hover:border-gray-400',
              props.disabled && 'opacity-50 cursor-not-allowed',
              className
            )}
          >
            {checked && !indeterminate && <Check className={clsx('text-white', iconSizes[size])} />}
            {indeterminate && <Minus className={clsx('text-white', iconSizes[size])} />}
          </label>
        </div>

        {label && (
          <label
            htmlFor={checkboxId}
            className={clsx(
              'ml-3 text-sm font-medium cursor-pointer',
              hasError ? 'text-red-700' : 'text-gray-700',
              props.disabled && 'opacity-50 cursor-not-allowed'
            )}
          >
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}
      </div>

      {(error || helperText) && (
        <div className="mt-1">
          {error && <p className="text-sm text-red-600">{error}</p>}
          {!error && helperText && <p className="text-sm text-gray-500">{helperText}</p>}
        </div>
      )}
    </div>
  );
};

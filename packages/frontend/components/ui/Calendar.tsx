import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { generateCalendarDays, getPreviousMonth, getNextMonth, isInCurrentMonth, isToday, formatMonthYear, getWeekDays } from '../../utils/calendar';

export interface CalendarProps {
  currentDate: Date;
  onDateChange: (date: Date) => void;
  onDayClick?: (date: Date) => void;
  renderDay?: (date: Date, isCurrentMonth: boolean, isToday: boolean) => React.ReactNode;
  className?: string;
}

export const Calendar: React.FC<CalendarProps> = ({ currentDate, onDateChange, onDayClick, renderDay, className = '' }) => {
  const calendarDays = generateCalendarDays(currentDate);
  const weekDays = getWeekDays();

  const handlePreviousMonth = () => {
    onDateChange(getPreviousMonth(currentDate));
  };

  const handleNextMonth = () => {
    onDateChange(getNextMonth(currentDate));
  };

  const handleDayClick = (date: Date) => {
    if (onDayClick) {
      onDayClick(date);
    }
  };

  const defaultRenderDay = (date: Date, isCurrentMonth: boolean, isTodayDate: boolean) => {
    return (
      <div
        className={`
          w-full h-full flex items-center justify-center text-sm cursor-pointer
          ${isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}
          ${isTodayDate ? 'bg-blue-600 text-white rounded-full' : 'hover:bg-gray-100'}
          ${!isCurrentMonth ? 'hover:bg-gray-50' : ''}
        `}
        onClick={() => handleDayClick(date)}
      >
        {date.getDate()}
      </div>
    );
  };

  return (
    <div className={`bg-white rounded-lg border border-gray-200 ${className}`}>
      {/* Calendar Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <button onClick={handlePreviousMonth} className="p-2 hover:bg-gray-100 rounded-lg transition-colors" aria-label="Previous month">
          <ChevronLeft className="w-5 h-5 text-gray-600" />
        </button>

        <h2 className="text-lg font-semibold text-gray-900">{formatMonthYear(currentDate)}</h2>

        <button onClick={handleNextMonth} className="p-2 hover:bg-gray-100 rounded-lg transition-colors" aria-label="Next month">
          <ChevronRight className="w-5 h-5 text-gray-600" />
        </button>
      </div>

      {/* Calendar Grid */}
      <div className="p-4">
        {/* Week Days Header */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {weekDays.map((day) => (
            <div key={day} className="h-8 flex items-center justify-center text-xs font-medium text-gray-500 uppercase">
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Days */}
        <div className="grid grid-cols-7 gap-1">
          {calendarDays.map((date, index) => {
            const isCurrentMonthDate = isInCurrentMonth(date, currentDate);
            const isTodayDate = isToday(date);

            return (
              <div key={index} className="h-24 border border-gray-100 rounded-lg overflow-hidden">
                {renderDay ? renderDay(date, isCurrentMonthDate, isTodayDate) : defaultRenderDay(date, isCurrentMonthDate, isTodayDate)}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Calendar;

import React from 'react';
import { UserRole } from '@alom-rentals/shared';
import { useRoleBasedUI } from '../../hooks/useRoleManagement';
import { Button, type ButtonProps } from './Button';

interface RoleBasedButtonProps extends ButtonProps {
  allowedRoles: UserRole[];
  requireAll?: boolean; // If true, user must have ALL roles (for future multi-role support)
  fallback?: React.ReactNode;
  action?: 'create' | 'edit' | 'delete' | 'view' | 'manage';
  'data-testid'?: string;
}

/**
 * Button component that only renders for users with specific roles
 */
export const RoleBasedButton: React.FC<RoleBasedButtonProps> = ({
  allowedRoles,
  requireAll = false,
  fallback = null,
  action,
  children,
  'data-testid': dataTestId,
  ...buttonProps
}) => {
  const { currentRole, hasReadOnlyAccess } = useRoleBasedUI();

  // If user has read-only access, hide all action buttons except view
  if (hasReadOnlyAccess && action && action !== 'view') {
    return <>{fallback}</>;
  }

  // Check if user has required role(s)
  const hasRequiredRole = requireAll
    ? allowedRoles.every((role) => currentRole === role) // For future multi-role support
    : allowedRoles.includes(currentRole);

  if (!hasRequiredRole) {
    return <>{fallback}</>;
  }

  // Generate test ID based on action if provided
  const testId = dataTestId || (action ? `${action}-btn` : undefined);

  return (
    <Button {...buttonProps} data-testid={testId}>
      {children}
    </Button>
  );
};

// Specific role-based button components for common use cases
interface ActionButtonProps extends Omit<ButtonProps, 'children'> {
  children?: React.ReactNode;
  fallback?: React.ReactNode;
}

export const CreateButton: React.FC<ActionButtonProps> = ({ children = 'Create', fallback, ...props }) => (
  <RoleBasedButton allowedRoles={['ADMIN', 'OPERATOR']} action="create" fallback={fallback} data-testid="create-btn" {...props}>
    {children}
  </RoleBasedButton>
);

export const EditButton: React.FC<ActionButtonProps> = ({ children = 'Edit', fallback, ...props }) => (
  <RoleBasedButton allowedRoles={['ADMIN', 'OPERATOR']} action="edit" fallback={fallback} data-testid="edit-btn" {...props}>
    {children}
  </RoleBasedButton>
);

export const DeleteButton: React.FC<ActionButtonProps> = ({ children = 'Delete', fallback, ...props }) => (
  <RoleBasedButton allowedRoles={['ADMIN', 'OPERATOR']} action="delete" fallback={fallback} data-testid="delete-btn" {...props}>
    {children}
  </RoleBasedButton>
);

export const ViewButton: React.FC<ActionButtonProps> = ({ children = 'View', fallback, ...props }) => (
  <RoleBasedButton allowedRoles={['ADMIN', 'OPERATOR', 'CUSTOMER']} action="view" fallback={fallback} data-testid="view-btn" {...props}>
    {children}
  </RoleBasedButton>
);

export const AdminOnlyButton: React.FC<ActionButtonProps> = ({ children, fallback, ...props }) => (
  <RoleBasedButton allowedRoles={['ADMIN']} fallback={fallback} data-testid="admin-only-btn" {...props}>
    {children}
  </RoleBasedButton>
);

export const OperatorOrAdminButton: React.FC<ActionButtonProps> = ({ children, fallback, ...props }) => (
  <RoleBasedButton allowedRoles={['ADMIN', 'OPERATOR']} fallback={fallback} data-testid="operator-admin-btn" {...props}>
    {children}
  </RoleBasedButton>
);

// Specific buttons for different entities
interface EntityButtonProps extends ActionButtonProps {
  entityType?: 'property' | 'guest' | 'rental' | 'user';
}

export const CreatePropertyButton: React.FC<EntityButtonProps> = ({ children = 'Create Property', ...props }) => (
  <CreateButton data-testid="create-property-btn" {...props}>
    {children}
  </CreateButton>
);

export const EditPropertyButton: React.FC<EntityButtonProps> = ({ children = 'Edit Property', ...props }) => (
  <EditButton data-testid="edit-property-btn" {...props}>
    {children}
  </EditButton>
);

export const DeletePropertyButton: React.FC<EntityButtonProps> = ({ children = 'Delete Property', ...props }) => (
  <DeleteButton data-testid="delete-property-btn" {...props}>
    {children}
  </DeleteButton>
);

export const CreateGuestButton: React.FC<EntityButtonProps> = ({ children = 'Create Guest', ...props }) => (
  <CreateButton data-testid="create-guest-btn" {...props}>
    {children}
  </CreateButton>
);

export const EditGuestButton: React.FC<EntityButtonProps> = ({ children = 'Edit Guest', ...props }) => (
  <EditButton data-testid="edit-guest-btn" {...props}>
    {children}
  </EditButton>
);

export const DeleteGuestButton: React.FC<EntityButtonProps> = ({ children = 'Delete Guest', ...props }) => (
  <DeleteButton data-testid="delete-guest-btn" {...props}>
    {children}
  </DeleteButton>
);

export const CreateRentalButton: React.FC<EntityButtonProps> = ({ children = 'Create Rental', ...props }) => (
  <CreateButton data-testid="create-rental-btn" {...props}>
    {children}
  </CreateButton>
);

export const EditRentalButton: React.FC<EntityButtonProps> = ({ children = 'Edit Rental', ...props }) => (
  <EditButton data-testid="edit-rental-btn" {...props}>
    {children}
  </EditButton>
);

export const DeleteRentalButton: React.FC<EntityButtonProps> = ({ children = 'Delete Rental', ...props }) => (
  <DeleteButton data-testid="delete-rental-btn" {...props}>
    {children}
  </DeleteButton>
);

export const CreateUserButton: React.FC<EntityButtonProps> = ({ children = 'Create User', ...props }) => (
  <AdminOnlyButton data-testid="create-user-btn" {...props}>
    {children}
  </AdminOnlyButton>
);

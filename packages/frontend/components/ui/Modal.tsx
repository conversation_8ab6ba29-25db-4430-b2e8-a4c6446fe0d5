import React from 'react';
import { clsx } from 'clsx';
import { X } from 'lucide-react';
import { Button } from './Button';

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  maxHeight?: boolean;
  footer?: React.ReactNode;
}

const modalSizes = {
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  '3xl': 'max-w-3xl',
};

export const Modal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  size = 'md',
  showCloseButton = true,
  closeOnOverlayClick = true,
  maxHeight = false,
  footer,
}) => {
  if (!isOpen) return null;

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget && closeOnOverlayClick) {
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" onClick={handleOverlayClick}>
      <div className={clsx('bg-white rounded-xl w-full mx-4', modalSizes[size], maxHeight ? 'max-h-[90vh] flex flex-col' : '')}>
        {(title || showCloseButton) && (
          <div className={clsx('flex items-center justify-between', maxHeight ? 'p-6 border-b border-gray-200 flex-shrink-0' : 'p-6 pb-4')}>
            {title && <h3 className="text-lg font-semibold text-gray-900">{title}</h3>}
            {showCloseButton && <Button variant="ghost" size="sm" onClick={onClose} icon={X} className="p-1 hover:bg-gray-100" />}
          </div>
        )}

        <div className={clsx(maxHeight ? 'flex-1 overflow-y-auto p-6' : 'p-6 pt-0')}>{children}</div>

        {footer && <div className={clsx(maxHeight ? 'p-6 border-t border-gray-200 flex-shrink-0' : 'px-6 pb-6')}>{footer}</div>}
      </div>
    </div>
  );
};

export interface ModalFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const ModalFooter: React.FC<ModalFooterProps> = ({ children, className }) => {
  return <div className={clsx('flex justify-end space-x-4 pt-4 border-t border-gray-200 mt-6 bg-white', className)}>{children}</div>;
};

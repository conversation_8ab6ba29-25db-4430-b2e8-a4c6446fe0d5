import React from 'react';
import { clsx } from 'clsx';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { Button } from './Button';

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  maxVisiblePages?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const paginationSizes = {
  sm: 'text-xs',
  md: 'text-sm',
  lg: 'text-base',
};

export const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  showFirstLast = true,
  showPrevNext = true,
  maxVisiblePages = 5,
  className,
  size = 'md',
}) => {
  if (totalPages <= 1) return null;

  const getVisiblePages = () => {
    const pages: (number | 'ellipsis')[] = [];

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than max visible
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Calculate start and end of visible range
      let start = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
      const end = Math.min(totalPages, start + maxVisiblePages - 1);

      // Adjust start if we're near the end
      if (end - start + 1 < maxVisiblePages) {
        start = Math.max(1, end - maxVisiblePages + 1);
      }

      // Add first page and ellipsis if needed
      if (start > 1) {
        pages.push(1);
        if (start > 2) {
          pages.push('ellipsis');
        }
      }

      // Add visible pages
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      // Add ellipsis and last page if needed
      if (end < totalPages) {
        if (end < totalPages - 1) {
          pages.push('ellipsis');
        }
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const visiblePages = getVisiblePages();

  const handlePageClick = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  return (
    <nav className={clsx('flex items-center justify-center space-x-1', paginationSizes[size], className)}>
      {/* First page button */}
      {showFirstLast && currentPage > 1 && (
        <Button variant="ghost" size="sm" onClick={() => handlePageClick(1)} className="px-2 py-1">
          First
        </Button>
      )}

      {/* Previous page button */}
      {showPrevNext && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlePageClick(currentPage - 1)}
          disabled={currentPage <= 1}
          icon={ChevronLeft}
          className="px-2 py-1"
        />
      )}

      {/* Page numbers */}
      {visiblePages.map((page, index) => {
        if (page === 'ellipsis') {
          return (
            <span key={`ellipsis-${index}`} className="px-2 py-1 text-gray-500">
              <MoreHorizontal className="w-4 h-4" />
            </span>
          );
        }

        return (
          <Button
            key={page}
            variant={page === currentPage ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => handlePageClick(page)}
            className={clsx('px-3 py-1 min-w-[2rem]', page === currentPage && 'font-semibold')}
          >
            {page}
          </Button>
        );
      })}

      {/* Next page button */}
      {showPrevNext && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handlePageClick(currentPage + 1)}
          disabled={currentPage >= totalPages}
          icon={ChevronRight}
          iconPosition="right"
          className="px-2 py-1"
        />
      )}

      {/* Last page button */}
      {showFirstLast && currentPage < totalPages && (
        <Button variant="ghost" size="sm" onClick={() => handlePageClick(totalPages)} className="px-2 py-1">
          Last
        </Button>
      )}
    </nav>
  );
};

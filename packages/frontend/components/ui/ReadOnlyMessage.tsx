import React from 'react';
import { Eye, Info } from 'lucide-react';
import { useRoleBasedUI } from '../../hooks/useRoleManagement';

interface ReadOnlyMessageProps {
  message?: string;
  className?: string;
  variant?: 'info' | 'warning';
  showIcon?: boolean;
}

export const ReadOnlyMessage: React.FC<ReadOnlyMessageProps> = ({
  message = 'You are viewing this data in read-only mode. Contact an administrator to make changes.',
  className = '',
  variant = 'info',
  showIcon = true,
}) => {
  const { hasReadOnlyAccess } = useRoleBasedUI();

  // Only show for customers (read-only users)
  if (!hasReadOnlyAccess) {
    return null;
  }

  const baseClasses = 'flex items-center gap-2 p-3 rounded-lg border text-sm';
  const variantClasses = {
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    warning: 'bg-amber-50 border-amber-200 text-amber-800',
  };

  return (
    <div className={`${baseClasses} ${variantClasses[variant]} ${className}`} data-testid="read-only-message">
      {showIcon && <div className="flex-shrink-0">{variant === 'info' ? <Eye className="w-4 h-4" /> : <Info className="w-4 h-4" />}</div>}
      <span>{message}</span>
    </div>
  );
};

interface ReadOnlyWrapperProps {
  children: React.ReactNode;
  message?: string;
  showMessage?: boolean;
  className?: string;
}

export const ReadOnlyWrapper: React.FC<ReadOnlyWrapperProps> = ({ children, message, showMessage = true, className = '' }) => {
  const { hasReadOnlyAccess } = useRoleBasedUI();

  return (
    <div className={className}>
      {hasReadOnlyAccess && showMessage && <ReadOnlyMessage message={message} className="mb-4" />}
      {children}
    </div>
  );
};

export default ReadOnlyMessage;

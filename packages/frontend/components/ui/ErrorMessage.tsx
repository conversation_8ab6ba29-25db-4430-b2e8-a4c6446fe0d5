import React, { useEffect } from 'react';
import { clsx } from 'clsx';
import { AlertCircle, AlertTriangle, Info, CheckCircle } from 'lucide-react';

export interface ErrorMessageProps {
  message: string;
  type?: 'error' | 'warning' | 'info' | 'success';
  className?: string;
  showIcon?: boolean;
}

const messageTypes = {
  error: {
    icon: AlertCircle,
    className: 'bg-red-50 border-red-200 text-red-700',
    iconClassName: 'text-red-500',
  },
  warning: {
    icon: AlertTriangle,
    className: 'bg-yellow-50 border-yellow-200 text-yellow-700',
    iconClassName: 'text-yellow-500',
  },
  info: {
    icon: Info,
    className: 'bg-blue-50 border-blue-200 text-blue-700',
    iconClassName: 'text-blue-500',
  },
  success: {
    icon: CheckCircle,
    className: 'bg-green-50 border-green-200 text-green-700',
    iconClassName: 'text-green-500',
  },
};

export const ErrorMessage: React.FC<ErrorMessageProps> = ({ message, type = 'error', className, showIcon = true }) => {
  const refAlert = React.useRef<HTMLDivElement>(null);
  const config = messageTypes[type];
  const Icon = config.icon;

  useEffect(() => {
    setTimeout(() => {
      if (refAlert.current) {
        refAlert.current.classList.add('hidden');
      }
    }, 5000);
  }, [message]);

  return (
    <div ref={refAlert} className={clsx('border px-4 py-3 rounded-lg flex items-start animate-fade opacity-0', config.className, className)}>
      {showIcon && <Icon className={clsx('w-4 h-4 mr-2 mt-0.5 flex-shrink-0', config.iconClassName)} />}
      <span className="text-sm">{message}</span>
    </div>
  );
};

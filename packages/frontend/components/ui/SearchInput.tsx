import React from 'react';
import { Search, X } from 'lucide-react';
import { Input, InputProps } from './Input';

export interface SearchInputProps extends Omit<InputProps, 'leftIcon' | 'rightIcon' | 'onRightIconClick'> {
  onClear?: () => void;
  showClearButton?: boolean;
}

export const SearchInput: React.FC<SearchInputProps> = ({ onClear, showClearButton = true, value, placeholder = 'Search...', ...props }) => {
  const hasValue = value && value.toString().length > 0;
  const showClear = showClearButton && hasValue && onClear;

  return (
    <Input
      {...props}
      value={value}
      placeholder={placeholder}
      leftIcon={Search}
      rightIcon={showClear ? X : undefined}
      onRightIconClick={showClear ? onClear : undefined}
    />
  );
};

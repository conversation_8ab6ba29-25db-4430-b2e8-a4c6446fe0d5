import React from 'react';
import { clsx } from 'clsx';

export interface CardProps {
  children: React.ReactNode;
  className?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  variant?: 'default' | 'outlined' | 'elevated';
}

const cardPadding = {
  none: '',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
};

const cardVariants = {
  default: 'bg-white border border-gray-200',
  outlined: 'bg-white border-2 border-gray-300',
  elevated: 'bg-white shadow-lg border border-gray-100',
};

export const Card: React.FC<CardProps> = ({ children, className, padding = 'md', variant = 'default' }) => {
  return <div className={clsx('rounded-xl', cardVariants[variant], cardPadding[padding], className)}>{children}</div>;
};

export interface CardHeaderProps {
  title?: string;
  subtitle?: string;
  children?: React.ReactNode;
  icon?: React.ComponentType<{ className?: string }>;
  className?: string;
}

export const CardHeader: React.FC<CardHeaderProps> = ({ title, subtitle, children, icon: Icon, className }) => {
  return (
    <div className={clsx('mb-6', className)}>
      {(title || Icon) && (
        <div className="flex items-center mb-2">
          {Icon && <Icon className="w-5 h-5 text-blue-600 mr-2" />}
          {title && <h2 className="text-xl font-semibold text-gray-900">{title}</h2>}
        </div>
      )}
      {subtitle && <p className="text-gray-600">{subtitle}</p>}
      {children}
    </div>
  );
};

export interface CardContentProps {
  children: React.ReactNode;
  className?: string;
}

export const CardContent: React.FC<CardContentProps> = ({ children, className }) => {
  return <div className={clsx('space-y-4', className)}>{children}</div>;
};

export interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

export const CardFooter: React.FC<CardFooterProps> = ({ children, className }) => {
  return <div className={clsx('mt-6 pt-4 border-t border-gray-200', className)}>{children}</div>;
};

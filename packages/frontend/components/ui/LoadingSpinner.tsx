import React from 'react';
import { clsx } from 'clsx';

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  color?: 'primary' | 'white' | 'gray';
}

const spinnerSizes = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
};

const spinnerColors = {
  primary: 'border-blue-600 border-t-transparent',
  white: 'border-white border-t-transparent',
  gray: 'border-gray-600 border-t-transparent',
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = 'md', className, color = 'primary' }) => {
  return <div className={clsx('border-2 rounded-full animate-spin', spinnerSizes[size], spinnerColors[color], className)} role="status" aria-label="Loading" />;
};

export interface LoadingStateProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({ message = 'Loading...', size = 'md', className }) => {
  return (
    <div className={clsx('flex flex-col items-center justify-center py-8', className)}>
      <LoadingSpinner size={size} />
      {message && <p className="text-gray-600 mt-2 text-sm">{message}</p>}
    </div>
  );
};

import React from 'react';
import { useCustomerContext } from '../../hooks/useCustomerUtils';
import { useAuth } from '../../hooks/useAuth';
import { IAssignedCustomer } from '@alom-rentals/shared';

interface CustomerSelectorProps {
  className?: string;
  placeholder?: string;
}

export const CustomerSelector: React.FC<CustomerSelectorProps> = ({ className = '', placeholder = 'Select a customer...' }) => {
  const { user } = useAuth();
  const { selectedCustomer, setSelectedCustomer, assignedCustomers, isLoading } = useCustomerContext();

  // Only show for operators
  if (user?.role !== 'OPERATOR') {
    return null;
  }

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-10 bg-gray-200 rounded-md"></div>
      </div>
    );
  }

  if (assignedCustomers.length === 0) {
    return <div className={`text-sm text-gray-500 p-2 bg-gray-50 rounded-md ${className}`}>No customers assigned to you. Please contact an administrator.</div>;
  }

  return (
    <div className={className}>
      <label htmlFor="customer-selector" className="block text-sm font-medium text-gray-700 mb-1">
        Managing Customer
      </label>
      <select
        id="customer-selector"
        value={selectedCustomer || ''}
        onChange={(e) => setSelectedCustomer(e.target.value || null)}
        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
      >
        <option value="">{placeholder}</option>
        {assignedCustomers.map((customer: IAssignedCustomer) => (
          <option key={customer.id} value={customer.id}>
            {customer.fullName} ({customer.email})
          </option>
        ))}
      </select>
      {selectedCustomer && (
        <div className="mt-2 text-xs text-gray-600">
          <div className="flex flex-wrap gap-2">
            {assignedCustomers.find((c: IAssignedCustomer) => c.id === selectedCustomer)?.permissions && (
              <>
                {assignedCustomers.find((c: IAssignedCustomer) => c.id === selectedCustomer)?.permissions.canCreateProperties && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Create</span>
                )}
                {assignedCustomers.find((c: IAssignedCustomer) => c.id === selectedCustomer)?.permissions.canEditProperties && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Edit</span>
                )}
                {assignedCustomers.find((c: IAssignedCustomer) => c.id === selectedCustomer)?.permissions.canDeleteProperties && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Delete</span>
                )}
                {assignedCustomers.find((c: IAssignedCustomer) => c.id === selectedCustomer)?.permissions.canManageRentals && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">Rentals</span>
                )}
                {assignedCustomers.find((c: IAssignedCustomer) => c.id === selectedCustomer)?.permissions.canViewFinancials && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Financials</span>
                )}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Compact version for navigation/header
export const CustomerSelectorCompact: React.FC<{ className?: string }> = ({ className = '' }) => {
  const { user } = useAuth();
  const { selectedCustomer, setSelectedCustomer, assignedCustomers, isLoading } = useCustomerContext();

  // Only show for operators
  if (user?.role !== 'OPERATOR') {
    return null;
  }

  if (isLoading) {
    return <div className={`animate-pulse h-8 w-48 bg-gray-200 rounded ${className}`}></div>;
  }

  if (assignedCustomers.length === 0) {
    return <div className={`text-xs text-red-600 ${className}`}>No customers assigned</div>;
  }

  return (
    <select
      value={selectedCustomer || ''}
      onChange={(e) => setSelectedCustomer(e.target.value || null)}
      className={`text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${className}`}
    >
      <option value="">Select customer...</option>
      {assignedCustomers.map((customer: IAssignedCustomer) => (
        <option key={customer.id} value={customer.id}>
          {customer.fullName}
        </option>
      ))}
    </select>
  );
};

import React from 'react';
import { UserRole } from '@alom-rentals/shared';
import { useAuth } from '../hooks/useAuth';
import { RoleGuard } from '../components/admin/RoleGuard';

// Higher-order component for role-based access
export function withRoleGuard<P extends object>(Component: React.ComponentType<P>, allowedRoles: UserRole[], fallback?: React.ReactNode) {
  return function RoleGuardedComponent(props: P) {
    return (
      <RoleGuard allowedRoles={allowedRoles} fallback={fallback}>
        <Component {...props} />
      </RoleGuard>
    );
  };
}

// Hook for role-based conditional logic
export const useRoleGuard = (allowedRoles: UserRole[]) => {
  const { user } = useAuth();

  if (!user || !user.role) {
    return { hasAccess: false, userRole: null };
  }

  const hasAccess = allowedRoles.includes(user.role as UserRole);
  return { hasAccess, userRole: user.role as UserRole };
};

// Utility function to check if user has required role
export const hasRequiredRole = (userRole: UserRole | null | undefined, allowedRoles: UserRole[]): boolean => {
  if (!userRole) return false;
  return allowedRoles.includes(userRole);
};

// Utility function to get the highest role from a list
export const getHighestRole = (roles: UserRole[]): UserRole | null => {
  const roleHierarchy: Record<UserRole, number> = {
    ADMIN: 3,
    OPERATOR: 2,
    CUSTOMER: 1,
  };

  let highestRole: UserRole | null = null;
  let highestLevel = 0;

  for (const role of roles) {
    const level = roleHierarchy[role];
    if (level > highestLevel) {
      highestLevel = level;
      highestRole = role;
    }
  }

  return highestRole;
};

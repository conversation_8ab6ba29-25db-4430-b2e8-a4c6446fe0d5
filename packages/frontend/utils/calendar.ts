/**
 * Calendar utility functions for date calculations and calendar operations
 */

import { IShortTermRental } from '@alom-rentals/shared';

/**
 * Get the first day of the month
 */
export const getFirstDayOfMonth = (date: Date): Date => {
  return new Date(date.getFullYear(), date.getMonth(), 1);
};

/**
 * Get the last day of the month
 */
export const getLastDayOfMonth = (date: Date): Date => {
  return new Date(date.getFullYear(), date.getMonth() + 1, 0);
};

/**
 * Get the number of days in a month
 */
export const getDaysInMonth = (date: Date): number => {
  return getLastDayOfMonth(date).getDate();
};

/**
 * Get the day of the week for the first day of the month (0 = Sunday, 6 = Saturday)
 */
export const getFirstDayOfWeek = (date: Date): number => {
  return getFirstDayOfMonth(date).getDay();
};

/**
 * Generate calendar days for a given month
 * Returns an array of dates including padding days from previous/next month
 */
export const generateCalendarDays = (date: Date): Date[] => {
  const firstDayOfWeek = getFirstDayOfWeek(date);
  const daysInMonth = getDaysInMonth(date);

  const days: Date[] = [];

  // Add padding days from previous month
  const prevMonth = new Date(date.getFullYear(), date.getMonth() - 1, 1);
  const daysInPrevMonth = getDaysInMonth(prevMonth);

  for (let i = firstDayOfWeek - 1; i >= 0; i--) {
    days.push(new Date(prevMonth.getFullYear(), prevMonth.getMonth(), daysInPrevMonth - i));
  }

  // Add days from current month
  for (let day = 1; day <= daysInMonth; day++) {
    days.push(new Date(date.getFullYear(), date.getMonth(), day));
  }

  // Add padding days from next month to complete the grid (42 days = 6 weeks)
  const totalDays = 42;
  const remainingDays = totalDays - days.length;
  const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1, 1);

  for (let day = 1; day <= remainingDays; day++) {
    days.push(new Date(nextMonth.getFullYear(), nextMonth.getMonth(), day));
  }

  return days;
};

/**
 * Navigate to previous month
 */
export const getPreviousMonth = (date: Date): Date => {
  return new Date(date.getFullYear(), date.getMonth() - 1, 1);
};

/**
 * Navigate to next month
 */
export const getNextMonth = (date: Date): Date => {
  return new Date(date.getFullYear(), date.getMonth() + 1, 1);
};

/**
 * Check if a date is in the current month
 */
export const isInCurrentMonth = (date: Date, currentMonth: Date): boolean => {
  return date.getMonth() === currentMonth.getMonth() && date.getFullYear() === currentMonth.getFullYear();
};

/**
 * Check if a date is today
 */
export const isToday = (date: Date): boolean => {
  const today = new Date();
  return date.getDate() === today.getDate() && date.getMonth() === today.getMonth() && date.getFullYear() === today.getFullYear();
};

/**
 * Format month and year for display
 */
export const formatMonthYear = (date: Date): string => {
  return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
};

/**
 * Check if two dates are the same day
 */
export const isSameDay = (date1: Date, date2: Date): boolean => {
  return date1.getDate() === date2.getDate() && date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear();
};

/**
 * Check if a date falls within a date range (inclusive)
 */
export const isDateInRange = (date: Date, startDate: Date, endDate: Date): boolean => {
  const checkDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  const start = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
  const end = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());

  return checkDate >= start && checkDate <= end;
};

/**
 * Get rentals that occur on a specific date
 */
export const getRentalsForDate = (date: Date, rentals: IShortTermRental[]): IShortTermRental[] => {
  return rentals.filter((rental) => {
    const startDate = new Date(rental.startDate);
    const endDate = new Date(rental.endDate);
    return isDateInRange(date, startDate, endDate);
  });
};

/**
 * Get the status color for a rental
 */
export const getRentalStatusColor = (status: string): string => {
  switch (status) {
    case 'active':
      return 'bg-green-500';
    case 'ended':
      return 'bg-blue-500';
    case 'cancelled':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

/**
 * Get the status color for a rental (lighter version for backgrounds)
 */
export const getRentalStatusColorLight = (status: string): string => {
  switch (status) {
    case 'active':
      return 'bg-green-100 border-green-200 text-green-800';
    case 'ended':
      return 'bg-blue-100 border-blue-200 text-blue-800';
    case 'cancelled':
      return 'bg-red-100 border-red-200 text-red-800';
    default:
      return 'bg-gray-100 border-gray-200 text-gray-800';
  }
};

/**
 * Calculate the number of days between two dates
 */
export const getDaysBetween = (startDate: Date, endDate: Date): number => {
  const start = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
  const end = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());
  const diffTime = Math.abs(end.getTime() - start.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

/**
 * Get the week days array
 */
export const getWeekDays = (): string[] => {
  return ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
};

/**
 * Get rentals that start on a specific date
 */
export const getRentalsStartingOnDate = (date: Date, rentals: IShortTermRental[]): IShortTermRental[] => {
  return rentals.filter((rental) => {
    const startDate = new Date(rental.startDate);
    return isSameDay(date, startDate);
  });
};

/**
 * Get rentals that end on a specific date
 */
export const getRentalsEndingOnDate = (date: Date, rentals: IShortTermRental[]): IShortTermRental[] => {
  return rentals.filter((rental) => {
    const endDate = new Date(rental.endDate);
    return isSameDay(date, endDate);
  });
};

/**
 * Check if a rental spans multiple days in the current month view
 */
export const isMultiDayRental = (rental: IShortTermRental, currentMonth: Date): boolean => {
  const startDate = new Date(rental.startDate);
  const endDate = new Date(rental.endDate);

  // Check if both start and end are in the current month
  const startInMonth = isInCurrentMonth(startDate, currentMonth);
  const endInMonth = isInCurrentMonth(endDate, currentMonth);

  // If both are in the month, check if they're different days
  if (startInMonth && endInMonth) {
    return !isSameDay(startDate, endDate);
  }

  // If only one is in the month, it's definitely multi-day
  return startInMonth || endInMonth;
};

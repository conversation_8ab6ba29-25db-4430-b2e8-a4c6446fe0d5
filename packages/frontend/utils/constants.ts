/**
 * Application constants
 */

// Property types
export const PROPERTY_TYPES = {
  RESIDENTIAL: 'residential',
  COMMERCIAL: 'commercial',
  LAND: 'land',
  OTHER: 'other',
} as const;

export const PROPERTY_TYPE_LABELS = {
  [PROPERTY_TYPES.RESIDENTIAL]: 'Residential',
  [PROPERTY_TYPES.COMMERCIAL]: 'Commercial',
  [PROPERTY_TYPES.LAND]: 'Land',
  [PROPERTY_TYPES.OTHER]: 'Other',
} as const;

// Document types
export const DOCUMENT_TYPES = {
  PASSPORT: 'passport',
  NATIONAL_ID: 'id',
  DRIVERS_LICENSE: 'license',
  OTHER: 'other',
} as const;

export const DOCUMENT_TYPE_LABELS = {
  [DOCUMENT_TYPES.PASSPORT]: 'Passport',
  [DOCUMENT_TYPES.NATIONAL_ID]: 'National ID',
  [DOCUMENT_TYPES.DRIVERS_LICENSE]: "Driver's License",
  [DOCUMENT_TYPES.OTHER]: 'Other',
} as const;

// Rental statuses
export const RENTAL_STATUSES = {
  ACTIVE: 'active',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  PENDING: 'pending',
} as const;

export const RENTAL_STATUS_LABELS = {
  [RENTAL_STATUSES.ACTIVE]: 'Active',
  [RENTAL_STATUSES.COMPLETED]: 'Completed',
  [RENTAL_STATUSES.CANCELLED]: 'Cancelled',
  [RENTAL_STATUSES.PENDING]: 'Pending',
} as const;

// Service types
export const SERVICE_TYPES = {
  FIXED: 'fixed',
  VARIABLE: 'variable',
} as const;

export const SERVICE_TYPE_LABELS = {
  [SERVICE_TYPES.FIXED]: 'Fixed',
  [SERVICE_TYPES.VARIABLE]: 'Variable',
} as const;

// Billing frequencies
export const BILLING_FREQUENCIES = {
  MONTHLY: 'monthly',
  QUARTERLY: 'quarterly',
  YEARLY: 'yearly',
  ONE_TIME: 'one-time',
} as const;

export const BILLING_FREQUENCY_LABELS = {
  [BILLING_FREQUENCIES.MONTHLY]: 'Monthly',
  [BILLING_FREQUENCIES.QUARTERLY]: 'Quarterly',
  [BILLING_FREQUENCIES.YEARLY]: 'Yearly',
  [BILLING_FREQUENCIES.ONE_TIME]: 'One-time',
} as const;

// Owner types
export const OWNER_TYPES = {
  USER: 'User',
  PARTNER: 'Partner',
} as const;

// API endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    PROFILE: '/auth/profile',
  },
  PROPERTIES: '/properties',
  PARTNERS: '/partners',
  GUESTS: '/guests',
  RENTALS: '/rentals',
  SERVICES: '/services',
  CURRENCIES: '/currencies',
  DASHBOARD: '/dashboard',
} as const;

// Query keys for React Query
export const QUERY_KEYS = {
  AUTH: ['auth'],
  PROPERTIES: ['properties'],
  PROPERTY: (id: string) => ['properties', id],
  PARTNERS: ['partners'],
  PARTNER: (id: string) => ['partners', id],
  GUESTS: ['guests'],
  GUEST: (id: string) => ['guests', id],
  RENTALS: ['rentals'],
  RENTAL: (id: string) => ['rentals', id],
  SERVICES: ['services'],
  SERVICE: (id: string) => ['services', id],
  CURRENCIES: ['currencies'],
  DASHBOARD: ['dashboard'],
} as const;

// Form validation messages
export const VALIDATION_MESSAGES = {
  REQUIRED: 'This field is required',
  INVALID_EMAIL: 'Please enter a valid email address',
  MIN_LENGTH: (length: number) => `Must be at least ${length} characters`,
  MAX_LENGTH: (length: number) => `Must be no more than ${length} characters`,
  INVALID_NUMBER: 'Must be a valid number',
  POSITIVE_NUMBER: 'Must be a positive number',
  INVALID_PERCENTAGE: 'Must be between 0 and 100',
  INVALID_PHONE: 'Please enter a valid phone number',
  INVALID_URL: 'Please enter a valid URL',
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
} as const;

// Date formats
export const DATE_FORMATS = {
  SHORT: 'MMM dd, yyyy',
  LONG: 'MMMM dd, yyyy',
  ISO: 'yyyy-MM-dd',
  DATETIME: 'MMM dd, yyyy HH:mm',
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 10,
  PAGE_SIZE_OPTIONS: [10, 25, 50, 100],
} as const;

// File upload
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
} as const;

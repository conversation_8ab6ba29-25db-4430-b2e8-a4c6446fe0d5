# Page snapshot

```yaml
- img
- heading "PropertyFin" [level=1]
- text: <PERSON>.<EMAIL> admin
- navigation:
  - link "Dashboard":
    - /url: /dashboard
    - img
    - text: Dashboard
  - link "Properties":
    - /url: /properties
    - img
    - text: Properties
  - link "Partners":
    - /url: /partners
    - img
    - text: Partners
  - link "Guests":
    - /url: /guests
    - img
    - text: Guests
  - link "Currencies":
    - /url: /currencies
    - img
    - text: Currencies
  - link "Rentals":
    - /url: /rentals
    - img
    - text: Rentals
  - link "Recurrent Costs":
    - /url: /recurrent-costs
    - img
    - text: Recurrent Costs
  - link "Personal Finance":
    - /url: /personal-finance
    - img
    - text: Personal Finance
  - link "Admin Dashboard":
    - /url: /admin
    - img
    - text: Admin Dashboard
  - link "Settings":
    - /url: /settings
    - img
    - text: Settings
- button "Sign Out":
  - img
  - text: Sign Out
- main:
  - heading "Dashboard" [level=1]
  - paragraph: Overview of your property portfolio and finances
  - paragraph: Current month data reflects July 2025
  - paragraph: Total Properties
  - paragraph: "238"
  - img
  - paragraph: Rentals This Year
  - paragraph: "1"
  - img
  - paragraph: Revenue This Year
  - paragraph: $1,000.00
  - img
  - paragraph: Avg Monthly Revenue
  - paragraph: $1,000.00
  - img
  - paragraph: This Month Income
  - paragraph: $0.00
  - img
  - paragraph: This Month Net Income
  - paragraph: $0.00
  - img
  - heading "Monthly Rental Performance (By Month)" [level=3]
  - img
  - text: Revenue vs Costs by Month
  - img: Aug 2025 $0k $0k $1k $1k $1k
  - list:
    - listitem:
      - img
      - text: Revenue
    - listitem:
      - img
      - text: Costs
    - listitem:
      - img
      - text: Net Income
  - heading "Quick Actions" [level=3]
  - button "Add New Property Create a new property and set ownership":
    - img
    - text: Add New Property Create a new property and set ownership
  - button "Create Rental Set up a new rental agreement":
    - img
    - text: Create Rental Set up a new rental agreement
  - button "Add Guest Register a new guest":
    - img
    - text: Add Guest Register a new guest
  - heading "Monthly Trends (Over Time)" [level=3]
  - img: Aug 2025 $0k $0k $0k $1k $1k
  - list:
    - listitem:
      - img
      - text: Net Income
    - listitem:
      - img
      - text: Total Rentals
- button "Open Tanstack query devtools":
  - img
```
import {
  IUserPublic,
  IRoleChangeResponse,
  IBulkRoleChangeResponse,
  RoleChangeInput,
  BulkRoleChangeInput,
  UserRole,
  IProperty,
  IGuest,
  IPartner,
  IShortTermRental,
} from '@alom-rentals/shared';
import { api } from '../lib/api';

export interface AdminStats {
  totalUsers: number;
  roleDistribution: {
    admin: number;
    operator: number;
    customer: number;
  };
  recentUsers: number;
}

export interface AuditLogEntry {
  _id: string;
  userId: {
    _id: string;
    email: string;
    fullName: string;
  };
  changedBy: {
    _id: string;
    email: string;
    fullName: string;
  };
  action: string;
  resourceType: string;
  resourceId?: string;
  oldValues?: Record<string, unknown>;
  newValues?: Record<string, unknown>;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
}

export interface PaginatedAuditLogs {
  data: AuditLogEntry[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Get all users (admin only)
export const getAllUsers = async (): Promise<IUserPublic[]> => {
  const response = await api.get('/admin/users');
  return response.data.data;
};

// Get user by ID (admin only)
export const getUserById = async (userId: string): Promise<IUserPublic> => {
  const response = await api.get(`/admin/users/${userId}`);
  return response.data.data;
};

// Change user role (admin only)
export const changeUserRole = async (userId: string, roleData: RoleChangeInput): Promise<IRoleChangeResponse> => {
  const response = await api.patch(`/admin/users/${userId}/role`, roleData);
  return response.data;
};

// Bulk role change (admin only)
export const bulkChangeUserRoles = async (bulkData: BulkRoleChangeInput): Promise<IBulkRoleChangeResponse> => {
  const response = await api.patch('/admin/users/bulk/role', bulkData);
  return response.data;
};

// Get role change audit logs (admin only)
export const getRoleChangeAuditLogs = async (page: number = 1, limit: number = 50): Promise<PaginatedAuditLogs> => {
  const response = await api.get('/admin/audit/role-changes', {
    params: { page, limit },
  });
  return response.data;
};

// Get admin statistics (admin only)
export const getAdminStats = async (): Promise<AdminStats> => {
  const response = await api.get('/admin/stats');
  return response.data.data;
};

// Cross-user data access functions (admin only)

// Get all properties with optional user filtering
export const getAllPropertiesForAdmin = async (userId?: string): Promise<IProperty[]> => {
  const params = userId ? { userId } : {};
  const response = await api.get('/properties', { params });
  return response.data;
};

// Get all guests with optional user filtering
export const getAllGuestsForAdmin = async (userId?: string): Promise<IGuest[]> => {
  const params = userId ? { userId } : {};
  const response = await api.get('/guests', { params });
  return response.data;
};

// Get all partners with optional user filtering
export const getAllPartnersForAdmin = async (userId?: string): Promise<IPartner[]> => {
  const params = userId ? { userId } : {};
  const response = await api.get('/partners', { params });
  return response.data;
};

// Get all rentals with optional user filtering
export const getAllRentalsForAdmin = async (userId?: string): Promise<IShortTermRental[]> => {
  const params = userId ? { userId } : {};
  const response = await api.get('/short-term-rentals', { params });
  return response.data.rentals || response.data;
};

// Password management functions (admin only)

export interface PasswordResetRequest {
  temporaryPassword: string;
  forceChange?: boolean;
}

export interface PasswordResetResponse {
  success: boolean;
  message: string;
  forceChange: boolean;
}

export interface ForcePasswordChangeRequest {
  forceChange: boolean;
}

export interface GenerateResetTokenResponse {
  success: boolean;
  message: string;
  resetToken: string;
  expiresAt: string;
}

// Reset user password (admin only)
export const resetUserPassword = async (userId: string, passwordData: PasswordResetRequest): Promise<PasswordResetResponse> => {
  const response = await api.patch(`/admin/users/${userId}/password/reset`, passwordData);
  return response.data;
};

// Force password change (admin only)
export const forcePasswordChange = async (userId: string, forceChangeData: ForcePasswordChangeRequest): Promise<PasswordResetResponse> => {
  const response = await api.patch(`/admin/users/${userId}/password/force-change`, forceChangeData);
  return response.data;
};

// Generate password reset token (admin only)
export const generatePasswordResetToken = async (userId: string): Promise<GenerateResetTokenResponse> => {
  const response = await api.post(`/admin/users/${userId}/password/generate-reset-token`);
  return response.data;
};

// Helper functions for role management
export const getRoleDisplayName = (role: UserRole): string => {
  switch (role) {
    case 'ADMIN':
      return 'Administrator';
    case 'OPERATOR':
      return 'Operator';
    case 'CUSTOMER':
      return 'Customer';
    default:
      return role;
  }
};

export const getRoleColor = (role: UserRole): string => {
  switch (role) {
    case 'ADMIN':
      return 'text-red-600 bg-red-50';
    case 'OPERATOR':
      return 'text-blue-600 bg-blue-50';
    case 'CUSTOMER':
      return 'text-green-600 bg-green-50';
    default:
      return 'text-gray-600 bg-gray-50';
  }
};

export const canChangeRole = (currentUserRole: UserRole): boolean => {
  // Only admins can change roles
  if (currentUserRole !== 'ADMIN') return false;

  // Admins can change any role
  return true;
};

export const getAvailableRoles = (currentUserRole: UserRole): UserRole[] => {
  if (currentUserRole === 'ADMIN') {
    return ['ADMIN', 'OPERATOR', 'CUSTOMER'];
  }

  // Non-admins cannot change roles
  return [];
};

// Validation helpers
export const validateRoleChange = (
  currentUserRole: UserRole,
  targetUserId: string,
  currentUserId: string,
  newRole: UserRole
): { valid: boolean; error?: string } => {
  // Only admins can change roles
  if (currentUserRole !== 'ADMIN') {
    return { valid: false, error: 'Only administrators can change user roles' };
  }

  // Prevent admin from changing their own role to non-admin
  if (targetUserId === currentUserId && newRole !== 'ADMIN') {
    return { valid: false, error: 'You cannot change your own admin role' };
  }

  // Valid role values
  const validRoles: UserRole[] = ['ADMIN', 'OPERATOR', 'CUSTOMER'];
  if (!validRoles.includes(newRole)) {
    return { valid: false, error: 'Invalid role specified' };
  }

  return { valid: true };
};

export const validateBulkRoleChange = (
  currentUserRole: UserRole,
  userIds: string[],
  currentUserId: string,
  newRole: UserRole
): { valid: boolean; error?: string } => {
  // Only admins can change roles
  if (currentUserRole !== 'ADMIN') {
    return { valid: false, error: 'Only administrators can change user roles' };
  }

  // Check if user IDs array is valid
  if (!Array.isArray(userIds) || userIds.length === 0) {
    return { valid: false, error: 'At least one user must be selected' };
  }

  // Prevent admin from changing their own role to non-admin in bulk operation
  if (userIds.includes(currentUserId) && newRole !== 'ADMIN') {
    return { valid: false, error: 'Cannot change your own admin role in bulk operation' };
  }

  // Valid role values
  const validRoles: UserRole[] = ['ADMIN', 'OPERATOR', 'CUSTOMER'];
  if (!validRoles.includes(newRole)) {
    return { valid: false, error: 'Invalid role specified' };
  }

  return { valid: true };
};

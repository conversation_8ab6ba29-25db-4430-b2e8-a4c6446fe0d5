import { createBrowserRouter, Navigate } from 'react-router-dom';
import { Layout } from '../components/layout/Layout';
import { AuthForm } from '../components/auth/AuthForm';
import { AuthProvider } from '../components/auth/AuthProvider';
import { CustomerProvider } from '../contexts/CustomerContext';
import { DashboardPage, PropertiesPage, PartnersPage, GuestsPage, RentalsPage } from '../pages';
import CurrenciesPage from '../components/currencies/CurrenciesPage';
import AdminPage from '../pages/AdminPage';
import { TransactionsPage } from '../pages/TransactionsPage';

// Main application routes
export const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <AuthProvider>
        <CustomerProvider>
          <Layout />
        </CustomerProvider>
      </AuthProvider>
    ),
    children: [
      {
        index: true,
        element: <Navigate to="/dashboard" replace />,
      },
      {
        path: 'auth',
        element: <AuthForm />,
      },
      {
        path: 'dashboard',
        element: <DashboardPage />,
      },
      {
        path: 'properties',
        element: <PropertiesPage />,
      },
      {
        path: 'properties/new',
        element: <PropertiesPage />,
      },
      {
        path: 'properties/:id',
        element: <PropertiesPage />,
      },
      {
        path: 'properties/:id/edit',
        element: <PropertiesPage />,
      },
      {
        path: 'partners',
        element: <PartnersPage />,
      },
      {
        path: 'guests',
        element: <GuestsPage />,
      },
      {
        path: 'guests/new',
        element: <GuestsPage />,
      },
      {
        path: 'guests/:id',
        element: <GuestsPage />,
      },
      {
        path: 'guests/:id/edit',
        element: <GuestsPage />,
      },
      {
        path: 'currencies',
        element: <CurrenciesPage />,
      },
      {
        path: 'trnsactions',
        element: <TransactionsPage />,
      },
      {
        path: 'admin',
        element: <AdminPage />,
      },
      {
        path: 'rentals',
        element: <RentalsPage />,
      },
      {
        path: 'rentals/new',
        element: <RentalsPage />,
      },
      {
        path: 'rentals/:id',
        element: <RentalsPage />,
      },
      {
        path: 'rentals/:id/edit',
        element: <RentalsPage />,
      },
      {
        path: 'rentals/calendar',
        element: <RentalsPage />,
      },
      {
        path: 'settings',
        element: (
          <div className="p-6">
            <h1 className="text-2xl font-bold">Settings (Coming Soon)</h1>
          </div>
        ),
      },
      {
        path: '*',
        element: <Navigate to="/dashboard" replace />,
      },
    ],
  },
]);

# End-to-End Testing with <PERSON><PERSON>

This directory contains comprehensive end-to-end tests for the Alom Rentals frontend application using Playwright.

## Overview

The E2E tests cover all major features and user workflows as documented in `/docs/features/features.md`:

- **Property Management**: Create, edit, delete, search, and filter properties
- **Rental Management**: Create, edit, delete, and manage short-term rentals
- **Dashboard Analytics**: View statistics, charts, and recent activity
- **Profile Management**: Update user profile and notification settings
- **Navigation & Integration**: Cross-feature workflows and URL state management

## Test Structure

```
e2e/
├── pages/              # Page Object Models
│   ├── BasePage.ts     # Common page functionality
│   ├── DashboardPage.ts
│   ├── PropertiesPage.ts
│   ├── RentalsPage.ts
│   └── ProfilePage.ts
├── fixtures/           # Test data and utilities
│   └── testData.ts     # Reusable test data
├── global-setup.ts     # Global test setup
├── global-teardown.ts  # Global test cleanup
└── *.spec.ts          # Test specifications
```

## Running Tests

### Prerequisites

1. Ensure both backend and frontend servers are running:
   ```bash
   # Terminal 1 - Backend
   cd packages/backend
   npm run dev

   # Terminal 2 - Frontend  
   cd packages/frontend
   npm run dev
   ```

2. Install Playwright browsers:
   ```bash
   cd packages/frontend
   npx playwright install
   ```

### Test Commands

```bash
# Run all E2E tests
npm run test:e2e

# Run tests with UI mode (interactive)
npm run test:e2e:ui

# Run tests in headed mode (see browser)
npm run test:e2e:headed

# Debug tests step by step
npm run test:e2e:debug

# View test report
npm run test:e2e:report
```

### Running Specific Tests

```bash
# Run specific test file
npx playwright test property-management.spec.ts

# Run specific test by name
npx playwright test --grep "should create a new apartment property"

# Run tests for specific browser
npx playwright test --project=chromium
```

## Test Categories

### 1. Property Management Tests (`property-management.spec.ts`)
- ✅ Property creation (apartment, house, condo)
- ✅ Property editing and validation
- ✅ Property deletion with confirmation
- ✅ Search and filtering functionality
- ✅ Property details view

### 2. Rental Management Tests (`rental-management.spec.ts`)
- ✅ Rental creation with validation
- ✅ Multi-guest rental management
- ✅ Rental editing and status updates
- ✅ Rental deletion
- ✅ Filtering by status and property
- ✅ Guest management (add/remove)

### 3. Dashboard Tests (`dashboard.spec.ts`)
- ✅ Dashboard statistics display
- ✅ Recent and upcoming rentals
- ✅ Revenue and occupancy charts
- ✅ Quick actions navigation
- ✅ Responsive design
- ✅ Real-time data updates

### 4. Profile Management Tests (`profile-management.spec.ts`)
- ✅ Profile information display
- ✅ Basic profile editing
- ✅ Financial information updates
- ✅ Notification settings
- ✅ Form validation
- ✅ Data persistence

### 5. Navigation & Integration Tests (`navigation-integration.spec.ts`)
- ✅ Main navigation between pages
- ✅ Breadcrumb navigation
- ✅ Cross-feature workflows
- ✅ URL state management
- ✅ Error handling and loading states
- ✅ Mobile navigation
- ✅ Accessibility features

## Page Object Models

The tests use the Page Object Model pattern for maintainable and reusable test code:

### BasePage
Common functionality shared across all pages:
- Navigation methods
- Form helpers
- Toast message handling
- Loading state management

### Specific Page Objects
Each page has its own class with methods for:
- Page-specific actions (create, edit, delete)
- Element interactions
- Assertions and expectations
- Data validation

## Test Data Management

### Static Test Data (`fixtures/testData.ts`)
Predefined test data for consistent testing:
- `testProperties`: Sample property data
- `testRentals`: Sample rental data  
- `testProfile`: Sample profile data
- `testGuests`: Sample guest data

### Dynamic Test Data
Helper functions for generating unique test data:
- `generateUniqueProperty()`: Creates unique property data
- `generateUniqueRental()`: Creates unique rental data
- `generateRandomEmail()`: Creates unique email addresses

## Configuration

### Playwright Config (`playwright.config.ts`)
- **Browsers**: Chromium, Firefox, WebKit, Mobile Chrome, Mobile Safari
- **Base URL**: http://localhost:5173
- **Parallel Execution**: Enabled for faster test runs
- **Retries**: 2 retries on CI, 0 locally
- **Screenshots**: On failure only
- **Videos**: Retained on failure
- **Traces**: On first retry

### Global Setup/Teardown
- **Setup**: Waits for backend/frontend to be ready, cleans test data
- **Teardown**: Cleans up test data after all tests complete

## CI/CD Integration

### GitHub Actions (`.github/workflows/e2e-tests.yml`)
Automated E2E testing on:
- Push to main/master/develop branches
- Pull requests to main/master/develop branches

The workflow:
1. Sets up PostgreSQL test database
2. Installs dependencies and builds packages
3. Runs database migrations and seeds
4. Starts backend and frontend servers
5. Runs Playwright tests
6. Uploads test reports and artifacts

## Best Practices

### Test Organization
- Group related tests using `test.describe()`
- Use descriptive test names that explain the expected behavior
- Keep tests independent and isolated
- Use `test.beforeEach()` for common setup

### Data Management
- Use unique test data to avoid conflicts
- Clean up test data after each test
- Use realistic test data that matches production scenarios

### Assertions
- Use specific assertions that clearly indicate what's being tested
- Prefer `expect().toBeVisible()` over `expect().toBeTruthy()`
- Test both positive and negative scenarios

### Error Handling
- Test form validation and error states
- Verify error messages are displayed correctly
- Test recovery from error states

### Performance
- Use `page.waitForLoadState('networkidle')` for dynamic content
- Avoid unnecessary waits with `page.waitForTimeout()`
- Use efficient selectors (data-testid preferred)

## Debugging Tests

### Local Debugging
```bash
# Run with browser visible
npm run test:e2e:headed

# Debug mode with step-by-step execution
npm run test:e2e:debug

# Run specific test with debug
npx playwright test --debug property-management.spec.ts
```

### CI Debugging
- Check uploaded Playwright reports in GitHub Actions artifacts
- Review screenshots and videos from failed tests
- Check console logs and network requests in trace files

## Maintenance

### Adding New Tests
1. Create test data in `fixtures/testData.ts`
2. Add page methods to appropriate Page Object Model
3. Write test specifications following existing patterns
4. Update this README with new test coverage

### Updating Tests
- Keep tests in sync with UI changes
- Update selectors when components change
- Maintain test data when API changes
- Review and update assertions for new features

## Troubleshooting

### Common Issues

**Tests timing out**
- Increase timeout in playwright.config.ts
- Check if servers are running properly
- Verify network connectivity

**Element not found**
- Check if data-testid attributes exist
- Verify element is visible and not hidden
- Use browser dev tools to inspect elements

**Flaky tests**
- Add proper wait conditions
- Use more specific selectors
- Check for race conditions in async operations

**Database issues**
- Ensure test database is properly seeded
- Check database connection in backend
- Verify test data cleanup is working

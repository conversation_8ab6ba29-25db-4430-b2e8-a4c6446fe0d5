import { test, expect } from '@playwright/test';
import { loginAsRole, TEST_USERS, setupTestUsers, cleanupTestUsers } from './fixtures/auth';

test.describe('US-URP-009: User Account Management', () => {
  test.beforeAll(async ({ browser }) => {
    // Setup test users for all role-based tests
    const context = await browser.newContext();
    const page = await context.newPage();
    await setupTestUsers(page);
    await context.close();
  });

  test.afterAll(async ({ browser }) => {
    // Cleanup test users after all tests
    const context = await browser.newContext();
    const page = await context.newPage();
    await cleanupTestUsers(page);
    await context.close();
  });

  test('admin can create new user accounts with specified roles', async ({ page }) => {
    // Login as admin
    await loginAsRole(page, 'ADMIN');

    // Navigate to admin page
    await page.goto('/admin');

    // Wait for admin page to load
    await page.waitForSelector('[data-testid="admin-dashboard"]', { timeout: 10000 });

    // Click on create user button
    const createUserButton = page.locator('[data-testid="create-user-btn"]');
    await expect(createUserButton).toBeVisible();
    await createUserButton.click();

    // Fill in user creation form
    const testUser = {
      email: '<EMAIL>',
      fullName: 'New Test User',
      password: 'newuserpassword123',
      role: 'OPERATOR',
    };

    await page.fill('[data-testid="user-email-input"]', testUser.email);
    await page.fill('[data-testid="user-fullname-input"]', testUser.fullName);
    await page.fill('[data-testid="user-password-input"]', testUser.password);

    // Select role
    await page.selectOption('[data-testid="user-role-select"]', testUser.role);

    // Submit form
    await page.click('[data-testid="create-user-submit-btn"]');

    // Wait for success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();

    // Verify user appears in users list
    const userRow = page.locator(`[data-testid="user-row-${testUser.email}"]`);
    await expect(userRow).toBeVisible();

    // Verify user has correct role
    const roleCell = userRow.locator('[data-testid="user-role-cell"]');
    await expect(roleCell).toContainText(testUser.role);
  });

  test('admin can activate and deactivate user accounts', async ({ page }) => {
    // Login as admin
    await loginAsRole(page, 'ADMIN');

    // Navigate to admin page
    await page.goto('/admin');

    // Wait for users list to load
    await page.waitForSelector('[data-testid="users-list"]', { timeout: 10000 });

    // Find a user to deactivate (not the current admin)
    const userRows = page.locator('[data-testid^="user-row-"]');
    const userCount = await userRows.count();

    if (userCount > 1) {
      // Get the first non-admin user
      const targetUserRow = userRows.nth(1);

      // Click on user actions menu
      const actionsButton = targetUserRow.locator('[data-testid="user-actions-btn"]');
      await actionsButton.click();

      // Click deactivate
      const deactivateButton = page.locator('[data-testid="deactivate-user-btn"]');
      await expect(deactivateButton).toBeVisible();
      await deactivateButton.click();

      // Confirm deactivation
      const confirmButton = page.locator('[data-testid="confirm-deactivate-btn"]');
      await expect(confirmButton).toBeVisible();
      await confirmButton.click();

      // Wait for success message
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();

      // Verify user status changed to inactive
      const statusCell = targetUserRow.locator('[data-testid="user-status-cell"]');
      await expect(statusCell).toContainText('Inactive');

      // Now reactivate the user
      await actionsButton.click();
      const activateButton = page.locator('[data-testid="activate-user-btn"]');
      await expect(activateButton).toBeVisible();
      await activateButton.click();

      // Confirm activation
      const confirmActivateButton = page.locator('[data-testid="confirm-activate-btn"]');
      await expect(confirmActivateButton).toBeVisible();
      await confirmActivateButton.click();

      // Wait for success message
      await expect(page.locator('[data-testid="success-message"]')).toBeVisible();

      // Verify user status changed back to active
      await expect(statusCell).toContainText('Active');
    }
  });

  test('admin can delete user accounts with confirmation', async ({ page }) => {
    // Login as admin
    await loginAsRole(page, 'ADMIN');

    // Navigate to admin page
    await page.goto('/admin');

    // Wait for users list to load
    await page.waitForSelector('[data-testid="users-list"]', { timeout: 10000 });

    // Create a test user to delete
    const createUserButton = page.locator('[data-testid="create-user-btn"]');
    await createUserButton.click();

    const testUser = {
      email: '<EMAIL>',
      fullName: 'Delete Me User',
      password: 'deletepassword123',
      role: 'CUSTOMER',
    };

    await page.fill('[data-testid="user-email-input"]', testUser.email);
    await page.fill('[data-testid="user-fullname-input"]', testUser.fullName);
    await page.fill('[data-testid="user-password-input"]', testUser.password);
    await page.selectOption('[data-testid="user-role-select"]', testUser.role);
    await page.click('[data-testid="create-user-submit-btn"]');

    // Wait for user to be created
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();

    // Find the created user and delete it
    const userRow = page.locator(`[data-testid="user-row-${testUser.email}"]`);
    await expect(userRow).toBeVisible();

    // Click on user actions menu
    const actionsButton = userRow.locator('[data-testid="user-actions-btn"]');
    await actionsButton.click();

    // Click delete
    const deleteButton = page.locator('[data-testid="delete-user-btn"]');
    await expect(deleteButton).toBeVisible();
    await deleteButton.click();

    // Verify confirmation dialog appears
    const confirmDialog = page.locator('[data-testid="delete-user-dialog"]');
    await expect(confirmDialog).toBeVisible();

    // Verify warning message
    const warningMessage = confirmDialog.locator('[data-testid="delete-warning"]');
    await expect(warningMessage).toContainText('permanently delete');

    // Confirm deletion
    const confirmDeleteButton = page.locator('[data-testid="confirm-delete-btn"]');
    await confirmDeleteButton.click();

    // Wait for success message
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();

    // Verify user is removed from list
    await expect(userRow).toHaveCount(0);
  });

  test('admin can view user login history and account status', async ({ page }) => {
    // Login as admin
    await loginAsRole(page, 'ADMIN');

    // Navigate to admin page
    await page.goto('/admin');

    // Wait for users list to load
    await page.waitForSelector('[data-testid="users-list"]', { timeout: 10000 });

    // Click on a user to view details
    const userRows = page.locator('[data-testid^="user-row-"]');
    if ((await userRows.count()) > 0) {
      const firstUser = userRows.first();
      await firstUser.click();

      // Verify user details modal/page opens
      const userDetailsModal = page.locator('[data-testid="user-details-modal"]');
      await expect(userDetailsModal).toBeVisible();

      // Verify account status is displayed
      const accountStatus = page.locator('[data-testid="account-status"]');
      await expect(accountStatus).toBeVisible();

      // Verify login history section exists
      const loginHistory = page.locator('[data-testid="login-history"]');
      await expect(loginHistory).toBeVisible();

      // Verify last login information
      const lastLogin = page.locator('[data-testid="last-login"]');
      await expect(lastLogin).toBeVisible();
    }
  });

  test('deactivated users cannot log in', async ({ page }) => {
    // First, login as admin and deactivate a test user
    await loginAsRole(page, 'ADMIN');
    await page.goto('/admin');

    // Create a test user to deactivate
    const createUserButton = page.locator('[data-testid="create-user-btn"]');
    await createUserButton.click();

    const testUser = {
      email: '<EMAIL>',
      fullName: 'Deactivated User',
      password: 'deactivatedpassword123',
      role: 'CUSTOMER',
    };

    await page.fill('[data-testid="user-email-input"]', testUser.email);
    await page.fill('[data-testid="user-fullname-input"]', testUser.fullName);
    await page.fill('[data-testid="user-password-input"]', testUser.password);
    await page.selectOption('[data-testid="user-role-select"]', testUser.role);
    await page.click('[data-testid="create-user-submit-btn"]');

    // Wait for user creation
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();

    // Deactivate the user
    const userRow = page.locator(`[data-testid="user-row-${testUser.email}"]`);
    const actionsButton = userRow.locator('[data-testid="user-actions-btn"]');
    await actionsButton.click();
    await page.click('[data-testid="deactivate-user-btn"]');
    await page.click('[data-testid="confirm-deactivate-btn"]');

    // Wait for deactivation
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();

    // Logout admin
    await page.click('[data-testid="logout-btn"]');

    // Try to login as deactivated user
    await page.goto('/auth');
    await page.click('text=Sign In');
    await page.fill('input[id="email"]', testUser.email);
    await page.fill('input[id="password"]', testUser.password);
    await page.click('button[type="submit"]');

    // Should see error message about account being deactivated
    const errorMessage = page.locator('[data-testid="error-message"]');
    await expect(errorMessage).toBeVisible();
    await expect(errorMessage).toContainText('deactivated');
  });

  test('admin cannot delete their own account', async ({ page }) => {
    // Login as admin
    await loginAsRole(page, 'ADMIN');

    // Navigate to admin page
    await page.goto('/admin');

    // Wait for users list to load
    await page.waitForSelector('[data-testid="users-list"]', { timeout: 10000 });

    // Find the current admin user row
    const adminUserRow = page.locator(`[data-testid="user-row-${TEST_USERS.ADMIN.email}"]`);

    if ((await adminUserRow.count()) > 0) {
      // Click on admin user actions
      const actionsButton = adminUserRow.locator('[data-testid="user-actions-btn"]');
      await actionsButton.click();

      // Delete button should not be present or should be disabled
      const deleteButton = page.locator('[data-testid="delete-user-btn"]');

      if ((await deleteButton.count()) > 0) {
        // If button exists, it should be disabled
        await expect(deleteButton).toBeDisabled();
      } else {
        // Button should not exist at all
        await expect(deleteButton).toHaveCount(0);
      }
    }
  });
});

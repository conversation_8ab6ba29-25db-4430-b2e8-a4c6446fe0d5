export const testProperties = {
  apartment: {
    name: 'Downtown Apartment',
    address: '123 Main St, City, State 12345',
    type: 'residential',
    purchasePrice: '250000',
    purchaseDate: '2023-01-15',
    description: 'Modern apartment in downtown area',
  },
  house: {
    name: 'Suburban House',
    address: '456 Oak Ave, Suburb, State 67890',
    type: 'residential',
    purchasePrice: '450000',
    purchaseDate: '2022-06-20',
    description: 'Family house with garden',
  },
  condo: {
    name: 'Beach Condo',
    address: '789 Beach Blvd, Coastal City, State 11111',
    type: 'commercial',
    purchasePrice: '350000',
    purchaseDate: '2023-03-10',
    description: 'Oceanview condo near the beach',
  },
};

// Helper function to get future dates
const getFutureDate = (daysFromNow: number): string => {
  const date = new Date();
  date.setDate(date.getDate() + daysFromNow);
  return date.toISOString().split('T')[0];
};

export const testRentals = {
  activeRental: {
    property: 'Downtown Apartment',
    startDate: getFutureDate(1), // Tomorrow
    endDate: getFutureDate(7), // Next week
    rentalAmount: '1200',
    costAmount: '200',
    status: 'active',
    isPaid: true,
    guestName: 'John Doe',
    guestEmail: '<EMAIL>',
  },
  upcomingRental: {
    property: 'Suburban House',
    startDate: getFutureDate(15), // 15 days from now
    endDate: getFutureDate(22), // 22 days from now
    rentalAmount: '1800',
    costAmount: '300',
    status: 'active',
    isPaid: false,
    guestName: 'Jane Smith',
    guestEmail: '<EMAIL>',
  },
  endedRental: {
    property: 'Beach Condo',
    startDate: getFutureDate(30), // 30 days from now
    endDate: getFutureDate(37), // 37 days from now
    rentalAmount: '1500',
    costAmount: '250',
    status: 'ended',
    isPaid: true,
    guestName: 'Bob Johnson',
    guestEmail: '<EMAIL>',
  },
};

export const testProfile = {
  basic: {
    name: 'Test User',
    email: '<EMAIL>',
    age: '30',
    salary: '75000',
    savingsAmount: '50000',
    investmentRiskLevel: 'medium',
  },
  updated: {
    name: 'Updated Test User',
    email: '<EMAIL>',
    age: '32',
    salary: '85000',
    savingsAmount: '60000',
    investmentRiskLevel: 'high',
  },
  notifications: {
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
  },
};

export const testGuests = {
  guest1: {
    name: 'Alice Cooper',
    email: '<EMAIL>',
  },
  guest2: {
    name: 'Charlie Brown',
    email: '<EMAIL>',
  },
  guest3: {
    name: 'Diana Prince',
    email: '<EMAIL>',
  },
};

export const testCommissions = {
  standard: {
    percentage: '10',
    flatFee: '50',
    type: 'percentage',
  },
  premium: {
    percentage: '15',
    flatFee: '100',
    type: 'flat',
  },
};

export const testPartners = {
  partner1: {
    name: 'Business Partner 1',
    email: '<EMAIL>',
  },
  partner2: {
    name: 'Business Partner 2',
    email: '<EMAIL>',
  },
};

// Helper functions for generating dynamic test data
export const generateUniqueProperty = (suffix: string = '') => ({
  name: `Test Property ${suffix} ${Date.now()}`,
  address: `${Math.floor(Math.random() * 9999)} Test St, Test City, TS 12345`,
  type: 'apartment',
  purchasePrice: (Math.floor(Math.random() * 500000) + 100000).toString(),
  purchaseDate: '2023-01-01',
  description: `Test property description ${suffix}`,
});

export const generateUniqueRental = (propertyName: string, suffix: string = '') => ({
  property: propertyName,
  startDate: getFutureDate(Math.floor(Math.random() * 30) + 1), // 1-30 days from now
  endDate: getFutureDate(Math.floor(Math.random() * 30) + 32), // 32-62 days from now
  rentalAmount: (Math.floor(Math.random() * 2000) + 500).toString(),
  costAmount: (Math.floor(Math.random() * 500) + 100).toString(),
  status: 'active',
  isPaid: Math.random() > 0.5,
  guestName: `Test Guest ${suffix} ${Date.now()}`,
  guestEmail: `test.guest.${suffix}.${Date.now()}@example.com`,
});

export const generateRandomEmail = () => `test.${Date.now()}@example.com`;
export const generateRandomName = () => `Test User ${Date.now()}`;
export const generateRandomAmount = (min: number = 100, max: number = 10000) => (Math.floor(Math.random() * (max - min)) + min).toString();

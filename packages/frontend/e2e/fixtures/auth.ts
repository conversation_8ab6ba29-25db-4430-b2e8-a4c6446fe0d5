import { Page } from '@playwright/test';
import { UserRole } from '@alom-rentals/shared';

// Test user credentials for different roles
export const TEST_USERS = {
  ADMIN: {
    email: '<EMAIL>',
    password: 'adminpassword123',
    fullName: 'Admin User',
    role: 'ADMIN' as UserRole,
  },
  OPERATOR: {
    email: '<EMAIL>',
    password: 'operatorpassword123',
    fullName: 'Operator User',
    role: 'OPERATOR' as User<PERSON>ole,
  },
  CUSTOMER: {
    email: '<EMAIL>',
    password: 'customerpassword123',
    fullName: 'Customer User',
    role: 'CUSTOMER' as UserRole,
  },
};

// Backward compatibility
export const TEST_USER = TEST_USERS.CUSTOMER;

/**
 * Login helper for tests that need fresh authentication
 */
export async function login(page: Page, credentials = TEST_USER) {
  await page.goto('/auth');

  // Wait for the page to load
  await page.waitForSelector('form', { timeout: 10000 });

  // Check if we need to switch to sign in mode
  const signInLink = page.locator('text="Already have an account? Sign in"');

  if (await signInLink.isVisible()) {
    // We're in sign up mode, switch to sign in
    await signInLink.click();
    await page.waitForTimeout(500); // Wait for form to switch
  }

  // Wait for email input to be visible
  await page.waitForSelector('input[id="email"]', { timeout: 5000 });

  // Fill login form
  await page.fill('input[id="email"]', credentials.email);
  await page.fill('input[id="password"]', credentials.password);

  // Submit login
  await page.click('button[type="submit"]');

  // Wait for redirect to dashboard
  await page.waitForURL('**/dashboard', { timeout: 10000 });
}

/**
 * Login as specific role helper
 */
export async function loginAsRole(page: Page, role: UserRole) {
  const credentials = TEST_USERS[role];
  await login(page, credentials);
}

/**
 * Create test user with specific role (requires admin privileges)
 */
export async function createTestUser(page: Page, userRole: UserRole, adminCredentials = TEST_USERS.ADMIN) {
  // First login as admin
  await login(page, adminCredentials);

  // Navigate to admin page
  await page.goto('/admin');

  // Create user logic would go here - this will be implemented when we add the user creation functionality
  // For now, we'll use the registration endpoint directly
  const credentials = TEST_USERS[userRole];

  // Use API to create user and then change role if needed
  const response = await page.request.post('/api/auth/register', {
    data: {
      email: credentials.email,
      password: credentials.password,
      fullName: credentials.fullName,
    },
  });

  if (response.ok()) {
    const userData = await response.json();

    // If not customer role, change the role via admin API
    if (userRole !== 'CUSTOMER') {
      await page.request.patch(`/api/admin/users/${userData.user.id}/role`, {
        data: { role: userRole },
      });
    }
  }

  return response.ok();
}

/**
 * Register helper for tests that need a new user
 */
export async function register(page: Page, credentials = TEST_USER) {
  await page.goto('/auth');

  // Click sign up tab
  const signUpButton = page.locator('text=Sign Up');
  if (await signUpButton.isVisible()) {
    await signUpButton.click();
  }

  // Fill registration form
  await page.fill('input[id="fullName"]', credentials.fullName);
  await page.fill('input[id="email"]', credentials.email);
  await page.fill('input[id="password"]', credentials.password);

  // Submit registration
  await page.click('button[type="submit"]');

  // Wait for redirect to dashboard
  await page.waitForURL('**/dashboard', { timeout: 10000 });
}

/**
 * Logout helper
 */
export async function logout(page: Page) {
  // Look for logout button/menu
  const userMenu = page.locator('[data-testid="user-menu"]');
  if (await userMenu.isVisible()) {
    await userMenu.click();
    await page.click('[data-testid="logout-btn"]');
  } else {
    // Fallback: clear localStorage and reload
    await page.evaluate(() => {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
    });
    await page.reload();
  }

  // Wait for redirect to auth page
  await page.waitForURL('**/auth', { timeout: 5000 });
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    const token = await page.evaluate(() => localStorage.getItem('token'));
    return !!token;
  } catch {
    return false;
  }
}

/**
 * Get current user from localStorage
 */
export async function getCurrentUser(page: Page) {
  try {
    const userStr = await page.evaluate(() => localStorage.getItem('user'));
    return userStr ? JSON.parse(userStr) : null;
  } catch {
    return null;
  }
}

/**
 * Setup test users for role-based testing
 */
export async function setupTestUsers(page: Page) {
  const results = {
    admin: false,
    operator: false,
    customer: false,
  };

  try {
    // Create admin user first
    const adminResponse = await page.request.post('/api/auth/register', {
      data: {
        email: TEST_USERS.ADMIN.email,
        password: TEST_USERS.ADMIN.password,
        fullName: TEST_USERS.ADMIN.fullName,
      },
    });

    if (adminResponse.ok()) {
      const adminData = await adminResponse.json();
      // Change role to admin
      await page.request.patch(`/api/admin/users/${adminData.user.id}/role`, {
        data: { role: 'ADMIN' },
      });
      results.admin = true;
    }

    // Create operator user
    const operatorResponse = await page.request.post('/api/auth/register', {
      data: {
        email: TEST_USERS.OPERATOR.email,
        password: TEST_USERS.OPERATOR.password,
        fullName: TEST_USERS.OPERATOR.fullName,
      },
    });

    if (operatorResponse.ok()) {
      const operatorData = await operatorResponse.json();
      // Change role to operator
      await page.request.patch(`/api/admin/users/${operatorData.user.id}/role`, {
        data: { role: 'OPERATOR' },
      });
      results.operator = true;
    }

    // Create customer user (default role)
    const customerResponse = await page.request.post('/api/auth/register', {
      data: {
        email: TEST_USERS.CUSTOMER.email,
        password: TEST_USERS.CUSTOMER.password,
        fullName: TEST_USERS.CUSTOMER.fullName,
      },
    });

    if (customerResponse.ok()) {
      results.customer = true;
    }
  } catch (error) {
    console.error('Error setting up test users:', error);
  }

  return results;
}

/**
 * Clean up test users
 */
export async function cleanupTestUsers(page: Page) {
  try {
    // Login as admin to clean up
    await login(page, TEST_USERS.ADMIN);

    // Get all users and delete test users
    const usersResponse = await page.request.get('/api/admin/users');
    if (usersResponse.ok()) {
      const users = await usersResponse.json();
      const testEmails = Object.values(TEST_USERS).map((u) => u.email);

      for (const user of users) {
        if (testEmails.includes(user.email)) {
          // Delete user - this endpoint will need to be implemented
          await page.request.delete(`/api/admin/users/${user._id}`);
        }
      }
    }
  } catch (error) {
    console.error('Error cleaning up test users:', error);
  }
}

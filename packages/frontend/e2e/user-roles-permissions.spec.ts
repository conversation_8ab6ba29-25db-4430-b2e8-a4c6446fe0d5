import { test, expect } from '@playwright/test';
import { login, TEST_USER } from './fixtures/auth';

test.describe('User Roles and Permissions', () => {
  // Use existing test user (customer role) for read-only tests
  // No setup/cleanup needed since we use the existing test user

  test.describe('US-URP-007: Customer Read-Only Access', () => {
    test('customer can view properties but cannot edit them', async ({ page }) => {
      // Login as customer (existing test user)
      await login(page, TEST_USER);

      // Navigate to properties page
      await page.goto('/properties');

      // Wait for properties to load
      await page.waitForSelector('[data-testid="properties-list"]', { timeout: 10000 });

      // Verify customer can see properties
      const propertiesExist = await page.locator('[data-testid="property-card"]').count();

      if (propertiesExist > 0) {
        // Check that edit buttons are hidden for customers
        const editButtons = page.locator('[data-testid="edit-property-btn"]');
        await expect(editButtons).toHaveCount(0);

        // Check that delete buttons are hidden for customers
        const deleteButtons = page.locator('[data-testid="delete-property-btn"]');
        await expect(deleteButtons).toHaveCount(0);

        // Click on a property to view details
        await page.locator('[data-testid="property-card"]').first().click();

        // Verify edit button is not visible in property details
        const detailEditButton = page.locator('[data-testid="property-detail-edit-btn"]');
        await expect(detailEditButton).toHaveCount(0);
      }

      // Verify create property button is hidden
      const createButton = page.locator('[data-testid="create-property-btn"]');
      await expect(createButton).toHaveCount(0);
    });

    test('customer can view guests but cannot modify them', async ({ page }) => {
      // Login as customer
      await login(page, TEST_USER);

      // Navigate to guests page
      await page.goto('/guests');

      // Wait for guests to load
      await page.waitForSelector('[data-testid="guests-list"]', { timeout: 10000 });

      // Verify create guest button is hidden
      const createButton = page.locator('[data-testid="create-guest-btn"]');
      await expect(createButton).toHaveCount(0);

      // Check if guests exist
      const guestsExist = await page.locator('[data-testid="guest-row"]').count();

      if (guestsExist > 0) {
        // Check that edit buttons are hidden for customers
        const editButtons = page.locator('[data-testid="edit-guest-btn"]');
        await expect(editButtons).toHaveCount(0);

        // Check that delete buttons are hidden for customers
        const deleteButtons = page.locator('[data-testid="delete-guest-btn"]');
        await expect(deleteButtons).toHaveCount(0);
      }
    });

    test('customer can view rentals but cannot modify them', async ({ page }) => {
      // Login as customer
      await login(page, TEST_USER);

      // Navigate to rentals page
      await page.goto('/rentals');

      // Wait for rentals to load
      await page.waitForSelector('[data-testid="rentals-container"]', { timeout: 10000 });

      // Verify create rental button is hidden
      const createButton = page.locator('[data-testid="create-rental-btn"]');
      await expect(createButton).toHaveCount(0);

      // Check if rentals exist
      const rentalsExist = await page.locator('[data-testid="rental-item"]').count();

      if (rentalsExist > 0) {
        // Check that edit buttons are hidden for customers
        const editButtons = page.locator('[data-testid="edit-rental-btn"]');
        await expect(editButtons).toHaveCount(0);

        // Check that delete buttons are hidden for customers
        const deleteButtons = page.locator('[data-testid="delete-rental-btn"]');
        await expect(deleteButtons).toHaveCount(0);
      }
    });

    test('customer sees read-only messaging', async ({ page }) => {
      // Login as customer
      await login(page, TEST_USER);

      // Navigate to properties page
      await page.goto('/properties');

      // Look for read-only indicator or message
      const readOnlyMessage = page.locator('[data-testid="read-only-message"]');
      await expect(readOnlyMessage).toBeVisible();

      // Verify the message content
      await expect(readOnlyMessage).toContainText('read-only');
    });

    test('customer can access detailed views and reports', async ({ page }) => {
      // Login as customer
      await login(page, TEST_USER);

      // Navigate to dashboard to check reports access
      await page.goto('/dashboard');

      // Wait for dashboard to load
      await page.waitForSelector('[data-testid="dashboard-content"]', { timeout: 10000 });

      // Verify customer can see financial reports/charts
      const financialCharts = page.locator('[data-testid="financial-chart"]');
      const reportSections = page.locator('[data-testid="report-section"]');

      // At least one of these should be visible
      const hasFinancialData = (await financialCharts.count()) > 0 || (await reportSections.count()) > 0;
      expect(hasFinancialData).toBeTruthy();
    });

    test('customer cannot access admin dashboard', async ({ page }) => {
      // Login as customer
      await login(page, TEST_USER);

      // Try to navigate to admin page
      await page.goto('/admin');

      // Should be redirected or see access denied
      await page.waitForTimeout(2000);

      // Check if we're still on admin page or redirected
      const currentUrl = page.url();
      const isOnAdminPage = currentUrl.includes('/admin');

      if (isOnAdminPage) {
        // If still on admin page, should see access denied message
        const accessDeniedMessage = page.locator('[data-testid="access-denied"]');
        await expect(accessDeniedMessage).toBeVisible();
      } else {
        // Should be redirected away from admin page
        expect(isOnAdminPage).toBeFalsy();
      }
    });
  });

  test.describe('US-URP-008: Role-Based Navigation', () => {
    test('customer sees limited navigation options', async ({ page }) => {
      // Login as customer (existing test user)
      await login(page, TEST_USER);

      // Navigate to dashboard
      await page.goto('/dashboard');

      // Wait for navigation to load
      await page.waitForSelector('[data-testid="sidebar"]', { timeout: 10000 });

      // Verify customer sees appropriate navigation
      const expectedCustomerNavItems = [
        'Dashboard',
        'Properties',
        'Partners',
        'Guests',
        'Currencies',
        'Rentals',
        'Recurrent Costs',
        'Personal Finance',
        'Settings',
      ];

      for (const navItem of expectedCustomerNavItems) {
        const navByText = page.locator(`text="${navItem}"`);
        await expect(navByText).toBeVisible();
      }

      // Verify customer does NOT see admin dashboard
      const adminDashboardLink = page.locator('text="Admin Dashboard"');
      await expect(adminDashboardLink).toHaveCount(0);

      // Verify customer does NOT see customer selector
      const customerSelector = page.locator('[data-testid="customer-selector"]');
      await expect(customerSelector).toHaveCount(0);
    });

    test('unauthorized menu items are completely hidden', async ({ page }) => {
      // Login as customer
      await login(page, TEST_USER);

      // Navigate to dashboard
      await page.goto('/dashboard');

      // Wait for navigation to load
      await page.waitForSelector('[data-testid="sidebar"]', { timeout: 10000 });

      // Check that admin dashboard is not just disabled, but completely hidden
      const adminDashboardLink = page.locator('text="Admin Dashboard"');
      await expect(adminDashboardLink).toHaveCount(0);

      // Verify no disabled admin links exist
      const disabledAdminLinks = page.locator('[data-testid*="admin"][disabled]');
      await expect(disabledAdminLinks).toHaveCount(0);
    });
  });
});

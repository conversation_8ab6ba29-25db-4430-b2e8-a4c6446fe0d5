import { test, expect } from '@playwright/test';
import { PropertiesPage } from './pages/PropertiesPage';
import { generateUniqueProperty } from './fixtures/testData';

test.describe('Simple Bulk Delete Test', () => {
  let propertiesPage: PropertiesPage;

  test.beforeEach(async ({ page }) => {
    propertiesPage = new PropertiesPage(page);
    await propertiesPage.goto();
  });

  test('should be able to select and delete properties using labels', async ({ page }) => {
    // Create a test property
    const property = generateUniqueProperty('simple-bulk-test');
    await propertiesPage.createProperty(property);
    await propertiesPage.goto();

    // Wait for property to appear
    await propertiesPage.expectPropertyExists(property.name);

    // Take a screenshot to see the current state
    await page.screenshot({ path: 'test-results/before-selection.png', fullPage: true });

    // Try to find and click the checkbox label for the property
    const propertyRow = page.locator('table tbody tr').filter({ hasText: property.name });
    await expect(propertyRow).toBeVisible();

    // Look for the checkbox label in the row
    const checkboxLabel = propertyRow.locator('td').first().locator('label');
    await expect(checkboxLabel).toBeVisible();

    // Click the checkbox label
    await checkboxLabel.click();

    // Take a screenshot after selection
    await page.screenshot({ path: 'test-results/after-selection.png', fullPage: true });

    // Check if the checkbox is now checked
    const checkbox = propertyRow.locator('input[type="checkbox"]');
    await expect(checkbox).toBeChecked();

    // Look for selection controls
    const selectionText = page.locator('text=1 selected');
    if ((await selectionText.count()) > 0) {
      await expect(selectionText).toBeVisible();

      // Look for delete button
      const deleteButton = page.locator('button').filter({ hasText: /Delete 1/ });
      if ((await deleteButton.count()) > 0) {
        await deleteButton.click();

        // Look for confirmation modal
        const confirmModal = page.locator('text=Delete Properties');
        if ((await confirmModal.count()) > 0) {
          await expect(confirmModal).toBeVisible();

          // Confirm deletion
          const confirmButton = page.locator('button').filter({ hasText: /Delete.*Properties/ });
          await confirmButton.click();

          // Wait for deletion to complete
          await page.waitForTimeout(2000);

          // Property should be gone
          await expect(propertyRow).not.toBeVisible();
        } else {
          console.log('Confirmation modal not found');
        }
      } else {
        console.log('Delete button not found');
      }
    } else {
      console.log('Selection text not found - checking what is visible');

      // Debug: log all visible text
      const allText = await page.textContent('body');
      console.log('Page content:', allText?.substring(0, 500));
    }
  });

  test('should show table structure correctly', async ({ page }) => {
    // Create a test property
    const property = generateUniqueProperty('structure-test');
    await propertiesPage.createProperty(property);
    await propertiesPage.goto();

    // Wait for property to appear
    await propertiesPage.expectPropertyExists(property.name);

    // Check table structure
    const table = page.locator('table');
    await expect(table).toBeVisible();

    const thead = table.locator('thead');
    await expect(thead).toBeVisible();

    const tbody = table.locator('tbody');
    await expect(tbody).toBeVisible();

    // Check if there are checkboxes in the header
    const headerCheckbox = thead.locator('input[type="checkbox"]');
    if ((await headerCheckbox.count()) > 0) {
      console.log('Header checkbox found');

      // Check if there's a label for it
      const headerLabel = thead.locator('label');
      if ((await headerLabel.count()) > 0) {
        console.log('Header label found');
        await expect(headerLabel).toBeVisible();
      } else {
        console.log('Header label NOT found');
      }
    } else {
      console.log('Header checkbox NOT found');
    }

    // Check if there are checkboxes in the body
    const bodyCheckboxes = tbody.locator('input[type="checkbox"]');
    const checkboxCount = await bodyCheckboxes.count();
    console.log(`Found ${checkboxCount} checkboxes in table body`);

    if (checkboxCount > 0) {
      // Check if there are labels for them
      const bodyLabels = tbody.locator('label');
      const labelCount = await bodyLabels.count();
      console.log(`Found ${labelCount} labels in table body`);
    }

    // Take a screenshot for debugging
    await page.screenshot({ path: 'test-results/table-structure.png', fullPage: true });
  });
});

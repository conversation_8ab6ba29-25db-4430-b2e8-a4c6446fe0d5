import { test, expect } from '@playwright/test';
import { PropertiesPage } from './pages/PropertiesPage';
import { generateUniqueProperty } from './fixtures/testData';

test.describe('Bulk Property Operations', () => {
  let propertiesPage: PropertiesPage;

  test.beforeEach(async ({ page }) => {
    propertiesPage = new PropertiesPage(page);
    await propertiesPage.goto();
  });

  test.describe('Bulk Selection and Deletion', () => {
    test('should select and delete all properties on first page', async ({ page }) => {
      // First, create multiple properties to test with
      const propertiesToCreate = [
        generateUniqueProperty('bulk-test-1'),
        generateUniqueProperty('bulk-test-2'),
        generateUniqueProperty('bulk-test-3'),
        generateUniqueProperty('bulk-test-4'),
        generateUniqueProperty('bulk-test-5'),
      ];

      // Create all test properties
      for (const property of propertiesToCreate) {
        await propertiesPage.createProperty(property);
        await propertiesPage.goto(); // Go back to list after each creation
      }

      // Wait for properties to load
      await page.waitForSelector('table tbody tr', { timeout: 10000 });

      // Check if we have properties to work with
      const initialPropertyCount = await page.locator('table tbody tr').count();
      console.log(`Found ${initialPropertyCount} properties on the page`);

      if (initialPropertyCount === 0) {
        console.log('No properties found, skipping bulk delete test');
        return;
      }

      // Check if the table has selection checkboxes - click on the label instead of hidden input
      const selectAllCheckboxLabel = page.locator('table thead th label').first();
      await expect(selectAllCheckboxLabel).toBeVisible({ timeout: 5000 });

      // Click select all checkbox label
      await selectAllCheckboxLabel.click();

      // Verify all visible properties are selected
      const propertyCheckboxes = page.locator('table tbody tr input[type="checkbox"]');
      const checkboxCount = await propertyCheckboxes.count();

      for (let i = 0; i < checkboxCount; i++) {
        await expect(propertyCheckboxes.nth(i)).toBeChecked();
      }

      // Check if selection controls are visible
      await expect(page.locator('text*=selected')).toBeVisible();

      // Look for bulk delete button
      const bulkDeleteButton = page.locator('button:has-text("Delete"), button:has(.lucide-trash2)').filter({ hasText: /Delete \d+/ });
      await expect(bulkDeleteButton).toBeVisible();

      // Click bulk delete button
      await bulkDeleteButton.click();

      // Wait for bulk delete confirmation modal
      await expect(page.locator('text=Delete Properties')).toBeVisible({ timeout: 5000 });
      await expect(page.locator('text*=Are you sure you want to delete')).toBeVisible();

      // Confirm bulk deletion
      const confirmButton = page.locator('button:has-text("Delete"):not(:has-text("Cancel"))').last();
      await confirmButton.click();

      // Wait for deletion to complete
      await page.waitForTimeout(2000);

      // Verify properties were deleted
      const finalPropertyCount = await page.locator('table tbody tr').count();
      console.log(`Properties after deletion: ${finalPropertyCount}`);

      // The count should be less than initial (or show empty state)
      expect(finalPropertyCount).toBeLessThan(initialPropertyCount);
    });

    test('should handle individual property selection', async ({ page }) => {
      // Create a test property
      const property = generateUniqueProperty('individual-select');
      await propertiesPage.createProperty(property);
      await propertiesPage.goto();

      // Wait for property to appear
      await propertiesPage.expectPropertyExists(property.name);

      // Find the property row and its checkbox label
      const propertyRow = page.locator('table tbody tr').filter({ hasText: property.name });
      const propertyCheckboxLabel = propertyRow.locator('label').first();
      const propertyCheckbox = propertyRow.locator('input[type="checkbox"]');

      // Select the individual property by clicking the label
      await propertyCheckboxLabel.click();
      await expect(propertyCheckbox).toBeChecked();

      // Verify selection controls appear
      await expect(page.locator('text=1 selected')).toBeVisible();
      await expect(page.locator('button:has-text("Delete 1")')).toBeVisible();

      // Test deselection by clicking label again
      await propertyCheckboxLabel.click();
      await expect(propertyCheckbox).not.toBeChecked();

      // Selection controls should disappear
      await expect(page.locator('text*=selected')).not.toBeVisible();
    });

    test('should show select all/none buttons when properties are selected', async ({ page }) => {
      // Create test properties
      const properties = [generateUniqueProperty('select-all-1'), generateUniqueProperty('select-all-2')];

      for (const property of properties) {
        await propertiesPage.createProperty(property);
        await propertiesPage.goto();
      }

      // Select first property by clicking its label
      const firstPropertyRow = page.locator('table tbody tr').first();
      const firstCheckboxLabel = firstPropertyRow.locator('label').first();
      await firstCheckboxLabel.click();

      // Verify select all/none buttons appear
      await expect(page.locator('button:has-text("Select All")')).toBeVisible();
      await expect(page.locator('button:has-text("Select None")')).toBeVisible();

      // Test Select All button
      await page.locator('button:has-text("Select All")').click();

      // All checkboxes should be selected
      const allCheckboxes = page.locator('table tbody tr input[type="checkbox"]');
      const checkboxCount = await allCheckboxes.count();

      for (let i = 0; i < checkboxCount; i++) {
        await expect(allCheckboxes.nth(i)).toBeChecked();
      }

      // Test Select None button
      await page.locator('button:has-text("Select None")').click();

      // All checkboxes should be deselected
      for (let i = 0; i < checkboxCount; i++) {
        await expect(allCheckboxes.nth(i)).not.toBeChecked();
      }
    });
  });

  test.describe('Pagination Features', () => {
    test('should show pagination when there are many properties', async ({ page }) => {
      // Create enough properties to trigger pagination (more than 10)
      const propertiesToCreate = Array.from({ length: 12 }, (_, i) => generateUniqueProperty(`pagination-test-${i + 1}`));

      // Create properties in batches to avoid timeout
      for (let i = 0; i < propertiesToCreate.length; i += 3) {
        const batch = propertiesToCreate.slice(i, i + 3);
        for (const property of batch) {
          await propertiesPage.createProperty(property);
          await propertiesPage.goto();
        }
      }

      // Check if pagination controls are visible
      const paginationContainer = page.locator('nav').filter({ has: page.locator('button').filter({ hasText: /^1$/ }) });

      if ((await paginationContainer.count()) > 0) {
        await expect(paginationContainer).toBeVisible();

        // Check for page numbers - be more specific to avoid conflicts with property IDs
        const page1Button = paginationContainer.locator('button').filter({ hasText: /^1$/ });
        const page2Button = paginationContainer.locator('button').filter({ hasText: /^2$/ });

        await expect(page1Button).toBeVisible();
        await expect(page2Button).toBeVisible();

        // Test page navigation
        await page2Button.click();
        await page.waitForTimeout(1000);

        // Should be on page 2 (button should be highlighted/active)
        const activePage2Button = paginationContainer.locator('button').filter({ hasText: /^2$/ });
        await expect(activePage2Button).toHaveClass(/font-semibold/);
      } else {
        console.log('Pagination not visible - may need more properties or different page size');
      }
    });
  });

  test.describe('Cost Display Features', () => {
    test('should display total monthly cost', async ({ page }) => {
      // Create a property
      const property = generateUniqueProperty('cost-test');
      await propertiesPage.createProperty(property);
      await propertiesPage.goto();

      // Look for cost display elements
      const totalCostElement = page.locator(':text("Total Monthly Cost")');

      if ((await totalCostElement.count()) > 0) {
        await expect(totalCostElement).toBeVisible();

        // Should show a currency amount
        await expect(page.locator('text=/\\$/')).toBeVisible();
      } else {
        console.log('Total cost display not found - may need property services to be configured');
      }

      // Look for individual property cost columns
      const costColumn = page.locator('th:has-text("Monthly Cost")');
      if ((await costColumn.count()) > 0) {
        await expect(costColumn).toBeVisible();
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should handle bulk delete cancellation', async ({ page }) => {
      // Create a test property
      const property = generateUniqueProperty('cancel-bulk-delete');
      await propertiesPage.createProperty(property);
      await propertiesPage.goto();

      // Select the property by clicking its label
      const propertyRow = page.locator('table tbody tr').filter({ hasText: property.name });
      const checkboxLabel = propertyRow.locator('label').first();
      await checkboxLabel.click();

      // Click bulk delete
      const deleteButton = page.locator('button:has-text("Delete 1")');
      await deleteButton.click();

      // Wait for confirmation modal
      await expect(page.locator('text=Delete Properties')).toBeVisible();

      // Cancel the deletion
      await page.locator('button:has-text("Cancel")').click();

      // Modal should close and property should still exist
      await expect(page.locator('text=Delete Properties')).not.toBeVisible();
      await propertiesPage.expectPropertyExists(property.name);
    });
  });
});

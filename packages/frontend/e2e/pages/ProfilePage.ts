import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class ProfilePage extends BasePage {
  readonly profileForm: Locator;
  readonly saveButton: Locator;
  readonly cancelButton: Locator;
  readonly editButton: Locator;
  readonly profileInfo: Locator;

  constructor(page: Page) {
    super(page);
    this.profileForm = page.locator('[data-testid="profile-form"]');
    this.saveButton = page.locator('[data-testid="save-profile"]');
    this.cancelButton = page.locator('[data-testid="cancel-profile"]');
    this.editButton = page.locator('[data-testid="edit-profile"]');
    this.profileInfo = page.locator('[data-testid="profile-info"]');
  }

  async goto() {
    await super.goto('/profile');
  }

  async clickEditProfile() {
    await this.editButton.click();
    await this.profileForm.waitFor({ state: 'visible' });
  }

  async updateProfile(profileData: {
    name?: string;
    email?: string;
    age?: string;
    salary?: string;
    savingsAmount?: string;
    investmentRiskLevel?: string;
    notificationSettings?: {
      emailNotifications?: boolean;
      smsNotifications?: boolean;
      pushNotifications?: boolean;
    };
  }) {
    await this.clickEditProfile();

    if (profileData.name) {
      await this.page.fill('[name="name"]', profileData.name);
    }

    if (profileData.email) {
      await this.page.fill('[name="email"]', profileData.email);
    }

    if (profileData.age) {
      await this.page.fill('[name="age"]', profileData.age);
    }

    if (profileData.salary) {
      await this.page.fill('[name="salary"]', profileData.salary);
    }

    if (profileData.savingsAmount) {
      await this.page.fill('[name="savingsAmount"]', profileData.savingsAmount);
    }

    if (profileData.investmentRiskLevel) {
      await this.page.selectOption('[name="investmentRiskLevel"]', profileData.investmentRiskLevel);
    }

    if (profileData.notificationSettings) {
      const { emailNotifications, smsNotifications, pushNotifications } = profileData.notificationSettings;

      if (emailNotifications !== undefined) {
        if (emailNotifications) {
          await this.page.check('[name="emailNotifications"]');
        } else {
          await this.page.uncheck('[name="emailNotifications"]');
        }
      }

      if (smsNotifications !== undefined) {
        if (smsNotifications) {
          await this.page.check('[name="smsNotifications"]');
        } else {
          await this.page.uncheck('[name="smsNotifications"]');
        }
      }

      if (pushNotifications !== undefined) {
        if (pushNotifications) {
          await this.page.check('[name="pushNotifications"]');
        } else {
          await this.page.uncheck('[name="pushNotifications"]');
        }
      }
    }

    await this.saveButton.click();
    await this.waitForToast('Profile updated successfully');
  }

  async cancelProfileEdit() {
    await this.cancelButton.click();
    await this.profileInfo.waitFor({ state: 'visible' });
  }

  async expectProfileField(field: string, value: string) {
    const fieldElement = this.profileInfo.locator(`[data-testid="profile-${field}"]`);
    await expect(fieldElement).toContainText(value);
  }

  async expectProfileFormField(field: string, value: string) {
    const input = this.profileForm.locator(`[name="${field}"]`);
    await expect(input).toHaveValue(value);
  }

  async expectNotificationSetting(setting: string, enabled: boolean) {
    const checkbox = this.profileForm.locator(`[name="${setting}"]`);
    if (enabled) {
      await expect(checkbox).toBeChecked();
    } else {
      await expect(checkbox).not.toBeChecked();
    }
  }

  async expectProfileInfoVisible() {
    await expect(this.profileInfo).toBeVisible();
    await expect(this.editButton).toBeVisible();
  }

  async expectProfileFormVisible() {
    await expect(this.profileForm).toBeVisible();
    await expect(this.saveButton).toBeVisible();
    await expect(this.cancelButton).toBeVisible();
  }
}

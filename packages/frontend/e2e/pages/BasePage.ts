import { Page, Locator } from '@playwright/test';

export class BasePage {
  readonly page: Page;
  readonly navigation: Locator;
  readonly loadingSpinner: Locator;

  constructor(page: Page) {
    this.page = page;
    this.navigation = page.locator('nav');
    this.loadingSpinner = page.locator('[data-testid="loading"]');
  }

  async ensureAuthenticated() {
    // Check if we're redirected to auth page
    if (this.page.url().includes('/auth')) {
      throw new Error('User is not authenticated. Please check authentication setup.');
    }

    // Wait for any loading to complete
    await this.waitForPageLoad();
  }

  async goto(path: string = '/') {
    await this.page.goto(path);
    await this.ensureAuthenticated();
  }

  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle');
  }

  async navigateToProperties() {
    await this.page.click('a[href="/properties"]');
    await this.waitForPageLoad();
  }

  async navigateToRentals() {
    await this.page.click('a[href="/rentals"]');
    await this.waitForPageLoad();
  }

  async navigateToDashboard() {
    await this.page.click('a[href="/dashboard"]');
    await this.waitForPageLoad();
  }

  async navigateToProfile() {
    await this.page.click('a[href="/profile"]');
    await this.waitForPageLoad();
  }

  async waitForToast(message?: string) {
    const toast = this.page.locator('[data-testid="toast"]');
    await toast.waitFor({ state: 'visible' });
    if (message) {
      await toast.filter({ hasText: message }).waitFor({ state: 'visible' });
    }
    return toast;
  }

  async dismissToast() {
    const toast = this.page.locator('[data-testid="toast"]');
    const closeButton = toast.locator('button');
    if (await closeButton.isVisible()) {
      await closeButton.click();
    }
    await toast.waitFor({ state: 'hidden' });
  }

  async fillForm(formData: Record<string, string>) {
    for (const [field, value] of Object.entries(formData)) {
      const input = this.page.locator(`[name="${field}"]`);
      await input.fill(value);
    }
  }

  async submitForm() {
    await this.page.click('button[type="submit"]');
  }

  async expectUrl(path: string) {
    await this.page.waitForURL(`**${path}`);
  }
}

import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class DashboardPage extends BasePage {
  readonly totalPropertiesCard: Locator;
  readonly totalRentalsCard: Locator;
  readonly totalRevenueCard: Locator;
  readonly occupancyRateCard: Locator;
  readonly recentRentalsSection: Locator;
  readonly upcomingRentalsSection: Locator;
  readonly revenueChart: Locator;
  readonly occupancyChart: Locator;
  readonly quickActionsSection: Locator;

  constructor(page: Page) {
    super(page);
    // Use more specific selectors for the dashboard cards
    this.totalPropertiesCard = page.locator('.bg-white.rounded-xl.border.border-gray-200').filter({ hasText: 'Total Properties' });
    this.totalRentalsCard = page.locator('.bg-white.rounded-xl.border.border-gray-200').filter({ hasText: 'Rentals This Year' });
    this.totalRevenueCard = page.locator('.bg-white.rounded-xl.border.border-gray-200').filter({ hasText: 'Revenue This Year' });
    this.occupancyRateCard = page.locator('.bg-white.rounded-xl.border.border-gray-200').filter({ hasText: 'Avg Monthly Revenue' });
    this.recentRentalsSection = page.locator('div:has-text("Recent Rentals")');
    this.upcomingRentalsSection = page.locator('div:has-text("Upcoming Rentals")');
    this.revenueChart = page.locator('.bg-white.rounded-xl.border.border-gray-200').filter({ hasText: 'Monthly Rental Performance' });
    this.occupancyChart = page.locator('.bg-white.rounded-xl.border.border-gray-200').filter({ hasText: 'Monthly Trends' });
    this.quickActionsSection = page.locator('.bg-white.rounded-xl.border.border-gray-200').filter({ hasText: 'Quick Actions' });
  }

  async goto() {
    await super.goto('/');
  }

  async expectDashboardLoaded() {
    await this.totalPropertiesCard.waitFor({ state: 'visible' });
    await this.totalRentalsCard.waitFor({ state: 'visible' });
    await this.totalRevenueCard.waitFor({ state: 'visible' });
    await this.occupancyRateCard.waitFor({ state: 'visible' });
  }

  async expectTotalProperties(count: string) {
    await expect(this.totalPropertiesCard).toContainText(count);
  }

  async expectTotalRentals(count: string) {
    await expect(this.totalRentalsCard).toContainText(count);
  }

  async expectTotalRevenue(amount: string) {
    await expect(this.totalRevenueCard).toContainText(amount);
  }

  async expectOccupancyRate(rate: string) {
    await expect(this.occupancyRateCard).toContainText(rate);
  }

  async expectRecentRentalsVisible() {
    await expect(this.recentRentalsSection).toBeVisible();
    const rentalItems = this.recentRentalsSection.locator('[data-testid^="rental-item-"]');
    await expect(rentalItems.first()).toBeVisible();
  }

  async expectUpcomingRentalsVisible() {
    await expect(this.upcomingRentalsSection).toBeVisible();
    const rentalItems = this.upcomingRentalsSection.locator('[data-testid^="rental-item-"]');
    await expect(rentalItems.first()).toBeVisible();
  }

  async expectChartsVisible() {
    await expect(this.revenueChart).toBeVisible();
    await expect(this.occupancyChart).toBeVisible();
  }

  async clickQuickActionAddProperty() {
    await this.quickActionsSection.locator('button:has-text("Add New Property")').click();
    await this.expectUrl('/properties/new');
  }

  async clickQuickActionAddRental() {
    await this.quickActionsSection.locator('button:has-text("Create Rental")').click();
    await this.expectUrl('/rentals/new');
  }

  async clickViewAllProperties() {
    // Navigate to properties page directly since there might not be a "view all" link
    await this.page.click('a[href="/properties"]');
    await this.expectUrl('/properties');
  }

  async clickViewAllRentals() {
    // Navigate to rentals page directly since there might not be a "view all" link
    await this.page.click('a[href="/rentals"]');
    await this.expectUrl('/rentals');
  }

  async expectRecentRentalItem(_rentalId: string, propertyName: string, guestName: string) {
    // Look for rental items in the recent rentals section
    const rentalItem = this.recentRentalsSection.locator('div').filter({ hasText: propertyName });
    await expect(rentalItem).toBeVisible();
    await expect(rentalItem).toContainText(guestName);
  }

  async expectUpcomingRentalItem(_rentalId: string, propertyName: string, startDate: string) {
    // Look for rental items in the upcoming rentals section
    const rentalItem = this.upcomingRentalsSection.locator('div').filter({ hasText: propertyName });
    await expect(rentalItem).toBeVisible();
    await expect(rentalItem).toContainText(startDate);
  }

  async refreshDashboard() {
    await this.page.reload();
    await this.expectDashboardLoaded();
  }

  async expectNoDataMessage(section: 'properties' | 'rentals') {
    if (section === 'rentals') {
      // Look for chart empty state messages instead of rental list empty state
      const noDataMessage = this.page.locator('text=No monthly rental data available, text=No monthly trend data available');
      await expect(noDataMessage.first()).toBeVisible();
    } else {
      // For properties, look for general empty state
      const noDataMessage = this.page.locator(`text=No ${section} yet, text=No recent ${section}, text=No upcoming ${section}`);
      await expect(noDataMessage.first()).toBeVisible();
    }
  }
}

import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './BasePage';

export class RentalsPage extends BasePage {
  readonly addRentalButton: Locator;
  readonly rentalList: Locator;
  readonly rentalForm: Locator;
  readonly searchInput: Locator;
  readonly statusFilter: Locator;
  readonly propertyFilter: Locator;

  constructor(page: Page) {
    super(page);
    // Updated selectors for the table-based rentals page
    this.addRentalButton = page.locator('button:has-text("Add Rental"), button:has-text("Create Rental"), button:has-text("Add New Rental")').first();
    this.rentalList = page.locator('table tbody'); // Rentals are displayed in a table
    this.rentalForm = page.locator('form').first();
    this.searchInput = page.locator('input[placeholder*="Search rentals"]');
    this.statusFilter = page.locator('select').first();
    this.propertyFilter = page.locator('select').nth(1);
  }

  async goto() {
    await super.goto('/rentals');
  }

  async clickAddRental() {
    await this.addRentalButton.click();
    await this.rentalForm.waitFor({ state: 'visible' });
  }

  async fillRentalForm(rentalData: {
    property: string;
    startDate: string;
    endDate: string;
    rentalAmount: string;
    costAmount?: string;
    status: string;
    isPaid?: boolean;
  }) {
    // Wait for form to be visible
    await this.page.waitForSelector('form');

    // Select property - wait for properties to load and select the first available one
    const propertySelect = this.page.locator('#property');
    await propertySelect.waitFor({ state: 'visible' });

    // Wait a bit for properties to load
    await this.page.waitForTimeout(2000);

    const propertyOptions = await propertySelect.locator('option').count();
    console.log(`Found ${propertyOptions} property options`);

    if (propertyOptions > 1) {
      // Get the value of the first non-empty option
      const firstOptionValue = await propertySelect.locator('option').nth(1).getAttribute('value');
      console.log(`Selecting property with value: ${firstOptionValue}`);

      await propertySelect.selectOption({ index: 1 }); // First non-empty option

      // Verify selection worked
      const selectedValue = await propertySelect.inputValue();
      console.log(`✅ Property selected: ${selectedValue}`);

      // Trigger change event
      await propertySelect.dispatchEvent('change');
    }

    // Fill in dates - ensure they are valid future dates
    const startDateInput = this.page.locator('#startDate');
    const endDateInput = this.page.locator('#endDate');

    await startDateInput.clear();
    await startDateInput.fill(rentalData.startDate);
    await startDateInput.dispatchEvent('change');

    await endDateInput.clear();
    await endDateInput.fill(rentalData.endDate);
    await endDateInput.dispatchEvent('change');

    console.log(`✅ Dates filled: ${rentalData.startDate} to ${rentalData.endDate}`);

    // Fill rental amount - ensure it's a valid number
    const rentalAmountInput = this.page.locator('#rentalAmount');
    await rentalAmountInput.clear();
    await rentalAmountInput.fill(rentalData.rentalAmount);
    await rentalAmountInput.dispatchEvent('input');
    await rentalAmountInput.dispatchEvent('change');

    // Verify the amount was filled
    const filledAmount = await rentalAmountInput.inputValue();
    console.log(`✅ Rental amount filled: ${filledAmount}`);
  }

  async createRental(rentalData: {
    property: string;
    startDate: string;
    endDate: string;
    rentalAmount: string;
    costAmount?: string;
    status: string;
    isPaid?: boolean;
    guestName?: string;
    guestEmail?: string;
  }) {
    await this.clickAddRental();
    await this.fillRentalForm(rentalData);

    // Add a guest (required for rental creation)
    await this.addGuest({
      name: rentalData.guestName || 'Test Guest',
      email: rentalData.guestEmail || '<EMAIL>',
    });

    await this.submitForm();

    // Wait for navigation back to rentals list (success) or check for error
    try {
      await Promise.race([
        this.page.waitForURL('**/rentals', { timeout: 10000 }),
        this.page.locator('.bg-red-50, .text-red-600, .text-red-700').waitFor({ state: 'visible', timeout: 10000 }),
      ]);

      if (this.page.url().includes('/rentals') && !this.page.url().includes('/new')) {
        console.log('✅ Rental created successfully - navigated back to rentals list');
      } else {
        // Check for various error message formats
        const errorSelectors = ['.bg-red-50', '.text-red-600, .text-red-700', '[class*="error"]'];
        let errorMessage = 'Unknown error';

        for (const selector of errorSelectors) {
          const errorElement = this.page.locator(selector).first();
          if (await errorElement.isVisible()) {
            errorMessage = (await errorElement.textContent()) || 'Unknown error';
            break;
          }
        }

        throw new Error(`Rental creation failed: ${errorMessage}`);
      }
    } catch (error) {
      throw new Error(`Rental creation failed: ${error}`);
    }
  }

  async filterByStatus(status: string) {
    await this.statusFilter.selectOption(status);
    await this.waitForPageLoad();
  }

  async filterByProperty(propertyName: string) {
    await this.propertyFilter.selectOption(propertyName);
    await this.waitForPageLoad();
  }

  async searchRentals(query: string) {
    await this.searchInput.fill(query);
    await this.page.keyboard.press('Enter');
    await this.waitForPageLoad();
  }

  async expectRentalExists(guestName: string) {
    // Look for table row containing the guest name
    const rentalRow = this.page.locator('table tbody tr').filter({ hasText: guestName });
    await expect(rentalRow).toBeVisible();
  }

  async expectRentalNotExists(guestName: string) {
    // Look for table row containing the guest name
    const rentalRow = this.page.locator('table tbody tr').filter({ hasText: guestName });
    await expect(rentalRow).not.toBeVisible();
  }

  async expectRentalStatus(guestName: string, status: string) {
    // Look for table row containing the guest name and check status
    const rentalRow = this.page.locator('table tbody tr').filter({ hasText: guestName });
    await expect(rentalRow).toContainText(status);
  }

  async getRentalCount() {
    return await this.page.locator('table tbody tr').count();
  }

  async editRental(rentalIndex: number, updates: { rentalAmount?: string }) {
    // Find the rental row and click edit button
    const rentalRow = this.page.locator('table tbody tr').nth(rentalIndex);
    const editButton = rentalRow.locator('button').nth(1); // Assuming edit is second button
    await editButton.click();

    // Update the fields
    if (updates.rentalAmount) {
      await this.page.fill('#rentalAmount', updates.rentalAmount);
    }

    await this.submitForm();
  }

  async deleteRental(rentalIndex: number) {
    // Find the rental row and click delete button
    const rentalRow = this.page.locator('table tbody tr').nth(rentalIndex);
    const deleteButton = rentalRow.locator('button').last(); // Assuming delete is last button
    await deleteButton.click();

    // Handle confirmation modal if it exists
    const confirmButton = this.page.locator('button:has-text("Delete"), button:has-text("Confirm")');
    if (await confirmButton.isVisible()) {
      await confirmButton.click();
    }
  }

  async viewRentalDetails(rentalId: string) {
    const rentalCard = this.rentalList.locator(`[data-testid="rental-card-${rentalId}"]`);
    await rentalCard.click();
    await this.expectUrl(`/rentals/${rentalId}`);
  }

  async addGuest(guestData: { name: string; email: string }) {
    try {
      // Click the "Add Guest" button to open the guest selection modal
      await this.page.click('button:has-text("Add Guest")');

      // Wait for the guest selection modal to appear
      await this.page.waitForSelector('.fixed.inset-0', { timeout: 5000 });

      // Click "Add New Guest" button in the modal
      await this.page.click('button:has-text("Add New Guest")');

      // Wait for the guest form to appear (give it more time)
      await this.page.waitForTimeout(1000);

      // Fill in the guest form using exact placeholder text
      const [firstName, lastName] = guestData.name.split(' ');
      await this.page.fill('input[placeholder="Enter first name"]', firstName || guestData.name);
      if (lastName) {
        await this.page.fill('input[placeholder="Enter last name"]', lastName);
      }
      await this.page.fill('input[placeholder="<EMAIL>"]', guestData.email);

      // Fill required date field (use a default date)
      // The date input expects YYYY-MM-DD format
      await this.page.fill('input[type="date"]', '1990-01-01');

      // Ensure we're creating a local guest (not foreign) to avoid additional validation
      const localGuestRadio = this.page.locator('input[type="radio"][value="false"]');
      if (await localGuestRadio.isVisible()) {
        await localGuestRadio.click();
      }

      // Fill required fields with default values - target the document type select in the modal
      // Wait for the document type select to be available and select it specifically
      const documentTypeSelect = this.page.locator('.fixed.inset-0 select').last(); // Last select in the modal
      await documentTypeSelect.waitFor({ state: 'visible' });
      await documentTypeSelect.selectOption('Passport');
      await this.page.fill('input[placeholder="Enter document number"]', '*********');

      // Fill additional required fields for local guests
      await this.page.fill('input[placeholder="Enter city of residency"]', 'Test City');
      await this.page.fill('input[placeholder="Enter city of birth"]', 'Test City');

      // Submit the guest form
      await this.page.click('button[type="submit"]');

      // Wait for the guest to be created and automatically selected
      // The GuestSelectionModal automatically calls onSelectGuest and closes when a new guest is created
      // So we just need to wait for the modal to close
      await this.page.waitForSelector('.fixed.inset-0', { state: 'hidden', timeout: 10000 });

      console.log('✅ Guest added and selected successfully');
    } catch (error) {
      console.log('❌ Failed to add guest:', error);
      throw error;
    }
  }

  async removeGuest(guestIndex: number) {
    await this.page.click(`[data-testid="remove-guest-${guestIndex}"]`);
  }

  async createGuestViaAPI(guestData: { name: string; email: string }) {
    // Create a guest via API to bypass the complex modal flow
    const [firstName, lastName] = guestData.name.split(' ');

    // Get the auth token from localStorage
    const token = await this.page.evaluate(() => localStorage.getItem('token'));

    const response = await this.page.request.post('http://localhost:5000/api/guests', {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      data: {
        firstName: firstName || guestData.name,
        lastName: lastName || 'Test',
        email: guestData.email,
        dateOfBirth: '1990-01-01',
        documentType: 'Passport',
        documentNumber: '*********',
        isForeigner: false,
        cityOfResidency: 'Test City',
        cityOfBirth: 'Test City',
        relationshipWithOwner: 'Guest',
      },
    });

    if (!response.ok()) {
      const errorText = await response.text();
      throw new Error(`Failed to create guest via API: ${response.status()} - ${errorText}`);
    }

    return await response.json();
  }

  async selectExistingGuest(guestId: string) {
    // Open the guest selection modal
    await this.page.click('button:has-text("Add Guest")');
    await this.page.waitForSelector('.fixed.inset-0', { timeout: 5000 });

    // Find and click the guest with the specified ID
    const guestCard = this.page.locator(`[data-guest-id="${guestId}"]`);
    if (await guestCard.isVisible()) {
      await guestCard.click();
    } else {
      // If no data-guest-id, try to find by name or email
      await this.page.click('.border.border-gray-200.rounded-lg.p-4:first-child');
    }

    // Wait for modal to close
    await this.page.waitForSelector('.fixed.inset-0', { state: 'hidden', timeout: 10000 });
  }
}

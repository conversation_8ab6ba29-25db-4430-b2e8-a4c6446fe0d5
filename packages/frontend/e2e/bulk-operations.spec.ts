import { test, expect } from '@playwright/test';
import { BasePage } from './pages/BasePage';

test.describe('Bulk Operations E2E Tests', () => {
  let basePage: BasePage;

  test.beforeEach(async ({ page }) => {
    basePage = new BasePage(page);
  });

  test.describe('Properties Page Bulk Operations', () => {
    test('should display properties with pagination and selection', async ({ page }) => {
      await basePage.goto('/properties');

      // Wait for properties to load
      await page.waitForSelector('table tbody', { timeout: 10000 });

      // Check if properties are displayed
      const propertyRows = page.locator('tbody tr');
      const rowCount = await propertyRows.count();

      if (rowCount > 0) {
        // Test selection functionality - use force click since checkbox is sr-only
        const firstCheckbox = page.locator('tbody tr:first-child input[type="checkbox"]');
        await firstCheckbox.click({ force: true });

        // Verify bulk actions appear
        await expect(page.locator('text=1 selected')).toBeVisible();
        await expect(page.locator('button:has-text("Delete 1")')).toBeVisible();
        await expect(page.locator('button:has-text("Select All")')).toBeVisible();
        await expect(page.locator('button:has-text("Select None")')).toBeVisible();
      }

      // Test pagination if more than 10 properties
      const paginationNext = page.locator('button:has-text("Next")');
      if ((await paginationNext.isVisible()) && !(await paginationNext.isDisabled())) {
        await paginationNext.click();
        await page.waitForTimeout(1000); // Wait for page change
      }
    });

    test('should perform bulk delete workflow', async ({ page }) => {
      await basePage.goto('/properties');
      await page.waitForSelector('table tbody', { timeout: 10000 });

      const propertyRows = page.locator('tbody tr');
      const rowCount = await propertyRows.count();

      if (rowCount > 0) {
        // Select first property - use force click since checkbox is sr-only
        const firstCheckbox = page.locator('tbody tr:first-child input[type="checkbox"]');
        await firstCheckbox.click({ force: true });

        // Click bulk delete
        const deleteButton = page.locator('button:has-text("Delete 1")');
        await deleteButton.click();

        // Verify confirmation modal appears
        await expect(page.locator('text=Delete Properties')).toBeVisible();
        await expect(page.locator('text=Are you sure you want to delete 1 properties?')).toBeVisible();

        // Cancel the deletion (to avoid actually deleting data)
        await page.locator('button:has-text("Cancel")').click();

        // Verify modal is closed
        await expect(page.locator('text=Delete Properties')).not.toBeVisible();
      }
    });
  });

  test.describe('Guests Page Bulk Operations', () => {
    test('should display guests with pagination and selection', async ({ page }) => {
      await basePage.goto('/guests');

      // Wait for guests to load
      await page.waitForSelector('.grid', { timeout: 10000 });

      // Check if guests are displayed
      const guestCards = page.locator('.grid > div');
      const cardCount = await guestCards.count();

      if (cardCount > 0) {
        // Test selection functionality
        const firstCheckbox = page.locator('.grid > div:first-child input[type="checkbox"]');
        await firstCheckbox.click();

        // Verify bulk actions appear
        await expect(page.locator('text=1 selected')).toBeVisible();
        await expect(page.locator('button:has-text("Delete 1")')).toBeVisible();
      }

      // Test pagination if more than 12 guests
      const paginationNext = page.locator('button:has-text("Next")');
      if ((await paginationNext.isVisible()) && !(await paginationNext.isDisabled())) {
        await paginationNext.click();
        await page.waitForTimeout(1000);
      }
    });

    test('should handle guest selection and bulk actions', async ({ page }) => {
      await page.goto('http://localhost:5174/guests');
      await page.waitForSelector('.grid', { timeout: 10000 });

      const guestCards = page.locator('.grid > div');
      const cardCount = await guestCards.count();

      if (cardCount > 1) {
        // Select multiple guests
        await page.locator('.grid > div:first-child input[type="checkbox"]').click();
        await page.locator('.grid > div:nth-child(2) input[type="checkbox"]').click();

        // Verify selection count
        await expect(page.locator('text=2 selected')).toBeVisible();

        // Test select all
        await page.locator('button:has-text("Select All")').click();
        await expect(page.locator(`text=${cardCount} selected`)).toBeVisible();

        // Test select none
        await page.locator('button:has-text("Select None")').click();
        await expect(page.locator('text=selected')).not.toBeVisible();
      }
    });
  });

  test.describe('Partners Page Bulk Operations', () => {
    test('should display partners with table selection', async ({ page }) => {
      await page.goto('http://localhost:5174/partners');

      // Wait for partners table to load
      await page.waitForSelector('table', { timeout: 10000 });

      const partnerRows = page.locator('tbody tr');
      const rowCount = await partnerRows.count();

      if (rowCount > 0) {
        // Test individual selection
        const firstRowCheckbox = page.locator('tbody tr:first-child input[type="checkbox"]');
        await firstRowCheckbox.click();

        // Verify bulk actions appear
        await expect(page.locator('text=1 selected')).toBeVisible();

        // Test header checkbox (select all)
        const headerCheckbox = page.locator('thead input[type="checkbox"]');
        await headerCheckbox.click();

        // Should select all partners
        await expect(page.locator(`text=${rowCount} selected`)).toBeVisible();
      }
    });

    test('should handle partner bulk delete workflow', async ({ page }) => {
      await page.goto('http://localhost:5174/partners');
      await page.waitForSelector('table', { timeout: 10000 });

      const partnerRows = page.locator('tbody tr');
      const rowCount = await partnerRows.count();

      if (rowCount > 0) {
        // Select first partner
        await page.locator('tbody tr:first-child input[type="checkbox"]').click();

        // Click bulk delete
        await page.locator('button:has-text("Delete 1")').click();

        // Verify confirmation modal
        await expect(page.locator('text=Delete Partners')).toBeVisible();

        // Cancel deletion
        await page.locator('button:has-text("Cancel")').click();
      }
    });
  });

  test.describe('Currencies Page Bulk Operations', () => {
    test('should display currencies with selection', async ({ page }) => {
      await page.goto('http://localhost:5174/currencies');

      // Wait for currencies to load
      await page.waitForSelector('.divide-y', { timeout: 10000 });

      const currencyItems = page.locator('.divide-y > div');
      const itemCount = await currencyItems.count();

      if (itemCount > 0) {
        // Test selection
        const firstCheckbox = page.locator('.divide-y > div:first-child input[type="checkbox"]');
        await firstCheckbox.click();

        // Verify bulk actions appear
        await expect(page.locator('text=1 selected')).toBeVisible();
        await expect(page.locator('button:has-text("Delete 1")')).toBeVisible();
      }
    });

    test('should handle currency pagination', async ({ page }) => {
      await page.goto('http://localhost:5174/currencies');
      await page.waitForSelector('.divide-y', { timeout: 10000 });

      // Check if pagination is present
      const paginationNext = page.locator('button:has-text("Next")');
      if ((await paginationNext.isVisible()) && !(await paginationNext.isDisabled())) {
        await paginationNext.click();
        await page.waitForTimeout(1000);

        // Verify we're on page 2
        const page2Button = page.locator('button:has-text("2")');
        if (await page2Button.isVisible()) {
          await expect(page2Button).toHaveClass(/text-blue-600/);
        }
      }
    });
  });

  test.describe('Rentals Page Bulk Operations', () => {
    test('should display rentals with table functionality', async ({ page }) => {
      await page.goto('http://localhost:5174/rentals');

      // Wait for rentals to load
      await page.waitForSelector('table', { timeout: 10000 });

      const rentalRows = page.locator('tbody tr');
      const rowCount = await rentalRows.count();

      if (rowCount > 0) {
        // Test selection
        const firstCheckbox = page.locator('tbody tr:first-child input[type="checkbox"]');
        await firstCheckbox.click();

        // Verify bulk actions appear
        await expect(page.locator('text=1 selected')).toBeVisible();
        await expect(page.locator('button:has-text("Delete 1")')).toBeVisible();
      }
    });

    test('should handle rental view switching', async ({ page }) => {
      await page.goto('http://localhost:5174/rentals');

      // Test view switching between table and calendar
      const calendarButton = page.locator('button:has-text("Calendar")');
      if (await calendarButton.isVisible()) {
        await calendarButton.click();
        await page.waitForTimeout(1000);

        // Switch back to table view
        const tableButton = page.locator('button:has-text("Table")');
        if (await tableButton.isVisible()) {
          await tableButton.click();
          await page.waitForTimeout(1000);
        }
      }
    });
  });

  test.describe('Cross-Page Navigation', () => {
    test('should navigate between all pages and maintain functionality', async ({ page }) => {
      const pages = [
        { url: '/properties', selector: '[data-testid="properties-table"]' },
        { url: '/guests', selector: '.grid' },
        { url: '/partners', selector: 'table' },
        { url: '/currencies', selector: '.divide-y' },
        { url: '/rentals', selector: 'table' },
      ];

      for (const pageInfo of pages) {
        await page.goto(`http://localhost:5174${pageInfo.url}`);
        await page.waitForSelector(pageInfo.selector, { timeout: 10000 });

        // Verify page loaded correctly
        await expect(page.locator(pageInfo.selector)).toBeVisible();

        // Check for pagination controls if present
        const paginationContainer = page.locator('button:has-text("Previous"), button:has-text("Next")');
        if (await paginationContainer.first().isVisible()) {
          await expect(paginationContainer.first()).toBeVisible();
        }
      }
    });
  });

  test.describe('Error Handling', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Intercept API calls and simulate network errors
      await page.route('**/api/properties/bulk', (route) => {
        route.abort('failed');
      });

      await page.goto('http://localhost:5174/properties');
      await page.waitForSelector('[data-testid="properties-table"]', { timeout: 10000 });

      const propertyRows = page.locator('tbody tr');
      const rowCount = await propertyRows.count();

      if (rowCount > 0) {
        // Try to perform bulk delete
        await page.locator('tbody tr:first-child input[type="checkbox"]').click();
        await page.locator('button:has-text("Delete 1")').click();
        await page.locator('button:has-text("Delete 1 Properties")').click();

        // Should handle error gracefully (implementation dependent)
        await page.waitForTimeout(2000);
      }
    });
  });
});

import { test, expect } from '@playwright/test';
import { login, TEST_USER } from './fixtures/auth';

test.describe('Simple Role Test', () => {
  test('customer can login and see dashboard', async ({ page }) => {
    // Login as customer (existing test user)
    await login(page, TEST_USER);

    // Navigate to dashboard
    await page.goto('/dashboard');

    // Wait for dashboard to load
    await page.waitForSelector('[data-testid="dashboard-content"]', { timeout: 10000 });

    // Verify we're on the dashboard
    expect(page.url()).toContain('/dashboard');
  });

  test('customer can see properties page with read-only message', async ({ page }) => {
    // Login as customer
    await login(page, TEST_USER);

    // Navigate to properties page
    await page.goto('/properties');

    // Wait for properties to load
    await page.waitForSelector('[data-testid="properties-list"]', { timeout: 10000 });

    // Check for read-only message
    const readOnlyMessage = page.locator('[data-testid="read-only-message"]');
    await expect(readOnlyMessage).toBeVisible();
  });

  test('customer cannot see create property button', async ({ page }) => {
    // Login as customer
    await login(page, TEST_USER);

    // Navigate to properties page
    await page.goto('/properties');

    // Wait for properties to load
    await page.waitForSelector('[data-testid="properties-list"]', { timeout: 10000 });

    // Verify create property button is hidden
    const createButton = page.locator('[data-testid="create-property-btn"]');
    await expect(createButton).toHaveCount(0);
  });
});

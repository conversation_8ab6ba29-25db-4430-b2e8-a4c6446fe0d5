import { chromium } from '@playwright/test';

async function globalTeardown() {
  console.log('🧹 Starting global teardown for E2E tests...');

  // Launch browser for teardown
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Clean up test data
    console.log('🧹 Cleaning up test data...');
    try {
      await page.goto('http://localhost:3000/api/test/cleanup', { waitUntil: 'networkidle' });
      console.log('✅ Test data cleaned up successfully');
    } catch {
      console.log('⚠️ Test cleanup endpoint not available, skipping...');
    }

    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  } finally {
    await browser.close();
  }
}

export default globalTeardown;

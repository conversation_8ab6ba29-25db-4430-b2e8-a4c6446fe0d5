import { chromium, Page } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123',
  fullName: 'Test User',
};

async function setupTestAuthentication(page: Page) {
  try {
    // Check if auth file already exists
    const storageStatePath = path.join(__dirname, '..', 'playwright', '.auth', 'user.json');

    if (fs.existsSync(storageStatePath)) {
      console.log('✅ Authentication state file already exists, skipping setup');
      return;
    }

    // Go to the frontend
    await page.goto('http://localhost:5173');

    // Wait for the page to load and check if we're on auth page
    await page.waitForLoadState('networkidle');

    // Check if we're already authenticated
    const isAuthenticated = await page.evaluate(() => {
      const token = localStorage.getItem('token');
      return !!token && !window.location.pathname.includes('/auth');
    });

    if (isAuthenticated) {
      console.log('✅ Already authenticated, using existing session');
    } else if (page.url().includes('/auth')) {
      console.log('📝 Registering test user...');

      // Try to register first (in case user doesn't exist)
      try {
        // Click sign up tab if it exists
        const signUpButton = page.locator('text=Sign Up');
        if (await signUpButton.isVisible()) {
          await signUpButton.click();
        }

        // Fill registration form
        await page.fill('input[id="fullName"]', TEST_USER.fullName);
        await page.fill('input[id="email"]', TEST_USER.email);
        await page.fill('input[id="password"]', TEST_USER.password);

        // Submit registration
        await page.click('button[type="submit"]');

        // Wait for redirect to dashboard
        try {
          await page.waitForURL('**/dashboard', { timeout: 10000 });
          console.log('✅ Test user registered and logged in');
        } catch (error) {
          console.log('⚠️ Registration may have failed, will try login instead');
          throw error; // This will trigger the catch block below
        }
      } catch {
        console.log('⚠️ Registration failed, trying login...');

        // If registration fails, try login
        const signInButton = page.locator('text=Sign In');
        if (await signInButton.isVisible()) {
          await signInButton.click();
        }

        // Fill login form
        await page.fill('input[id="email"]', TEST_USER.email);
        await page.fill('input[id="password"]', TEST_USER.password);

        // Submit login
        await page.click('button[type="submit"]');

        // Wait for either dashboard redirect or error
        try {
          await page.waitForURL('**/dashboard', { timeout: 10000 });
          console.log('✅ Test user logged in');
        } catch {
          // Check if there's an error message
          const errorMessage = await page
            .locator('[data-testid="error-message"]')
            .textContent()
            .catch(() => null);
          if (errorMessage) {
            console.log(`❌ Login error: ${errorMessage}`);
          }

          // Take a screenshot for debugging
          await page.screenshot({ path: 'playwright/.auth/login-error.png' });
          console.log('📸 Screenshot saved to playwright/.auth/login-error.png');

          throw new Error(`Login failed. Current URL: ${page.url()}`);
        }
      }
    } else {
      console.log('✅ Already authenticated');
    }

    // Save authentication state
    await page.context().storageState({ path: storageStatePath });
    console.log(`💾 Authentication state saved to ${storageStatePath}`);
  } catch (error) {
    console.error('❌ Authentication setup failed:', error);
    throw error;
  }
}

async function globalSetup() {
  console.log('🚀 Starting global setup for E2E tests...');

  // Launch browser for setup
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Wait for backend to be ready
    console.log('⏳ Waiting for backend to be ready...');
    let backendReady = false;
    let attempts = 0;
    const maxAttempts = 30;

    while (!backendReady && attempts < maxAttempts) {
      try {
        const response = await page.goto('http://localhost:3100/api/health');
        if (response?.ok()) {
          backendReady = true;
          console.log('✅ Backend is ready');
        }
      } catch {
        attempts++;
        console.log(`⏳ Backend not ready yet (attempt ${attempts}/${maxAttempts})`);
        await page.waitForTimeout(2000);
      }
    }

    if (!backendReady) {
      throw new Error('Backend failed to start within timeout period');
    }

    // Wait for frontend to be ready
    console.log('⏳ Waiting for frontend to be ready...');
    let frontendReady = false;
    attempts = 0;

    while (!frontendReady && attempts < maxAttempts) {
      try {
        const response = await page.goto('http://localhost:5173');
        if (response?.ok()) {
          frontendReady = true;
          console.log('✅ Frontend is ready');
        }
      } catch {
        attempts++;
        console.log(`⏳ Frontend not ready yet (attempt ${attempts}/${maxAttempts})`);
        await page.waitForTimeout(2000);
      }
    }

    if (!frontendReady) {
      throw new Error('Frontend failed to start within timeout period');
    }

    // Clear any existing test data
    console.log('🧹 Cleaning up test data...');
    try {
      await page.goto('http://localhost:3100/api/test/cleanup', { waitUntil: 'networkidle' });
    } catch {
      console.log('⚠️ Test cleanup endpoint not available, continuing...');
    }

    // Create test user and save authentication state
    console.log('👤 Setting up test user authentication...');
    await setupTestAuthentication(page);

    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export default globalSetup;

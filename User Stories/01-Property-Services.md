# Property Services User Stories

## Epic: Property Service Management

As a property owner, I want to manage the services associated with my properties so that I can track costs and calculate accurate rental pricing.

---

## US-PS-001: Add Services to Property

**As a** property owner  
**I want to** add services to my properties  
**So that** I can track all associated costs and include them in rental calculations

### Acceptance Criteria

- [x] I can access a "Services" section when viewing a property
- [x] I can add a new service with the following information:
  - Service name (required)
  - Cost amount (required, numeric)
  - Frequency (monthly/quarterly/yearly) (required)
  - Status (active/inactive) (default: active)
  - Mandatory flag (checkbox, default: false)
  - Paid by user flag (checkbox, default: true)
  - Paid by partner flag (checkbox, default: false)
- [x] I can save the service and see it in the property's service list
- [x] Form validation prevents saving incomplete or invalid data
- [x] I receive confirmation when a service is successfully added

### Technical Notes

- Service cost should be stored as decimal with 2 decimal places
- Frequency should be an enum: ['monthly', 'quarterly', 'yearly']
- Status should be an enum: ['active', 'inactive']

---

## US-PS-002: View Property Services

**As a** property owner  
**I want to** view all services associated with a property  
**So that** I can see the complete cost breakdown

### Acceptance Criteria

- [x] I can see a list of all services for a property
- [x] Each service displays: name, cost, frequency, status, and payment responsibility
- [x] Services are grouped by status (active services shown first)
- [x] I can see the total monthly cost equivalent for all active services
- [x] I can filter services by status (active/inactive)
- [x] I can search services by name

### Technical Notes

- Calculate monthly equivalent: quarterly ÷ 3, yearly ÷ 12
- Display costs in property's assigned currency format

---

## US-PS-003: Edit Property Services

**As a** property owner  
**I want to** edit existing property services  
**So that** I can keep service information up to date

### Acceptance Criteria

- [x] I can click on a service to edit it
- [x] I can modify all service fields except creation date
- [x] Changes are validated before saving
- [x] I can cancel editing without saving changes
- [x] I receive confirmation when changes are saved
- [x] Updated service information is immediately reflected in the list

---

## US-PS-004: Delete Property Services

**As a** property owner  
**I want to** delete property services that are no longer needed  
**So that** I can keep my service list clean and accurate

### Acceptance Criteria

- [x] I can delete a service from the property
- [x] I receive a confirmation dialog before deletion
- [x] I cannot delete a service if it's being used in active rentals
- [x] Deleted services are removed from the property's service list
- [x] I receive confirmation when a service is successfully deleted

### Technical Notes

- Implement soft delete if service is referenced in historical rentals
- Check for active rental dependencies before allowing deletion

---

## US-PS-005: Service Cost Calculation

**As a** property owner  
**I want to** see the total service costs for my property  
**So that** I can understand the baseline costs for rental pricing

### Acceptance Criteria

- [x] I can see total monthly service costs for the property
- [x] I can see costs broken down by payment responsibility (user vs partner)
- [x] I can see only mandatory service costs separately
- [x] Calculations update automatically when services are added/modified
- [x] Inactive services are excluded from calculations

### Technical Notes

- Provide API endpoint for service cost calculations
- Cache calculations for performance if needed

---

## US-PS-006: Custom Currency Management

**As a** property owner
**I want to** create and manage custom currencies
**So that** I can track property finances in different currencies based on property location or preference

### Acceptance Criteria

- [x] I can create new currencies with the following information:
  - Currency name (required, e.g., "US Dollar", "Euro", "British Pound")
  - Currency symbol (required, e.g., "$", "€", "£")
  - Currency code (optional, e.g., "USD", "EUR", "GBP")
- [x] I can view a list of all my created currencies
- [x] I can edit existing currency information
- [x] I can delete currencies that are not being used by any property
- [x] Currency names and symbols must be unique within my account
- [x] I receive validation errors for duplicate or invalid currency data
- [x] I receive confirmation when currencies are created, updated, or deleted

### Technical Notes

- Implement Currency model with name, symbol, and code fields
- Add unique constraints for name and symbol per user
- Validate currency symbol length (1-3 characters recommended)
- Consider providing default currencies (USD, EUR, etc.) for new users

---

## US-PS-007: Property Currency Assignment

**As a** property owner
**I want to** assign a currency to each property
**So that** all financial calculations for that property use the correct currency

### Acceptance Criteria

- [x] I can select a currency when creating a new property
- [x] I can change the currency for an existing property
- [x] If no currency is selected, system uses a default currency
- [x] All property-related financial displays show the assigned currency symbol
- [x] Property services, rentals, and costs display in the property's currency
- [x] I can see which currency is assigned to each property in property lists
- [x] Currency changes affect all future calculations but preserve historical data
- [x] I receive a warning when changing currency for properties with existing rentals

### Technical Notes

- Add currencyId field to Property model (foreign key to Currency)
- Update Property creation and edit forms to include currency selection
- Modify all financial displays to show property-specific currency
- Consider currency conversion implications for reporting
- Preserve historical data integrity when currency changes

---

## US-PS-008: Multi-Currency Financial Display

**As a** property owner
**I want to** see financial information displayed in the correct currency
**So that** I can understand costs and revenues in the appropriate monetary units

### Acceptance Criteria

- [ ] Property service costs display with the property's currency symbol
- [ ] Rental amounts display with the property's currency symbol
- [ ] Property financial summaries show currency-specific totals
- [ ] Dashboard and reports group financial data by currency
- [ ] I can see total portfolio value broken down by currency
- [ ] Currency symbols are consistently displayed throughout the application
- [ ] Financial calculations respect currency-specific decimal places
- [ ] Export functions include currency information in the data

### Technical Notes

- Update all financial display components to show currency symbols
- Implement currency formatting utilities
- Group financial aggregations by currency
- Consider currency-specific decimal place handling
- Update API responses to include currency information
- Modify export functions to include currency data

---

## US-PS-009: Edit Property

**As a** property owner  
**I want to** edit an existing property  
**So that** I can update any information about the Property

### Acceptance Criteria

- [x] I can choose to edit a propery from the Propery list
- [x] I can modify all property fields except creation date
- [x] Changes are validated before saving
- [x] I can cancel editing without saving changes
- [x] I receive confirmation when changes are saved
- [x] Updated property information is immediately reflected in the list

---

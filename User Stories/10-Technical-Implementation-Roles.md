# Technical Implementation - User Roles System

**Epic**: Backend and Frontend Technical Implementation  
**Priority**: High  
**Estimated Effort**: 35-40 story points

## Overview

This epic covers the technical implementation details for the user roles and permissions system, including database schema changes, API modifications, middleware implementation, and frontend component updates.

---

## 🎯 Technical User Stories

### US-TIR-001: Database Schema for User Roles
**As a** Developer  
**I want to** implement the database schema for user roles and permissions  
**So that** the system can store and manage role-based access control data

**Acceptance Criteria:**
- Add `role` enum field to User model (ADMIN, OPERATOR, CUSTOMER)
- Create OperatorAssignment model linking operators to customers
- Add audit logging tables for role changes and user actions
- Implement database migrations for existing users (default to CUSTOMER)
- Add indexes for performance on role-based queries
- Create foreign key constraints for data integrity

**Technical Implementation:**
```sql
-- User table modification
ALTER TABLE users ADD COLUMN role ENUM('ADMIN', 'OPERATOR', 'CUSTOMER') DEFAULT 'CUSTOMER';

-- Operator assignments table
CREATE TABLE operator_assignments (
  id VARCHAR(36) PRIMARY KEY,
  operator_id VARCHAR(36) NOT NULL,
  customer_id VARCHAR(36) NOT NULL,
  assigned_by VARCHAR(36) NOT NULL,
  assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  permissions JSON,
  FOREIGN KEY (operator_id) REFERENCES users(id),
  FOREIGN KEY (customer_id) REFERENCES users(id),
  FOREIGN KEY (assigned_by) REFERENCES users(id)
);

-- Audit log table
CREATE TABLE audit_logs (
  id VARCHAR(36) PRIMARY KEY,
  user_id VARCHAR(36),
  action VARCHAR(100) NOT NULL,
  resource_type VARCHAR(50),
  resource_id VARCHAR(36),
  old_values JSON,
  new_values JSON,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

### US-TIR-002: Authentication Middleware Enhancement
**As a** Developer  
**I want to** enhance authentication middleware to support role-based access control  
**So that** API endpoints can enforce role-specific permissions

**Acceptance Criteria:**
- Extend JWT tokens to include user role information
- Create role-based middleware decorators (@requireRole, @requireAnyRole)
- Implement permission checking for operator-customer assignments
- Add request context with user role and permissions
- Create middleware for audit logging all API requests
- Handle role changes with token invalidation

**Technical Implementation:**
```typescript
// Enhanced JWT payload
interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  assignedCustomers?: string[]; // For operators
  permissions: string[];
}

// Role-based middleware
export const requireRole = (roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!roles.includes(req.user.role)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};

// Operator assignment middleware
export const requireCustomerAccess = (customerIdParam: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const customerId = req.params[customerIdParam];
    if (req.user.role === 'ADMIN') return next();
    if (req.user.role === 'CUSTOMER' && req.user.userId === customerId) return next();
    if (req.user.role === 'OPERATOR' && req.user.assignedCustomers?.includes(customerId)) return next();
    return res.status(403).json({ error: 'Access denied to customer data' });
  };
};
```

---

### US-TIR-003: API Endpoint Modifications
**As a** Developer  
**I want to** modify existing API endpoints to support role-based data filtering  
**So that** users only see data they're authorized to access

**Acceptance Criteria:**
- Modify all GET endpoints to filter data based on user role
- Add user context parameter to all database queries
- Implement admin endpoints for cross-user data access
- Create operator-specific endpoints with customer filtering
- Add bulk user management endpoints for admins
- Implement data export endpoints with role-based filtering

**Technical Implementation:**
```typescript
// Properties endpoint with role-based filtering
router.get('/properties', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  let whereClause: any = {};
  
  switch (req.user.role) {
    case 'ADMIN':
      // Admin sees all properties
      break;
    case 'OPERATOR':
      // Operator sees properties of assigned customers
      whereClause.ownerId = { $in: req.user.assignedCustomers };
      break;
    case 'CUSTOMER':
      // Customer sees only their properties
      whereClause.ownerId = req.user.userId;
      break;
  }
  
  const properties = await Property.find(whereClause);
  res.json(properties);
});

// Admin-only user management endpoints
router.get('/admin/users', authenticate, requireRole(['ADMIN']), async (req, res) => {
  const users = await User.find().select('-password');
  res.json(users);
});

router.patch('/admin/users/:id/role', authenticate, requireRole(['ADMIN']), async (req, res) => {
  const { role } = req.body;
  await User.findByIdAndUpdate(req.params.id, { role });
  // Log role change
  await AuditLog.create({
    userId: req.user.userId,
    action: 'ROLE_CHANGE',
    resourceType: 'USER',
    resourceId: req.params.id,
    newValues: { role }
  });
  res.json({ success: true });
});
```

---

### US-TIR-004: Frontend Role-Based Components
**As a** Developer  
**I want to** create reusable components for role-based UI rendering  
**So that** the frontend can adapt to different user roles dynamically

**Acceptance Criteria:**
- Create RoleGuard component for conditional rendering
- Implement useRole hook for role-based logic
- Create role-specific navigation components
- Add permission-based button and action components
- Implement customer selector for operators
- Create admin-only components and pages

**Technical Implementation:**
```typescript
// RoleGuard component
interface RoleGuardProps {
  allowedRoles: UserRole[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const RoleGuard: React.FC<RoleGuardProps> = ({ 
  allowedRoles, 
  children, 
  fallback = null 
}) => {
  const { user } = useAuth();
  
  if (!user || !allowedRoles.includes(user.role)) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
};

// useRole hook
export const useRole = () => {
  const { user } = useAuth();
  
  return {
    role: user?.role,
    isAdmin: user?.role === 'ADMIN',
    isOperator: user?.role === 'OPERATOR',
    isCustomer: user?.role === 'CUSTOMER',
    canManageUsers: user?.role === 'ADMIN',
    canManageProperties: ['ADMIN', 'OPERATOR'].includes(user?.role || ''),
    assignedCustomers: user?.assignedCustomers || []
  };
};

// Customer selector for operators
export const CustomerSelector: React.FC = () => {
  const { assignedCustomers } = useRole();
  const [selectedCustomer, setSelectedCustomer] = useCustomerContext();
  
  return (
    <select 
      value={selectedCustomer} 
      onChange={(e) => setSelectedCustomer(e.target.value)}
    >
      <option value="">Select Customer</option>
      {assignedCustomers.map(customer => (
        <option key={customer.id} value={customer.id}>
          {customer.name}
        </option>
      ))}
    </select>
  );
};
```

---

### US-TIR-005: Admin Dashboard Implementation
**As a** Developer  
**I want to** implement the admin dashboard with user management features  
**So that** admins can efficiently manage users and system settings

**Acceptance Criteria:**
- Create AdminDashboard page with system overview
- Implement UserManagement component with CRUD operations
- Add role assignment interface with bulk operations
- Create operator assignment management interface
- Implement audit log viewer with filtering
- Add system statistics and reporting widgets

**Technical Implementation:**
```typescript
// AdminDashboard component
export const AdminDashboard: React.FC = () => {
  const { data: stats } = useQuery('admin-stats', fetchAdminStats);
  const { data: recentActivity } = useQuery('recent-activity', fetchRecentActivity);
  
  return (
    <div className="admin-dashboard">
      <div className="stats-grid">
        <StatCard title="Total Users" value={stats?.totalUsers} />
        <StatCard title="Active Properties" value={stats?.activeProperties} />
        <StatCard title="Monthly Revenue" value={stats?.monthlyRevenue} />
        <StatCard title="System Health" value="Healthy" status="success" />
      </div>
      
      <div className="dashboard-sections">
        <UserManagementWidget />
        <RecentActivityWidget activities={recentActivity} />
        <SystemAlertsWidget />
      </div>
    </div>
  );
};

// UserManagement component with bulk operations
export const UserManagement: React.FC = () => {
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const { data: users, refetch } = useQuery('users', fetchAllUsers);
  const bulkUpdateRole = useMutation(bulkUpdateUserRoles);
  
  const handleBulkRoleChange = async (newRole: UserRole) => {
    await bulkUpdateRole.mutateAsync({
      userIds: selectedUsers,
      role: newRole
    });
    setSelectedUsers([]);
    refetch();
  };
  
  return (
    <div className="user-management">
      <div className="bulk-actions">
        {selectedUsers.length > 0 && (
          <BulkActionsBar
            selectedCount={selectedUsers.length}
            onRoleChange={handleBulkRoleChange}
            onPasswordReset={() => handleBulkPasswordReset(selectedUsers)}
          />
        )}
      </div>
      
      <Table
        data={users}
        columns={userTableColumns}
        selectable
        selectedItems={selectedUsers}
        onSelectionChange={setSelectedUsers}
      />
    </div>
  );
};
```

---

### US-TIR-006: Operator Context Management
**As a** Developer  
**I want to** implement customer context management for operators  
**So that** operators can seamlessly work with their assigned customers' data

**Acceptance Criteria:**
- Create CustomerContext provider for operators
- Implement customer switching functionality
- Add customer-scoped data fetching hooks
- Create breadcrumb navigation showing current customer
- Implement customer-specific permissions checking
- Add customer data isolation validation

**Technical Implementation:**
```typescript
// Customer context for operators
interface CustomerContextType {
  selectedCustomer: string | null;
  setSelectedCustomer: (customerId: string) => void;
  assignedCustomers: User[];
  customerPermissions: Permission[];
}

export const CustomerContext = createContext<CustomerContextType | null>(null);

export const CustomerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [selectedCustomer, setSelectedCustomer] = useState<string | null>(null);
  const { data: assignedCustomers } = useQuery(
    ['assigned-customers', user?.id],
    () => fetchAssignedCustomers(user?.id),
    { enabled: user?.role === 'OPERATOR' }
  );
  
  return (
    <CustomerContext.Provider value={{
      selectedCustomer,
      setSelectedCustomer,
      assignedCustomers: assignedCustomers || [],
      customerPermissions: getCustomerPermissions(selectedCustomer)
    }}>
      {children}
    </CustomerContext.Provider>
  );
};

// Customer-scoped data hooks
export const useCustomerProperties = () => {
  const { selectedCustomer } = useCustomerContext();
  const { role } = useRole();
  
  return useQuery(
    ['properties', selectedCustomer],
    () => fetchProperties({ customerId: selectedCustomer }),
    { 
      enabled: role === 'OPERATOR' && !!selectedCustomer 
    }
  );
};
```

---

### US-TIR-007: Audit Logging System
**As a** Developer  
**I want to** implement comprehensive audit logging  
**So that** all user actions are tracked for security and compliance

**Acceptance Criteria:**
- Create audit logging middleware for all API endpoints
- Implement client-side action tracking
- Add audit log storage with proper indexing
- Create audit log viewer with search and filtering
- Implement log retention and archival policies
- Add real-time audit alerts for sensitive operations

**Technical Implementation:**
```typescript
// Audit logging middleware
export const auditLogger = (action: string, resourceType?: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // Log successful operations
      if (res.statusCode < 400) {
        AuditLog.create({
          userId: req.user?.userId,
          action,
          resourceType,
          resourceId: req.params.id,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent'),
          requestData: req.body,
          responseData: data
        });
      }
      
      return originalSend.call(this, data);
    };
    
    next();
  };
};

// Usage in routes
router.patch('/users/:id/role', 
  authenticate, 
  requireRole(['ADMIN']), 
  auditLogger('ROLE_CHANGE', 'USER'),
  updateUserRole
);

// Audit log viewer component
export const AuditLogViewer: React.FC = () => {
  const [filters, setFilters] = useState({
    userId: '',
    action: '',
    dateRange: [null, null],
    resourceType: ''
  });
  
  const { data: logs } = useQuery(
    ['audit-logs', filters],
    () => fetchAuditLogs(filters)
  );
  
  return (
    <div className="audit-log-viewer">
      <AuditLogFilters filters={filters} onChange={setFilters} />
      <AuditLogTable logs={logs} />
    </div>
  );
};
```

---

## 🔄 Implementation Order

### Phase 1: Core Infrastructure
1. US-TIR-001: Database Schema for User Roles
2. US-TIR-002: Authentication Middleware Enhancement
3. US-TIR-004: Frontend Role-Based Components

### Phase 2: API and Data Layer
4. US-TIR-003: API Endpoint Modifications
5. US-TIR-006: Operator Context Management
6. US-TIR-007: Audit Logging System

### Phase 3: Admin Interface
7. US-TIR-005: Admin Dashboard Implementation

## 🧪 Testing Requirements

### Unit Tests
- Role-based middleware functions
- Permission checking logic
- Component rendering based on roles

### Integration Tests
- End-to-end role assignment workflows
- Data filtering by role and assignments
- Audit logging accuracy

### Security Tests
- Unauthorized access prevention
- Data isolation verification
- Token security and expiration

## 📊 Technical Metrics

- **Performance**: All role-based queries execute within 100ms
- **Security**: 100% of sensitive operations are logged
- **Reliability**: Zero data leakage between user roles
- **Maintainability**: Role system is extensible for future roles

# Short-Term Rentals User Stories

## Epic: Short-Term Rental Management
As a property owner, I want to manage short-term rentals (less than 1 year) so that I can track bookings, payments, and rental events for my properties.

---

## US-STR-001: Create Short-Term Rental
**As a** property owner  
**I want to** create a short-term rental  
**So that** I can manage bookings for periods less than one year

### Acceptance Criteria
- [X] I can create a new short-term rental with:
  - Property selection (from my properties)
  - Start date (required)
  - End date (required, must be after start date)
  - Rental amount (required, numeric)
  - Cost amount (auto-calculated from property services, editable)
- [X] I can add guests to the rental:
  - Search and select existing guests
  - Create new guests inline
  - Designate one guest as principal contact
- [X] I can set rental status (active/ended/cancelled)
- [X] I can mark if rental has been paid
- [X] System validates that end date is within 30 days of start date
- [X] I receive confirmation when rental is created

### Technical Notes
- Auto-calculate cost from active property services
- Validate date ranges don't overlap with existing rentals for same property
- Generate unique rental reference number
- Validate a rental must have at least one guest


---

## US-STR-002: Manage Rental Payments
**As a** property owner  
**I want to** track rental payments  
**So that** I can monitor payment status and cash flow

### Acceptance Criteria
- [ ] I can mark a rental as paid/unpaid
- [ ] I can see payment status clearly in rental lists
- [ ] I can add payment date when marking as paid
- [ ] I can add payment method and reference
- [ ] I can see overdue rentals (past end date and unpaid)
- [ ] I can generate payment reminders for unpaid rentals
- [ ] Payment history is tracked with timestamps

### Technical Notes
- Store payment date, method, and reference
- Implement payment status indicators in UI
- Consider integration with payment processors

---

## US-STR-003: Apply Discounts
**As a** property owner  
**I want to** apply discounts to rental profit  
**So that** I can offer competitive pricing while tracking actual profit margins

### Acceptance Criteria
- [ ] I can add a discount amount to a rental
- [ ] Discount is applied only to profit (rental amount - cost amount)
- [ ] I can see original profit, discount amount, and final profit
- [ ] Discount cannot exceed the profit amount
- [ ] I can add a reason for the discount
- [ ] Discount information is saved with the rental

### Technical Notes
- Profit = Rental Amount - Cost Amount
- Final Profit = Profit - Discount Amount
- Validate discount ≤ profit amount

---

## US-STR-004: Manage Commissions
**As a** property owner  
**I want to** track commissions for rentals  
**So that** I can manage payments to partners and service providers

### Acceptance Criteria
- [ ] I can add multiple commissions to a rental
- [ ] Each commission includes:
  - Commission amount or percentage
  - Commission type (percentage/fixed amount)
  - Commission receiver (user/partner)
  - Commission status (pending/paid)
- [ ] I can calculate percentage commissions from rental amount
- [ ] I can mark commissions as paid with payment date
- [ ] I can see total commission amounts for a rental
- [ ] I can filter rentals by commission status

### Technical Notes
- Support both fixed amount and percentage-based commissions
- Calculate percentage commissions dynamically
- Track commission payment history

---

## US-STR-005: View Rental Calendar
**As a** property owner  
**I want to** view rentals in a calendar format  
**So that** I can see property availability and booking patterns

### Acceptance Criteria
- [X] I can view rentals in monthly calendar view
- [X] Each rental shows property name, guest name, and dates
- [X] Different colors indicate rental status (active/ended/cancelled)
- [X] I can click on a rental to view details
- [X] I can navigate between months
- [X] I can filter by property
- [X] I can see availability gaps between rentals

### Technical Notes
- Implement calendar component with rental overlays
- Use color coding for different rental statuses
- Support month navigation and property filtering

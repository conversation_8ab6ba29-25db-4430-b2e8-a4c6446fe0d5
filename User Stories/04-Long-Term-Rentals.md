# Long-Term Rentals User Stories

## Epic: Long-Term Rental Management
As a property owner, I want to manage long-term rentals (1+ years) so that I can track ongoing tenancies, monthly payments, and rental events for my properties.

---

## US-LTR-001: Create Long-Term Rental
**As a** property owner  
**I want to** create a long-term rental  
**So that** I can manage tenancies for periods of one year or more

### Acceptance Criteria
- [ ] I can create a new long-term rental with:
  - Property selection (from my properties)
  - Start date (required)
  - End date (required, must be at least 1 year after start date)
  - Monthly rental amount (required, numeric)
  - Monthly cost amount (auto-calculated from property services, editable)
- [ ] I can add tenants to the rental:
  - Search and select existing guests
  - Create new guests inline
  - Designate one guest as principal contact
- [ ] I can set rental status (active/ended/cancelled)
- [ ] System validates that end date is at least 1 year from start date
- [ ] I receive confirmation when rental is created

### Technical Notes
- Auto-calculate monthly cost from active property services
- Validate date ranges don't overlap with existing rentals for same property
- Generate unique rental reference number
- Consider lease renewal workflows

---

## US-LTR-002: Manage Monthly Payments
**As a** property owner  
**I want to** track monthly rental payments  
**So that** I can monitor payment status and identify late payments

### Acceptance Criteria
- [ ] System automatically generates monthly payment records for the rental period
- [ ] I can mark individual months as paid/unpaid
- [ ] I can see payment status for each month in the rental period
- [ ] I can add payment date and method when marking as paid
- [ ] I can see overdue payments (past due date and unpaid)
- [ ] I can generate payment reminders for late payments
- [ ] I can see payment history with timestamps

### Technical Notes
- Auto-generate monthly payment records on rental creation
- Implement payment due date calculation (e.g., 5th of each month)
- Track payment status per month
- Consider automated payment reminder system

---

## US-LTR-003: Apply Monthly Discounts
**As a** property owner  
**I want to** apply discounts to monthly rental profit  
**So that** I can offer competitive pricing while tracking actual profit margins

### Acceptance Criteria
- [ ] I can add a monthly discount amount to a rental
- [ ] Discount is applied only to profit (monthly rental - monthly cost)
- [ ] I can see original monthly profit, discount amount, and final monthly profit
- [ ] Discount cannot exceed the monthly profit amount
- [ ] I can add a reason for the discount
- [ ] Discount applies to all months in the rental period
- [ ] I can modify discount amount during the rental period

### Technical Notes
- Monthly Profit = Monthly Rental Amount - Monthly Cost Amount
- Final Monthly Profit = Monthly Profit - Monthly Discount Amount
- Validate discount ≤ monthly profit amount
- Consider different discount types (fixed, percentage, temporary)

---

## US-LTR-004: Manage Monthly Commissions
**As a** property owner  
**I want to** track monthly commissions for long-term rentals  
**So that** I can manage ongoing payments to partners and service providers

### Acceptance Criteria
- [ ] I can add monthly commissions to a rental
- [ ] Each commission includes:
  - Commission amount or percentage
  - Commission type (percentage/fixed amount)
  - Commission receiver (user/partner)
  - Commission frequency (monthly/quarterly/yearly)
- [ ] I can calculate percentage commissions from monthly rental amount
- [ ] I can mark monthly commissions as paid with payment date
- [ ] I can see total commission amounts per month
- [ ] I can see commission payment history

### Technical Notes
- Support both fixed amount and percentage-based commissions
- Calculate percentage commissions dynamically
- Handle different commission frequencies
- Track commission payment history per month

---

## US-LTR-005: Rental Events and History
**As a** property owner  
**I want to** track events during long-term rentals  
**So that** I can maintain a complete history of the tenancy

### Acceptance Criteria
- [ ] I can add events to a rental with:
  - Event date
  - Event type (maintenance, inspection, complaint, etc.)
  - Event description
  - Associated cost (optional)
- [ ] I can view chronological event history for a rental
- [ ] I can filter events by type and date range
- [ ] I can attach documents or photos to events
- [ ] Events are automatically created for payment activities
- [ ] I can export event history for reporting

### Technical Notes
- Implement event logging system
- Support file attachments for events
- Auto-generate events for payments and status changes
- Consider event categories and templates

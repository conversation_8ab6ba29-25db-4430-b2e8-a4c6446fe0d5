# Guest Management User Stories

## Epic: Guest Information Management
As a property owner, I want to manage guest information so that I can track who is renting my properties and maintain proper records for legal and administrative purposes.

---

## US-GM-001: Create Guest Profile
**As a** property owner  
**I want to** create guest profiles  
**So that** I can store guest information for rentals and reuse it for future bookings

### Acceptance Criteria
- [X] I can create a new guest with the following required information:
  - First name and last name
  - Date of birth
  - Email address
  - Document type (National ID, Visa, Passport, Foreigner ID)
  - Document number
- [X] I can add optional information:
  - Phone number
  - Relationship with owner (default: "Guest")
- [X] I can mark a guest as foreigner and add:
  - Nationality
  - Country of origin
  - Next destination
- [X] I can add local guest information (when not foreigner):
  - City of residency
  - City of birth
  - Trip motivation
- [X] Form validation ensures all required fields are completed
- [X] Email and document number must be unique in the system
- [X] I receive confirmation when guest is successfully created

### Technical Notes
- I can add files to guest profile as supporting documents
- Implement email format validation
- Document number should be alphanumeric
- Date of birth should validate age (minimum 15 years)

---

## US-GM-002: Search and Select Existing Guests
**As a** property owner  
**I want to** search for existing guests  
**So that** I can reuse guest information for new rentals without duplicating data

### Acceptance Criteria
- [X] I can search guests by name, email, or document number
- [X] Search results show guest name, email, and document type
- [X] I can select a guest from search results
- [X] I can view full guest details before selecting
- [X] Search is case-insensitive and supports partial matches

### Technical Notes
- Implement debounced search for performance
- Return maximum 20 results per search
- Include guest ID in search results for selection

---

## US-GM-003: View Guest Details
**As a** property owner  
**I want to** view complete guest information  
**So that** I can verify guest details and see their rental history

### Acceptance Criteria
- [X] I can view all guest information in a detailed view
- [X] I can see guest's contact information and documents
- [X] Information is displayed in a clear, organized format
- [X] I can see all supporting documents uploaded for the guest

---

## US-GM-004: Edit Guest Information
**As a** property owner  
**I want to** edit guest information  
**So that** I can keep guest records up to date

### Acceptance Criteria
- [X] I can edit all guest fields except creation date
- [X] Changes are validated before saving
- [X] I can switch between foreigner and local guest types
- [X] I can add/remove supporting documents
- [X] Relevant fields show/hide based on guest type
- [X] I receive confirmation when changes are saved
- [X] Updated information is reflected in all associated rentals

### Technical Notes
- Implement field-level validation
- Consider audit trail for sensitive changes
- Update rental records if guest information changes
- Limit file size up to 5mb and allow pdf, word and image file types

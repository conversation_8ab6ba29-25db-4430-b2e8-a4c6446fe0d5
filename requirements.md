# Requirements for Alom Rentals Application

## Rentals

We already support creation of properties and partners, which allow us to manage the properties of the user, now we need to allow them to manage their rentals, expenses and incomes.
For this goal we need to add Rentals module that will allow users to:

- Create short and long rentals for their properties, a rental will have:

  - A short rental will be for a specific period of time, and a long rental will be for more than 1 year time.
  - A historycal of changes to the rental, for example if the rental amount is changed, or the start date is changed, etc.
  - Both short and long rentals will share this information:

    - A property
    - A cost amount calculated by the sum of property services at the moment of rent and can be editable by the user.
    - A start date
    - An end date
    - A status (active, ended, cancelled)
    - A flag to indicate if the rental has been payed
    - A comission list that will have the following information:
      - A comission amount
      - A comission type (percentage, fixed amount)
      - A comission status (pending, paid)
      - A comission receiver (user or partner)
    - List of guesses with a guess that will be the principal contact, a guess will have this information:
      - Name and last name
      - Date of birth
      - Email
      - Phone number is optional
      - Relationship with owner by default Guess
      - Flag to indicate if the guess is the principal contact
      - Document Type (National ID, Visa, Passport, Foreigner ID)
      - Document number
      - Flag to indicate if the guess is foreigner, if yes guess will have this additional information:
        - Nationality
        - Country of origin
        - Next destination
      - if foreigner flag is not set, guess will have this additional information:
        - City of Residency
        - City of Birth
        - Trip motivation
    - Every guess has to be saved in the database as a guess, and the rental will have a list of guesses that belong to it. So, if the same guess rent again a property we don't need to create a new guess, instead we will add the guess to the rental.
    - A discount amount that should applied only to the profit of the rental. Profit is cost of rental amount - cost of the rental.
    - An array of events that will add information to the rental for each required step, the events will have:
      - A date
      - A description
      - A type event
      - A user that created the event
    - By default we want to support these types of events:
      - Contract send
      - Contract signed
      - Deposit requested
      - Deposit paid
      - Bulding administration report done
      - Goverment registration done
      - Key code delivered
      - Check-in date
      - Check-out date
      - Cleaning done
      - Damage report
      - Follow up email done
      - Comissions paid

  - A short rental will required below information:
    - A rental amount
    - A flag to indicate if the rental has been payed
  - A long rental will required below information:
    - A recurrent monthly amount
    - A list of payments to track monthly payments

- We need to modify the property model to add a list of services that the property has, a service will have:
  - A name
  - A cost
  - A frequency (monthly, quarterly, yearly)
  - A status (active, inactive)
  - A flag to indicate if the service is mandatory
  - A flag to indicate if the service is paid by the user
  - A flag to indicate if the service is paid by the partner

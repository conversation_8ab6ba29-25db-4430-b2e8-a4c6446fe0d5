# Alom Rentals - Monorepo

A property finance management application built with a modern monorepo architecture.

## 🏗️ Architecture

This project uses a **Yarn Workspaces** monorepo structure with three main packages:

```
project/
├── packages/
│   ├── shared/          # Shared types and schemas
│   ├── backend/         # Hono API server
│   └── frontend/        # React application
├── package.json         # Root workspace configuration
└── README.md
```

## 📦 Packages

### `@alom-rentals/shared`

- **Purpose**: Shared TypeScript types and Zod schemas
- **Exports**: User, Partner, Property types and validation schemas
- **Benefits**: Ensures type consistency between frontend and backend

### `@alom-rentals/backend`

- **Tech Stack**: Hono, TypeScript, MongoDB, Mongoose
- **Purpose**: REST API server with authentication and data management
- **Features**: JWT auth, property management, partner management

### `@alom-rentals/frontend`

- **Tech Stack**: React, TypeScript, Vite, TailwindCSS
- **Purpose**: Web application for property finance management
- **Features**: Property creation, owner management, partner management

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- Yarn 1.22+
- MongoDB

### Installation

1. **Clone and install dependencies:**

```bash
git clone <repository>
cd project
yarn install
```

2. **Build shared package:**

```bash
yarn build:shared
```

3. **Set up environment variables:**

```bash
cp .env.example .env
# Edit .env with your MongoDB connection string and JWT secret
```

4. **Start development servers:**

```bash
yarn dev
```

This will start both the backend API (port 3001) and frontend (port 5173) concurrently.

## 🎨 Code Style & Development Workflow

This project uses consistent code formatting and commit conventions across all packages.

### Code Formatting

- **Prettier** is configured at the root level for consistent code formatting
- **ESLint** is configured for the frontend package for code quality
- **Husky** pre-commit hooks automatically format code before commits
- **lint-staged** ensures only staged files are processed

### Commit Conventions

This project follows [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Types:**
- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `build`: Changes that affect the build system or external dependencies
- `ci`: Changes to CI configuration files and scripts
- `chore`: Other changes that don't modify src or test files

**Examples:**
```bash
git commit -m "feat: add property creation form"
git commit -m "fix: resolve authentication token expiry issue"
git commit -m "docs: update API documentation"
```

### Pre-commit Hooks

The following checks run automatically before each commit:
1. **Prettier** formats all staged files
2. **ESLint** checks and fixes frontend code
3. **Commitlint** validates commit message format

### Manual Formatting

```bash
# Format all files
yarn format

# Check formatting without making changes
yarn format:check

# Run linting
yarn lint
```

## 📝 Available Scripts

### Root Level

- `yarn dev` - Start both backend and frontend in development mode
- `yarn build` - Build all packages
- `yarn clean` - Clean all build artifacts
- `yarn format` - Format all code using Prettier
- `yarn format:check` - Check if code is formatted correctly
- `yarn lint` - Run ESLint on frontend code
- `yarn test` - Run tests for all packages
- `yarn test:coverage` - Run tests with coverage for all packages

### Package-Specific

- `yarn dev:shared` - Watch mode for shared package
- `yarn dev:backend` - Start backend development server
- `yarn dev:frontend` - Start frontend development server
- `yarn build:shared` - Build shared package
- `yarn build:backend` - Build backend package
- `yarn build:frontend` - Build frontend package
- `yarn test:backend` - Run backend tests
- `yarn test:frontend` - Run frontend tests

## 🔧 Development Workflow

1. **Adding new types**: Add to `packages/shared/src/types/`
2. **Adding new schemas**: Add to `packages/shared/src/schemas/`
3. **After shared changes**: Run `yarn build:shared`
4. **Backend changes**: Work in `packages/backend/`
5. **Frontend changes**: Work in `packages/frontend/`

## 🎯 Benefits of This Architecture

### ✅ **Type Safety**

- Shared types ensure consistency between frontend and backend
- No more API contract mismatches
- Compile-time error detection

### ✅ **Code Reusability**

- Validation schemas used in both frontend and backend
- Single source of truth for data structures
- Reduced code duplication

### ✅ **Maintainability**

- Clear separation of concerns
- Independent package versioning
- Easier testing and deployment

### ✅ **Developer Experience**

- IntelliSense and autocomplete across packages
- Refactoring safety with TypeScript
- Hot reload for all packages

## 🔄 Migration Benefits

**Before**: Separate codebases with potential type mismatches
**After**: Unified monorepo with shared types and schemas

This ensures that when you add a new field to a model, both the frontend and backend automatically get the updated types, preventing runtime errors and improving development velocity.

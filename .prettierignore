# Dependencies
node_modules/
**/node_modules/

# Build outputs
dist/
**/dist/
build/
**/build/

# Coverage reports
coverage/
**/coverage/
**/coverage/**

# Logs
*.log
logs/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Package manager files
yarn.lock
package-lock.json
pnpm-lock.yaml

# Generated files
*.d.ts.map
*.js.map

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Docker files
Dockerfile
docker-compose.yml

# Markdown files (optional - remove if you want to format markdown)
*.md

# Config files that should maintain their formatting
.gitignore
.dockerignore

name: E2E Tests

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]

jobs:
  e2e-tests:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: alom_rentals_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: |
          packages/frontend/package-lock.json
          packages/backend/package-lock.json
          packages/shared/package-lock.json
    
    - name: Install dependencies
      run: |
        npm ci
        cd packages/shared && npm ci
        cd ../backend && npm ci
        cd ../frontend && npm ci
    
    - name: Build shared package
      run: |
        cd packages/shared
        npm run build
    
    - name: Setup test database
      run: |
        cd packages/backend
        npm run db:migrate
        npm run db:seed:test
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/alom_rentals_test
        NODE_ENV: test
    
    - name: Install Playwright Browsers
      run: |
        cd packages/frontend
        npx playwright install --with-deps
    
    - name: Start backend server
      run: |
        cd packages/backend
        npm run dev &
        sleep 10
      env:
        DATABASE_URL: postgresql://postgres:postgres@localhost:5432/alom_rentals_test
        NODE_ENV: test
        PORT: 3000
    
    - name: Start frontend server
      run: |
        cd packages/frontend
        npm run dev &
        sleep 10
      env:
        VITE_API_URL: http://localhost:3000
    
    - name: Run Playwright tests
      run: |
        cd packages/frontend
        npm run test:e2e
      env:
        CI: true
    
    - name: Upload Playwright Report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: packages/frontend/playwright-report/
        retention-days: 30
    
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: packages/frontend/test-results/
        retention-days: 30

{"name": "alom-rentals-monorepo", "private": true, "version": "0.0.0", "workspaces": ["packages/*"], "scripts": {"dev": "concurrently \"yarn dev:backend\" \"yarn dev:frontend\"", "dev:shared": "yarn workspace @alom-rentals/shared dev", "dev:backend": "yarn workspace @alom-rentals/backend dev", "dev:frontend": "yarn workspace @alom-rentals/frontend dev", "build": "yarn build:shared & yarn build:backend & yarn build:frontend", "build:shared": "yarn workspace @alom-rentals/shared build", "build:backend": "yarn workspace @alom-rentals/backend build", "build:frontend": "yarn workspace @alom-rentals/frontend build", "test": "concurrently  \"yarn test:backend\" \"yarn test:frontend\"", "test:backend": "yarn workspace @alom-rentals/backend test", "test:frontend": "yarn workspace @alom-rentals/frontend test", "test:coverage": "concurrently  \"yarn test:backend:coverage\" \"yarn test:frontend:coverage\"", "test:backend:coverage": "yarn workspace @alom-rentals/backend test:coverage", "test:frontend:coverage": "yarn workspace @alom-rentals/frontend test:coverage", "clean": "yarn workspaces foreach -Apt run clean", "format": "prettier --write .", "format:check": "prettier --check .", "lint": "yarn lint:frontend", "lint:frontend": "yarn workspace @alom-rentals/frontend lint", "lint-staged": "lint-staged", "prepare": "husky", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:restart": "docker-compose restart", "docker:clean": "docker-compose down -v --remove-orphans"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "concurrently": "^9.1.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --write"], "packages/frontend/**/*.{js,jsx,ts,tsx}": ["yarn workspace @alom-rentals/frontend lint --fix"]}}